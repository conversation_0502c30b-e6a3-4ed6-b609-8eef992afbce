<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">活动详情</text>
      <uni-icons class="share-icon" type="redo" size="20" color="#333333" @click="shareActivity"></uni-icons>
    </view>

    <scroll-view class="content-scroll" scroll-y>
      <!-- 活动头图 -->
      <view class="activity-header">
        <image class="header-image" :src="activity.image || defaultImage" mode="aspectFill"></image>
        <view class="header-overlay">
          <view class="status-badge" :class="getStatusClass(activity.status)">
            {{ getStatusText(activity.status) }}
          </view>
        </view>
      </view>

      <!-- 活动信息 -->
      <view class="activity-info">
        <text class="activity-title">{{ activity.name }}</text>
        <text class="activity-subtitle">{{ activity.subtitle }}</text>
        
        <view class="info-grid">
          <view class="info-item">
            <uni-icons type="calendar" size="16" color="#666666"></uni-icons>
            <text class="info-text">{{ formatDateRange(activity.startTime, activity.endTime) }}</text>
          </view>
          <view class="info-item">
            <uni-icons type="person" size="16" color="#666666"></uni-icons>
            <text class="info-text">{{ activity.participantCount || 0 }}人参与</text>
          </view>
          <view class="info-item">
            <uni-icons type="location" size="16" color="#666666"></uni-icons>
            <text class="info-text">{{ activity.location || '线上活动' }}</text>
          </view>
        </view>
      </view>

      <!-- 活动描述 -->
      <view class="activity-description">
        <view class="section-title">活动详情</view>
        <rich-text class="description-content" :nodes="activity.content"></rich-text>
      </view>

      <!-- 活动奖励 -->
      <view v-if="activity.rewards && activity.rewards.length > 0" class="activity-rewards">
        <view class="section-title">活动奖励</view>
        <view class="rewards-list">
          <view v-for="reward in activity.rewards" :key="reward.id" class="reward-item">
            <uni-icons type="medal" size="20" color="#ff6b35"></uni-icons>
            <view class="reward-content">
              <text class="reward-title">{{ reward.title }}</text>
              <text class="reward-desc">{{ reward.description }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 参与规则 -->
      <view class="activity-rules">
        <view class="section-title">参与规则</view>
        <view class="rules-list">
          <text v-for="(rule, index) in activity.rules" :key="index" class="rule-item">
            {{ index + 1 }}. {{ rule }}
          </text>
        </view>
      </view>

      <!-- 邀请功能 -->
      <view v-if="activity.type === 1" class="invite-section">
        <view class="section-title">邀请好友</view>
        <view class="invite-card">
          <view class="invite-info">
            <text class="invite-title">邀请好友参与活动</text>
            <text class="invite-desc">每邀请一位好友可获得额外奖励</text>
          </view>
          <view class="invite-actions">
            <view class="invite-btn" @click="generateInviteCode">
              <uni-icons type="gift" size="16" color="#ffffff"></uni-icons>
              <text class="btn-text">生成邀请码</text>
            </view>
            <view class="my-invites-btn" @click="viewMyInvites">
              <text class="btn-text">我的邀请</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="participant-info">
        <text class="participant-text">{{ activity.participantCount || 0 }}人已参与</text>
      </view>
      <view v-if="activity.status === 1" class="join-btn" @click="joinActivity">
        <text class="join-text">立即参与</text>
      </view>
      <view v-else-if="activity.status === 0" class="wait-btn">
        <text class="wait-text">敬请期待</text>
      </view>
      <view v-else class="ended-btn">
        <text class="ended-text">活动已结束</text>
      </view>
    </view>

    <!-- 邀请码弹窗 -->
    <uni-popup ref="invitePopup" type="center">
      <view class="invite-popup">
        <view class="popup-header">
          <text class="popup-title">我的邀请码</text>
          <uni-icons class="close-icon" type="close" size="20" color="#666666" @click="closeInvitePopup"></uni-icons>
        </view>
        <view class="invite-code-content">
          <text class="invite-code">{{ inviteCode }}</text>
          <view class="code-actions">
            <view class="copy-btn" @click="copyInviteCode">
              <uni-icons type="copy" size="16" color="#ffffff"></uni-icons>
              <text class="copy-text">复制邀请码</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { api } from '@/api/index.js'
import { config } from '@/config/index.js'

export default {
  data() {
    return {
      activityId: null,
      activity: {
        name: '',
        subtitle: '',
        image: '',
        content: '',
        status: 0,
        type: 1,
        startTime: '',
        endTime: '',
        location: '',
        participantCount: 0,
        rewards: [],
        rules: []
      },
      inviteCode: '',
      defaultImage: config.defaultActivityImage || 'https://via.placeholder.com/400x200'
    }
  },

  onLoad(options) {
    if (options.id) {
      this.activityId = parseInt(options.id)
      this.loadActivityDetail()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadActivityDetail() {
      try {
        const res = await api.get(`/activities/${this.activityId}`)
        if (res) {
          this.activity = res
          
          // 设置默认规则
          if (!this.activity.rules || this.activity.rules.length === 0) {
            this.activity.rules = [
              '活动期间内完成指定任务即可获得奖励',
              '每个用户仅可参与一次',
              '活动最终解释权归平台所有'
            ]
          }
        }
      } catch (error) {
        console.error('获取活动详情失败:', error)
        uni.showToast({
          title: '获取活动详情失败',
          icon: 'none'
        })
      }
    },

    async joinActivity() {
      try {
        uni.showLoading({ title: '参与中...' })
        
        const res = await api.post(`/activities/${this.activityId}/join`)
        
        if (res) {
          uni.showToast({
            title: res.message || '参与成功',
            icon: 'success'
          })
          
          // 更新参与人数
          this.activity.participantCount = (this.activity.participantCount || 0) + 1
        }
      } catch (error) {
        console.error('参与活动失败:', error)
        uni.showToast({
          title: '参与失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    async generateInviteCode() {
      try {
        uni.showLoading({ title: '生成中...' })
        
        const res = await api.post('/activities/invite', {
          activityId: this.activityId
        })
        
        if (res) {
          this.inviteCode = res
          this.$refs.invitePopup.open()
        }
      } catch (error) {
        console.error('生成邀请码失败:', error)
        uni.showToast({
          title: '生成邀请码失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    closeInvitePopup() {
      this.$refs.invitePopup.close()
    },

    copyInviteCode() {
      uni.setClipboardData({
        data: this.inviteCode,
        success: () => {
          uni.showToast({
            title: '邀请码已复制',
            icon: 'success'
          })
          this.closeInvitePopup()
        }
      })
    },

    viewMyInvites() {
      uni.navigateTo({
        url: `/pages/activity/invites?activityId=${this.activityId}`
      })
    },

    shareActivity() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `pages/activity/detail?id=${this.activityId}`,
        title: this.activity.name,
        summary: this.activity.subtitle,
        imageUrl: this.activity.image
      })
    },

    getStatusClass(status) {
      switch (status) {
        case 0: return 'pending'
        case 1: return 'active'
        case 2: return 'ended'
        default: return 'pending'
      }
    },

    getStatusText(status) {
      switch (status) {
        case 0: return '未开始'
        case 1: return '进行中'
        case 2: return '已结束'
        default: return '未知'
      }
    },

    formatDateRange(startTime, endTime) {
      if (!startTime) return ''
      
      const start = new Date(startTime)
      const end = endTime ? new Date(endTime) : null
      
      const startStr = `${start.getMonth() + 1}月${start.getDate()}日`
      
      if (end) {
        const endStr = `${end.getMonth() + 1}月${end.getDate()}日`
        return `${startStr} - ${endStr}`
      }
      
      return startStr
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon, .share-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 内容滚动 */
.content-scroll {
  height: calc(100vh - 200rpx);
}

/* 活动头图 */
.activity-header {
  position: relative;
  height: 400rpx;
}

.header-image {
  width: 100%;
  height: 100%;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 30rpx;
}

.status-badge {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
}

.status-badge.active {
  background-color: #52c41a;
}

.status-badge.pending {
  background-color: #faad14;
}

.status-badge.ended {
  background-color: #999999;
}

/* 活动信息 */
.activity-info {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.activity-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.activity-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 30rpx;
  display: block;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-text {
  font-size: 26rpx;
  color: #666666;
}

/* 通用区块样式 */
.activity-description,
.activity-rewards,
.activity-rules,
.invite-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
  display: block;
}

/* 活动描述 */
.description-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666666;
}

/* 活动奖励 */
.rewards-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.reward-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  background-color: #fff5f0;
  border-radius: 12rpx;
}

.reward-content {
  flex: 1;
}

.reward-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b35;
  margin-bottom: 8rpx;
  display: block;
}

.reward-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

/* 参与规则 */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.rule-item {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  display: block;
}

/* 邀请功能 */
.invite-card {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
}

.invite-info {
  margin-bottom: 24rpx;
}

.invite-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.invite-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

.invite-actions {
  display: flex;
  gap: 20rpx;
}

.invite-btn, .my-invites-btn {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.invite-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.my-invites-btn {
  background-color: #ffffff;
  border: 2rpx solid #ff6b35;
}

.invite-btn .btn-text {
  color: #ffffff;
}

.my-invites-btn .btn-text {
  color: #ff6b35;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 600;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.participant-info {
  flex: 1;
}

.participant-text {
  font-size: 26rpx;
  color: #666666;
}

.join-btn, .wait-btn, .ended-btn {
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  min-width: 200rpx;
  text-align: center;
}

.join-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.wait-btn {
  background-color: #faad14;
}

.ended-btn {
  background-color: #d9d9d9;
}

.join-text, .wait-text, .ended-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 邀请码弹窗 */
.invite-popup {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 600rpx;
  padding: 40rpx;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-icon {
  padding: 10rpx;
}

.invite-code-content {
  text-align: center;
}

.invite-code {
  font-size: 48rpx;
  font-weight: 600;
  color: #ff6b35;
  letter-spacing: 4rpx;
  margin-bottom: 30rpx;
  display: block;
}

.code-actions {
  display: flex;
  justify-content: center;
}

.copy-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.copy-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
}
</style>
