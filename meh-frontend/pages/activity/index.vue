<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">活动中心</text>
    </view>

    <!-- 活动分类 -->
    <view class="category-tabs">
      <view 
        v-for="(category, index) in categories" 
        :key="index"
        class="tab-item"
        :class="{ active: activeCategory === category.value }"
        @click="switchCategory(category.value)"
      >
        <text class="tab-text">{{ category.label }}</text>
      </view>
    </view>

    <!-- 活动列表 -->
    <scroll-view class="activity-list" scroll-y @scrolltolower="loadMore">
      <view v-if="activities.length === 0 && !loading" class="empty-state">
        <uni-icons type="info" size="60" color="#cccccc"></uni-icons>
        <text class="empty-text">暂无活动</text>
      </view>

      <view v-for="activity in activities" :key="activity.id" class="activity-card" @click="goToActivityDetail(activity)">
        <view class="activity-image">
          <image class="image" :src="activity.image || defaultImage" mode="aspectFill"></image>
          <view v-if="activity.status === 1" class="status-badge active">进行中</view>
          <view v-else-if="activity.status === 0" class="status-badge pending">未开始</view>
          <view v-else class="status-badge ended">已结束</view>
        </view>
        
        <view class="activity-content">
          <text class="activity-title">{{ activity.name }}</text>
          <text class="activity-desc">{{ activity.description }}</text>
          
          <view class="activity-meta">
            <view class="meta-item">
              <uni-icons type="calendar" size="14" color="#666666"></uni-icons>
              <text class="meta-text">{{ formatDate(activity.startTime) }}</text>
            </view>
            <view class="meta-item">
              <uni-icons type="person" size="14" color="#666666"></uni-icons>
              <text class="meta-text">{{ activity.participantCount || 0 }}人参与</text>
            </view>
          </view>

          <view class="activity-rewards" v-if="activity.rewards">
            <view class="reward-item" v-for="reward in activity.rewards" :key="reward.type">
              <uni-icons type="medal" size="14" color="#ff6b35"></uni-icons>
              <text class="reward-text">{{ reward.description }}</text>
            </view>
          </view>
        </view>

        <view class="activity-action">
          <view v-if="activity.status === 1" class="join-btn" @click.stop="joinActivity(activity)">
            <text class="btn-text">立即参与</text>
          </view>
          <view v-else-if="activity.status === 0" class="wait-btn">
            <text class="btn-text">敬请期待</text>
          </view>
          <view v-else class="ended-btn">
            <text class="btn-text">已结束</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="loading-more">
        <uni-icons type="spinner-cycle" size="20" color="#999999"></uni-icons>
        <text class="loading-text">加载中...</text>
      </view>
    </scroll-view>

    <!-- 我的邀请按钮 -->
    <view class="float-btn" @click="goToMyInvites">
      <uni-icons type="gift" size="24" color="#ffffff"></uni-icons>
      <text class="float-text">我的邀请</text>
    </view>
  </view>
</template>

<script>
import { api } from '@/api/index.js'
import { config } from '@/config/index.js'

export default {
  data() {
    return {
      activeCategory: 0,
      categories: [
        { label: '全部', value: 0 },
        { label: '裂变营销', value: 1 },
        { label: '签到活动', value: 2 },
        { label: '其他活动', value: 3 }
      ],
      activities: [],
      loading: false,
      page: 1,
      hasMore: true,
      defaultImage: config.defaultActivityImage || 'https://via.placeholder.com/300x200'
    }
  },

  onLoad() {
    this.loadActivities()
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    switchCategory(category) {
      this.activeCategory = category
      this.page = 1
      this.activities = []
      this.hasMore = true
      this.loadActivities()
    },

    async loadActivities() {
      if (this.loading || !this.hasMore) return

      this.loading = true
      try {
        const res = await api.get('/activities', {
          type: this.activeCategory || undefined,
          page: this.page,
          size: 10
        })

        if (res && Array.isArray(res)) {
          if (this.page === 1) {
            this.activities = res
          } else {
            this.activities.push(...res)
          }
          
          this.hasMore = res.length === 10
          this.page++
        }
      } catch (error) {
        console.error('获取活动列表失败:', error)
        uni.showToast({
          title: '获取活动失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    loadMore() {
      this.loadActivities()
    },

    async joinActivity(activity) {
      try {
        uni.showLoading({ title: '参与中...' })
        
        const res = await api.post(`/activities/${activity.id}/join`)
        
        if (res) {
          uni.showToast({
            title: res.message || '参与成功',
            icon: 'success'
          })
          
          // 更新参与人数
          activity.participantCount = (activity.participantCount || 0) + 1
        }
      } catch (error) {
        console.error('参与活动失败:', error)
        uni.showToast({
          title: '参与失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    goToActivityDetail(activity) {
      uni.navigateTo({
        url: `/pages/activity/detail?id=${activity.id}`
      })
    },

    goToMyInvites() {
      uni.navigateTo({
        url: '/pages/activity/invites'
      })
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return `${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 分类标签 */
.category-tabs {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  gap: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #ff6b35;
}

.tab-text {
  font-size: 28rpx;
  color: #666666;
}

.tab-item.active .tab-text {
  color: #ffffff;
}

/* 活动列表 */
.activity-list {
  height: calc(100vh - 200rpx);
  padding: 20rpx;
}

.activity-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.activity-image {
  position: relative;
  height: 200rpx;
}

.image {
  width: 100%;
  height: 100%;
}

.status-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #ffffff;
}

.status-badge.active {
  background-color: #52c41a;
}

.status-badge.pending {
  background-color: #faad14;
}

.status-badge.ended {
  background-color: #999999;
}

.activity-content {
  padding: 30rpx;
}

.activity-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  display: block;
}

.activity-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: block;
}

.activity-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #666666;
}

.activity-rewards {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.reward-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.reward-text {
  font-size: 24rpx;
  color: #ff6b35;
}

.activity-action {
  padding: 0 30rpx 30rpx;
}

.join-btn, .wait-btn, .ended-btn {
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
}

.join-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.wait-btn {
  background-color: #faad14;
}

.ended-btn {
  background-color: #d9d9d9;
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
  display: block;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999999;
}

/* 浮动按钮 */
.float-btn {
  position: fixed;
  bottom: 100rpx;
  right: 30rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.3);
}

.float-text {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 600;
}
</style>
