<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">设置</text>
    </view>

    <!-- 用户信息 -->
    <view class="user-section">
      <view class="user-card">
        <view class="user-avatar">
          <image class="avatar-image" :src="userInfo.avatar || defaultAvatar" mode="aspectFill"></image>
        </view>
        <view class="user-info">
          <text class="user-name">{{ userInfo.nickname || '未设置昵称' }}</text>
          <text class="user-phone">{{ formatPhone(userInfo.phone) }}</text>
        </view>
        <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc" @click="editProfile"></uni-icons>
      </view>
    </view>

    <!-- 设置列表 -->
    <view class="settings-section">
      <!-- 账户设置 -->
      <view class="section-group">
        <view class="group-title">
          <text class="title-text">账户设置</text>
        </view>
        <view class="setting-item" @click="editProfile">
          <view class="item-icon">
            <uni-icons type="person" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">个人资料</text>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
        <view class="setting-item" @click="changePassword">
          <view class="item-icon">
            <uni-icons type="locked" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">修改密码</text>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
        <view class="setting-item" @click="bindPhone">
          <view class="item-icon">
            <uni-icons type="phone" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">手机绑定</text>
          <view class="item-status">
            <text class="status-text">{{ userInfo.phone ? '已绑定' : '未绑定' }}</text>
          </view>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
      </view>

      <!-- 通知设置 -->
      <view class="section-group">
        <view class="group-title">
          <text class="title-text">通知设置</text>
        </view>
        <view class="setting-item">
          <view class="item-icon">
            <uni-icons type="notification" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">消息通知</text>
          <switch class="setting-switch" :checked="settings.messageNotification" @change="toggleNotification" />
        </view>
        <view class="setting-item">
          <view class="item-icon">
            <uni-icons type="sound" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">声音提醒</text>
          <switch class="setting-switch" :checked="settings.soundNotification" @change="toggleSound" />
        </view>
        <view class="setting-item">
          <view class="item-icon">
            <uni-icons type="chatbubble" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">评论通知</text>
          <switch class="setting-switch" :checked="settings.commentNotification" @change="toggleComment" />
        </view>
      </view>

      <!-- 隐私设置 -->
      <view class="section-group">
        <view class="group-title">
          <text class="title-text">隐私设置</text>
        </view>
        <view class="setting-item">
          <view class="item-icon">
            <uni-icons type="eye" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">名片可见性</text>
          <view class="item-status">
            <text class="status-text">{{ getVisibilityText(settings.cardVisibility) }}</text>
          </view>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc" @click="setVisibility"></uni-icons>
        </view>
        <view class="setting-item">
          <view class="item-icon">
            <uni-icons type="location" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">位置信息</text>
          <switch class="setting-switch" :checked="settings.locationEnabled" @change="toggleLocation" />
        </view>
      </view>

      <!-- 其他设置 -->
      <view class="section-group">
        <view class="group-title">
          <text class="title-text">其他</text>
        </view>
        <view class="setting-item" @click="clearCache">
          <view class="item-icon">
            <uni-icons type="trash" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">清除缓存</text>
          <view class="item-status">
            <text class="status-text">{{ cacheSize }}</text>
          </view>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
        <view class="setting-item" @click="checkUpdate">
          <view class="item-icon">
            <uni-icons type="refresh" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">检查更新</text>
          <view class="item-status">
            <text class="status-text">v{{ version }}</text>
          </view>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
        <view class="setting-item" @click="viewAgreement">
          <view class="item-icon">
            <uni-icons type="paperplane" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">用户协议</text>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
        <view class="setting-item" @click="viewPrivacy">
          <view class="item-icon">
            <uni-icons type="locked" size="20" color="#666666"></uni-icons>
          </view>
          <text class="item-text">隐私政策</text>
          <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <view class="logout-btn" @click="confirmLogout">
        <text class="logout-text">退出登录</text>
      </view>
    </view>

    <!-- 可见性选择弹窗 -->
    <uni-popup ref="visibilityPopup" type="bottom">
      <view class="visibility-popup">
        <view class="popup-header">
          <text class="popup-title">名片可见性</text>
          <uni-icons class="close-icon" type="close" size="20" color="#666666" @click="closeVisibilityPopup"></uni-icons>
        </view>
        <view class="visibility-options">
          <view
            v-for="(option, index) in visibilityOptions"
            :key="index"
            class="option-item"
            :class="{ active: settings.cardVisibility === option.value }"
            @click="selectVisibility(option.value)"
          >
            <text class="option-text">{{ option.label }}</text>
            <text class="option-desc">{{ option.desc }}</text>
            <uni-icons v-if="settings.cardVisibility === option.value" class="check-icon" type="checkmarkempty" size="20" color="#ff6b35"></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { api } from '@/api/index.js'
import { config } from '@/config/index.js'

export default {
  data() {
    return {
      userInfo: {
        nickname: '',
        phone: '',
        avatar: ''
      },
      settings: {
        messageNotification: true,
        soundNotification: true,
        commentNotification: true,
        cardVisibility: 'public',
        locationEnabled: false
      },
      cacheSize: '12.5MB',
      version: config.version || '1.0.0',
      defaultAvatar: config.defaultAvatar,
      visibilityOptions: [
        {
          value: 'public',
          label: '公开',
          desc: '所有人都可以查看我的名片'
        },
        {
          value: 'friends',
          label: '仅好友',
          desc: '只有我的好友可以查看'
        },
        {
          value: 'private',
          label: '私密',
          desc: '只有我自己可以查看'
        }
      ]
    }
  },

  onLoad() {
    this.loadUserInfo()
    this.loadSettings()
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadUserInfo() {
      try {
        const res = await api.get('/users/current')
        if (res) {
          this.userInfo = res
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    async loadSettings() {
      try {
        // TODO: 后端需要实现用户设置接口
        // const res = await api.get('/users/settings')
        // if (res) {
        //   this.settings = { ...this.settings, ...res }
        // }
      } catch (error) {
        console.error('获取设置失败:', error)
      }
    },

    async saveSettings() {
      try {
        // TODO: 后端需要实现保存用户设置接口
        // await api.put('/users/settings', this.settings)
      } catch (error) {
        console.error('保存设置失败:', error)
      }
    },

    editProfile() {
      uni.navigateTo({
        url: '/pages/profile/edit'
      })
    },

    changePassword() {
      uni.navigateTo({
        url: '/pages/profile/password'
      })
    },

    bindPhone() {
      uni.navigateTo({
        url: '/pages/profile/phone'
      })
    },

    toggleNotification(e) {
      this.settings.messageNotification = e.detail.value
      this.saveSettings()
    },

    toggleSound(e) {
      this.settings.soundNotification = e.detail.value
      this.saveSettings()
    },

    toggleComment(e) {
      this.settings.commentNotification = e.detail.value
      this.saveSettings()
    },

    toggleLocation(e) {
      this.settings.locationEnabled = e.detail.value
      this.saveSettings()
    },

    setVisibility() {
      this.$refs.visibilityPopup.open()
    },

    closeVisibilityPopup() {
      this.$refs.visibilityPopup.close()
    },

    selectVisibility(value) {
      this.settings.cardVisibility = value
      this.saveSettings()
      this.closeVisibilityPopup()
    },

    getVisibilityText(value) {
      const option = this.visibilityOptions.find(item => item.value === value)
      return option ? option.label : '公开'
    },

    clearCache() {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有缓存数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除缓存逻辑
            uni.clearStorageSync()
            this.cacheSize = '0MB'
            uni.showToast({
              title: '缓存已清除',
              icon: 'success'
            })
          }
        }
      })
    },

    checkUpdate() {
      uni.showLoading({
        title: '检查中...'
      })

      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '已是最新版本',
          icon: 'success'
        })
      }, 1500)
    },

    viewAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },

    viewPrivacy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },

    confirmLogout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出当前账户吗？',
        success: (res) => {
          if (res.confirm) {
            this.logout()
          }
        }
      })
    },

    async logout() {
      try {
        await api.post('/auth/logout')
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        // 清除本地存储
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')

        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    },

    formatPhone(phone) {
      if (!phone) return '未绑定'
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 用户信息 */
.user-section {
  margin: 20rpx;
}

.user-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.user-phone {
  font-size: 26rpx;
  color: #666666;
}

.arrow-icon {
  padding: 10rpx;
}

/* 设置列表 */
.settings-section {
  margin: 20rpx;
}

.section-group {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.group-title {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f8f8;
  gap: 20rpx;
}

.setting-item:last-child {
  border-bottom: none;
}

.item-icon {
  width: 40rpx;
  display: flex;
  justify-content: center;
}

.item-text {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
}

.item-status {
  margin-right: 10rpx;
}

.status-text {
  font-size: 26rpx;
  color: #666666;
}

.setting-switch {
  transform: scale(0.8);
}

/* 退出登录 */
.logout-section {
  margin: 40rpx 20rpx 20rpx;
}

.logout-btn {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
}

.logout-text {
  font-size: 32rpx;
  color: #ff4757;
  font-weight: 600;
}

/* 可见性弹窗 */
.visibility-popup {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.close-icon {
  padding: 10rpx;
}

.visibility-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  padding: 30rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  position: relative;
}

.option-item.active {
  border-color: #ff6b35;
  background-color: #fff8f5;
}

.option-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.check-icon {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
</style>
