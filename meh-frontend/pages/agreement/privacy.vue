<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">隐私政策</text>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <view class="content-section">
        <!-- 标题 -->
        <view class="header-section">
          <text class="main-title">隐私政策</text>
          <text class="update-time">更新时间：{{ updateTime }}</text>
          <text class="effective-time">生效时间：{{ effectiveTime }}</text>
        </view>

        <!-- 前言 -->
        <view class="section">
          <text class="section-content">
            欢迎使用「MEH 电子名片」！我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。
          </text>
        </view>

        <!-- 第一条 -->
        <view class="section">
          <text class="section-title">一、我们收集的信息</text>
          <text class="section-content">
            为了向您提供更好的服务，我们可能会收集以下信息：
          </text>
          <view class="list-section">
            <text class="list-item">1. 账户信息：包括您的手机号码、昵称、头像等注册信息</text>
            <text class="list-item">2. 名片信息：包括您创建的名片内容、联系方式、公司信息等</text>
            <text class="list-item">3. 设备信息：包括设备型号、操作系统版本、设备标识符等</text>
            <text class="list-item">4. 位置信息：在您授权的情况下，我们可能收集您的位置信息</text>
            <text class="list-item">5. 使用信息：包括您的操作记录、访问时间、使用偏好等</text>
          </view>
        </view>

        <!-- 第二条 -->
        <view class="section">
          <text class="section-title">二、信息的使用</text>
          <text class="section-content">
            我们会将收集的信息用于以下目的：
          </text>
          <view class="list-section">
            <text class="list-item">1. 提供、维护和改进我们的服务</text>
            <text class="list-item">2. 处理您的请求和交易</text>
            <text class="list-item">3. 向您发送服务相关的通知和更新</text>
            <text class="list-item">4. 保护我们的服务安全，防止欺诈行为</text>
            <text class="list-item">5. 遵守法律法规要求</text>
          </view>
        </view>

        <!-- 第三条 -->
        <view class="section">
          <text class="section-title">三、信息的共享</text>
          <text class="section-content">
            我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：
          </text>
          <view class="list-section">
            <text class="list-item">1. 获得您的明确同意</text>
            <text class="list-item">2. 法律法规要求或政府部门要求</text>
            <text class="list-item">3. 为保护我们的合法权益</text>
            <text class="list-item">4. 与我们的服务提供商共享，但仅限于提供服务所必需的信息</text>
          </view>
        </view>

        <!-- 第四条 -->
        <view class="section">
          <text class="section-title">四、信息的存储</text>
          <text class="section-content">
            我们采用业界标准的安全措施来保护您的个人信息：
          </text>
          <view class="list-section">
            <text class="list-item">1. 使用加密技术保护数据传输和存储</text>
            <text class="list-item">2. 建立严格的数据访问权限控制</text>
            <text class="list-item">3. 定期进行安全审计和漏洞检测</text>
            <text class="list-item">4. 制定数据泄露应急响应计划</text>
          </view>
        </view>

        <!-- 第五条 -->
        <view class="section">
          <text class="section-title">五、您的权利</text>
          <text class="section-content">
            您对自己的个人信息享有以下权利：
          </text>
          <view class="list-section">
            <text class="list-item">1. 访问权：您有权了解我们收集、使用您个人信息的情况</text>
            <text class="list-item">2. 更正权：您有权要求我们更正或补充您的个人信息</text>
            <text class="list-item">3. 删除权：您有权要求我们删除您的个人信息</text>
            <text class="list-item">4. 撤回同意：您有权撤回对个人信息处理的同意</text>
            <text class="list-item">5. 投诉举报：您有权向有关部门投诉举报</text>
          </view>
        </view>

        <!-- 第六条 -->
        <view class="section">
          <text class="section-title">六、未成年人保护</text>
          <text class="section-content">
            我们非常重视未成年人的个人信息保护。如果您是未成年人，建议您请您的监护人仔细阅读本隐私政策，并在征得您的监护人同意后使用我们的服务或向我们提供信息。
          </text>
        </view>

        <!-- 第七条 -->
        <view class="section">
          <text class="section-title">七、政策更新</text>
          <text class="section-content">
            我们可能会不时更新本隐私政策。当我们对隐私政策进行重大变更时，我们会通过适当的方式通知您，包括但不限于在我们的服务中发布通知或向您发送邮件。
          </text>
        </view>

        <!-- 第八条 -->
        <view class="section">
          <text class="section-title">八、联系我们</text>
          <text class="section-content">
            如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：
          </text>
          <view class="contact-section">
            <view class="contact-item">
              <text class="contact-label">邮箱：</text>
              <text class="contact-value"><EMAIL></text>
            </view>
            <view class="contact-item">
              <text class="contact-label">电话：</text>
              <text class="contact-value">400-123-4567</text>
            </view>
            <view class="contact-item">
              <text class="contact-label">地址：</text>
              <text class="contact-value">北京市朝阳区xxx街道xxx号</text>
            </view>
          </view>
        </view>

        <!-- 底部声明 -->
        <view class="footer-section">
          <text class="footer-text">
            本隐私政策是「MEH 电子名片」服务协议的重要组成部分。使用我们的服务即表示您同意本隐私政策的全部内容。
          </text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      updateTime: '2024年1月15日',
      effectiveTime: '2024年1月15日'
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 100rpx);
}

.content-section {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.main-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  display: block;
  margin-bottom: 20rpx;
}

.update-time,
.effective-time {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

/* 章节 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
}

/* 列表 */
.list-section {
  margin-left: 20rpx;
}

.list-item {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}

.list-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #ff6b35;
  font-weight: bold;
}

/* 联系方式 */
.contact-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  width: 100rpx;
  flex-shrink: 0;
}

.contact-value {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

/* 底部声明 */
.footer-section {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
  text-align: center;
}

.footer-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
  font-style: italic;
}
</style>
