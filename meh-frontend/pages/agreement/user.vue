<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">用户协议</text>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <view class="content-section">
        <!-- 标题 -->
        <view class="header-section">
          <text class="main-title">用户服务协议</text>
          <text class="update-time">更新时间：{{ updateTime }}</text>
          <text class="effective-time">生效时间：{{ effectiveTime }}</text>
        </view>

        <!-- 前言 -->
        <view class="section">
          <text class="section-content">
            欢迎使用「MEH 电子名片」服务！本协议是您与我们之间关于您使用「MEH 电子名片」服务所订立的协议。请您仔细阅读本协议，特别是免除或者限制责任的条款、法律适用和争议解决条款。除非您已阅读并接受本协议所有条款，否则您无权使用本服务。
          </text>
        </view>

        <!-- 第一条 -->
        <view class="section">
          <text class="section-title">一、服务内容</text>
          <text class="section-content">
            「MEH 电子名片」是一款专业的数字名片服务平台，为用户提供以下服务：
          </text>
          <view class="list-section">
            <text class="list-item">1. 数字名片创建、编辑、分享功能</text>
            <text class="list-item">2. 名片模板和个性化定制服务</text>
            <text class="list-item">3. 联系人管理和互动功能</text>
            <text class="list-item">4. 积分系统和兑换服务</text>
            <text class="list-item">5. 数据统计和分析功能</text>
            <text class="list-item">6. 其他相关增值服务</text>
          </view>
        </view>

        <!-- 第二条 -->
        <view class="section">
          <text class="section-title">二、用户注册</text>
          <text class="section-content">
            使用本服务需要注册账户，您需要：
          </text>
          <view class="list-section">
            <text class="list-item">1. 提供真实、准确、完整的个人信息</text>
            <text class="list-item">2. 及时更新注册信息，确保其真实有效</text>
            <text class="list-item">3. 妥善保管账户信息，对账户活动负责</text>
            <text class="list-item">4. 不得将账户转让、出售或授权他人使用</text>
            <text class="list-item">5. 发现账户被盗用应立即通知我们</text>
          </view>
        </view>

        <!-- 第三条 -->
        <view class="section">
          <text class="section-title">三、使用规范</text>
          <text class="section-content">
            在使用本服务时，您承诺遵守以下规范：
          </text>
          <view class="list-section">
            <text class="list-item">1. 遵守国家法律法规和社会公德</text>
            <text class="list-item">2. 不发布违法、有害、虚假、侵权的内容</text>
            <text class="list-item">3. 不进行恶意攻击、骚扰他人的行为</text>
            <text class="list-item">4. 不利用服务进行商业欺诈或非法牟利</text>
            <text class="list-item">5. 不传播病毒、恶意代码或进行网络攻击</text>
            <text class="list-item">6. 尊重他人知识产权和合法权益</text>
          </view>
        </view>

        <!-- 第四条 -->
        <view class="section">
          <text class="section-title">四、知识产权</text>
          <text class="section-content">
            关于知识产权的约定：
          </text>
          <view class="list-section">
            <text class="list-item">1. 本服务的软件、界面设计、商标等知识产权归我们所有</text>
            <text class="list-item">2. 您上传的内容，您保留相应的知识产权</text>
            <text class="list-item">3. 您授权我们在提供服务范围内使用您的内容</text>
            <text class="list-item">4. 未经许可，不得复制、修改、传播本服务内容</text>
            <text class="list-item">5. 如发现侵权行为，我们有权采取相应措施</text>
          </view>
        </view>

        <!-- 第五条 -->
        <view class="section">
          <text class="section-title">五、隐私保护</text>
          <text class="section-content">
            我们重视您的隐私保护：
          </text>
          <view class="list-section">
            <text class="list-item">1. 严格按照隐私政策处理您的个人信息</text>
            <text class="list-item">2. 采用行业标准的安全措施保护数据</text>
            <text class="list-item">3. 不会未经授权向第三方披露您的信息</text>
            <text class="list-item">4. 您有权查询、更正、删除个人信息</text>
            <text class="list-item">5. 详细内容请参阅我们的隐私政策</text>
          </view>
        </view>

        <!-- 第六条 -->
        <view class="section">
          <text class="section-title">六、服务变更与终止</text>
          <text class="section-content">
            关于服务变更与终止的规定：
          </text>
          <view class="list-section">
            <text class="list-item">1. 我们可能会更新、修改或终止部分服务功能</text>
            <text class="list-item">2. 重大变更会提前通知用户</text>
            <text class="list-item">3. 您可以随时停止使用服务并注销账户</text>
            <text class="list-item">4. 违反协议的账户可能被限制或终止服务</text>
            <text class="list-item">5. 服务终止后，相关数据可能被删除</text>
          </view>
        </view>

        <!-- 第七条 -->
        <view class="section">
          <text class="section-title">七、免责声明</text>
          <text class="section-content">
            在法律允许的范围内：
          </text>
          <view class="list-section">
            <text class="list-item">1. 我们不保证服务完全无中断、无错误</text>
            <text class="list-item">2. 不对用户发布的内容承担责任</text>
            <text class="list-item">3. 不对因不可抗力导致的损失承担责任</text>
            <text class="list-item">4. 不对第三方服务或链接内容负责</text>
            <text class="list-item">5. 用户应对自己的行为承担相应责任</text>
          </view>
        </view>

        <!-- 第八条 -->
        <view class="section">
          <text class="section-title">八、争议解决</text>
          <text class="section-content">
            如发生争议，双方应友好协商解决。协商不成的，可通过以下方式解决：
          </text>
          <view class="list-section">
            <text class="list-item">1. 向消费者权益保护组织投诉</text>
            <text class="list-item">2. 向有关行政部门申诉</text>
            <text class="list-item">3. 提交仲裁机构仲裁</text>
            <text class="list-item">4. 向人民法院提起诉讼</text>
          </view>
        </view>

        <!-- 第九条 -->
        <view class="section">
          <text class="section-title">九、协议修改</text>
          <text class="section-content">
            我们可能会根据业务发展需要修改本协议。修改后的协议将在服务中公布，继续使用服务即视为接受修改后的协议。如不同意修改内容，您可以停止使用服务。
          </text>
        </view>

        <!-- 第十条 -->
        <view class="section">
          <text class="section-title">十、联系方式</text>
          <text class="section-content">
            如对本协议有任何疑问，请联系我们：
          </text>
          <view class="contact-section">
            <view class="contact-item">
              <text class="contact-label">客服邮箱：</text>
              <text class="contact-value"><EMAIL></text>
            </view>
            <view class="contact-item">
              <text class="contact-label">客服电话：</text>
              <text class="contact-value">400-123-4567</text>
            </view>
            <view class="contact-item">
              <text class="contact-label">工作时间：</text>
              <text class="contact-value">周一至周五 9:00-18:00</text>
            </view>
          </view>
        </view>

        <!-- 底部声明 -->
        <view class="footer-section">
          <text class="footer-text">
            本协议自发布之日起生效。感谢您选择「MEH 电子名片」，祝您使用愉快！
          </text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      updateTime: '2024年1月15日',
      effectiveTime: '2024年1月15日'
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 100rpx);
}

.content-section {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.main-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  display: block;
  margin-bottom: 20rpx;
}

.update-time,
.effective-time {
  font-size: 24rpx;
  color: #666666;
  display: block;
  margin-bottom: 8rpx;
}

/* 章节 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  display: block;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.section-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
}

/* 列表 */
.list-section {
  margin-left: 20rpx;
}

.list-item {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  display: block;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}

.list-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #ff6b35;
  font-weight: bold;
}

/* 联系方式 */
.contact-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 600;
  width: 140rpx;
  flex-shrink: 0;
}

.contact-value {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

/* 底部声明 */
.footer-section {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
  text-align: center;
}

.footer-text {
  font-size: 26rpx;
  color: #999999;
  line-height: 1.5;
  font-style: italic;
}
</style>
