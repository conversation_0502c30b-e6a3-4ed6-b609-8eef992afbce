<template>
  <view class="container">
    <!-- 登录表单 -->
    <view class="login-form">
      <view class="logo-area">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <view class="title">MEH电子名片</view>
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <input class="input" type="number" placeholder="请输入手机号" v-model="form.phone" maxlength="11" />
      </view>
      
      <view class="form-item">
        <text class="label">密码</text>
        <input class="input" type="password" placeholder="请输入密码" v-model="form.password" password />
      </view>
      
      <view class="form-item verify-code-item">
        <text class="label">验证码</text>
        <view class="verify-code-input-box">
          <input class="input verify-code-input" type="number" placeholder="请输入验证码" v-model="form.verifyCode" maxlength="6" />
          <button class="verify-code-btn" :disabled="countdown > 0" @click="getVerifyCode">
            {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
          </button>
        </view>
      </view>
      
      <view class="form-item remember-me">
        <checkbox :checked="form.rememberMe" @click="form.rememberMe = !form.rememberMe" color="#1296db" />
        <text class="remember-text">记住我</text>
      </view>
      
      <view class="form-actions">
        <view class="action-link" @click="navigateTo('/pages/register/register')">注册账号</view>
        <view class="action-link" @click="navigateTo('/pages/forget/forget')">忘记密码</view>
      </view>
      
      <button class="login-btn" @click="handleLogin" :disabled="loading">
        <text v-if="!loading">登录</text>
        <text v-else>登录中...</text>
      </button>
      
      <view class="divider">
        <text class="divider-text">其他登录方式</text>
      </view>
      
      <view class="other-login">
        <view class="other-login-item" @click="handleWxLogin">
          <image class="other-login-icon" src="/static/images/icons/wechat.png" mode="aspectFit"></image>
          <text class="other-login-text">微信登录</text>
        </view>
      </view>
    </view>
    
    <!-- 底部协议 -->
    <view class="agreement">
      <checkbox :checked="agree" @click="agree = !agree" color="#1296db" />
      <text class="agreement-text">登录即表示同意</text>
      <text class="agreement-link" @click="navigateTo('/pages/agreement/user')">《用户协议》</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link" @click="navigateTo('/pages/agreement/privacy')">《隐私政策》</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        phone: '',
        password: '',
        verifyCode: '', // 验证码字段，与UserLoginDTO保持一致
        rememberMe: false // 记住我字段，与UserLoginDTO保持一致
      },
      agree: true,
      loading: false,
      countdown: 0, // 验证码倒计时
      timer: null // 倒计时定时器
    }
  },
  methods: {
    // 处理登录
    handleLogin() {
      // 表单验证
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      
      if (!this.form.password) {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        })
        return
      }
      
      // 验证码验证（如果需要验证码）
      if (this.needVerifyCode && !this.form.verifyCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }
      
      if (!this.agree) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      
      // 登录请求
      this.loading = true
      
      // 构建登录DTO，与后端UserLoginDTO保持一致
      const loginDTO = {
        username: this.form.phone, // 使用手机号作为用户名
        password: this.form.password,
        verifyCode: this.form.verifyCode,
        rememberMe: this.form.rememberMe
      }
      
      // 调用后端登录接口
      this.$api.post('/auth/login', loginDTO).then(res => {
        // 登录成功，保存用户信息和token
        this.$store.commit('login', res)
        
        // 如果选择了记住我，保存登录信息
        if (this.form.rememberMe) {
          uni.setStorageSync('rememberMe', true)
          uni.setStorageSync('username', this.form.phone)
        } else {
          uni.removeStorageSync('rememberMe')
          uni.removeStorageSync('username')
        }
        
        // 跳转到首页
        uni.switchTab({
          url: '/pages/index/index'
        })
      }).catch(() => {
        // 登录失败，错误提示已在请求拦截器中处理
      }).finally(() => {
        this.loading = false
      })
    },
    
    // 获取验证码
    getVerifyCode() {
      // 手机号验证
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      
      // 手机号格式验证
      if (!/^1\d{10}$/.test(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }
      
      // 发送验证码请求
      this.$api.post('/auth/send-verify-code', {
        phone: this.form.phone,
        type: 'login' // 验证码类型：登录
      }).then(() => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        
        // 开始倒计时
        this.countdown = 60
        this.timer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.timer)
          }
        }, 1000)
      }).catch(() => {
        // 发送失败，错误提示已在请求拦截器中处理
      })
    }
    },
    
    // 处理微信登录
    handleWxLogin() {
      if (!this.agree) {
        uni.showToast({
          title: '请阅读并同意用户协议和隐私政策',
          icon: 'none'
        })
        return
      }
      
      // 显示加载中
      uni.showLoading({
        title: '登录中...'
      })
      
      // 微信登录
      uni.login({
        provider: 'weixin',
        success: (loginRes) => {
          // 获取微信登录code
          const code = loginRes.code
          
          // 发送code到后端，与AuthController.wxLogin方法对应
          this.$api.post('/auth/wx-login', null, {
            params: {
              code: code // 使用请求参数传递code，与后端接口保持一致
            }
          }).then(res => {
            // 登录成功，保存用户信息和token
            this.$store.commit('login', res)
            
            // 跳转到首页
            uni.switchTab({
              url: '/pages/index/index'
            })
          }).catch(() => {
            // 登录失败，错误提示已在请求拦截器中处理
          }).finally(() => {
            uni.hideLoading()
          })
        },
        fail: () => {
          uni.hideLoading()
          uni.showToast({
            title: '微信登录失败，请重试',
            icon: 'none'
          })
        }
      })
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({
        url
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  padding: 60rpx 40rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 登录表单 */
.login-form {
  flex: 1;
  
  .logo-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80rpx;
    
    .logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
    }
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333333;
    }
  }
  
  .form-item {
    margin-bottom: 30rpx;
    
    .label {
      display: block;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 10rpx;
    }
    
    .input {
      height: 90rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
    }
  }
  
  /* 验证码样式 */
  .verify-code-item {
    position: relative;
  }

  .verify-code-input-box {
    display: flex;
    align-items: center;
  }

  .verify-code-input {
    flex: 1;
  }

  .verify-code-btn {
    height: 90rpx;
    line-height: 90rpx;
    padding: 0 20rpx;
    font-size: 24rpx;
    background-color: #1296db;
    color: #fff;
    border-radius: 0 8rpx 8rpx 0;
    border: none;
  }

  .verify-code-btn[disabled] {
    background-color: #ccc;
  }

  /* 记住我样式 */
  .remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .remember-text {
    font-size: 26rpx;
    color: #666;
    margin-left: 10rpx;
  }
  
  .form-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40rpx;
    
    .action-link {
      font-size: 24rpx;
      color: #1296db;
    }
  }
  
  .login-btn {
    height: 90rpx;
    background-color: #1296db;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 60rpx;
    
    &:active {
      opacity: 0.8;
    }
    
    &[disabled] {
      opacity: 0.6;
      background-color: #1296db;
      color: #ffffff;
    }
  }
  
  .divider {
    position: relative;
    text-align: center;
    margin: 40rpx 0;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      width: 40%;
      height: 1px;
      background-color: #eeeeee;
    }
    
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      width: 40%;
      height: 1px;
      background-color: #eeeeee;
    }
    
    .divider-text {
      display: inline-block;
      padding: 0 20rpx;
      font-size: 24rpx;
      color: #999999;
      background-color: #ffffff;
      position: relative;
      z-index: 1;
    }
  }
  
  .other-login {
    display: flex;
    justify-content: center;
    
    .other-login-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .other-login-icon {
        width: 80rpx;
        height: 80rpx;
        margin-bottom: 10rpx;
      }
      
      .other-login-text {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
}

/* 底部协议 */
.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999999;
  margin-top: 40rpx;
  
  .agreement-text {
    margin: 0 4rpx;
  }
  
  .agreement-link {
    color: #1296db;
  }
}
</style>
