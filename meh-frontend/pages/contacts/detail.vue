<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">联系人详情</text>
      <uni-icons class="more-icon" type="more-filled" size="20" color="#333333" @click="showMore"></uni-icons>
    </view>

    <!-- 联系人头像和基本信息 -->
    <view class="profile-section">
      <view class="profile-card">
        <view class="avatar-container">
          <image class="avatar" :src="contact.avatar" mode="aspectFill"></image>
          <view class="online-status" v-if="contact.isOnline"></view>
        </view>
        <view class="profile-info">
          <text class="contact-name">{{ contact.name }}</text>
          <text class="contact-position">{{ contact.position }}</text>
          <text class="contact-company">{{ contact.company }}</text>
          <view class="contact-tags" v-if="contact.tags && contact.tags.length > 0">
            <text v-for="(tag, index) in contact.tags" :key="index" class="tag">{{ tag }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="actions-section">
      <view class="action-buttons">
        <view class="action-btn" @click="makeCall">
          <uni-icons type="phone" size="24" color="#2B85E4"></uni-icons>
          <text class="action-text">电话</text>
        </view>
        <view class="action-btn" @click="sendMessage">
          <uni-icons type="chat" size="24" color="#52C41A"></uni-icons>
          <text class="action-text">消息</text>
        </view>
        <view class="action-btn" @click="sendEmail">
          <uni-icons type="email" size="24" color="#FF8A00"></uni-icons>
          <text class="action-text">邮件</text>
        </view>
        <view class="action-btn" @click="viewBusinessCard">
          <uni-icons type="person" size="24" color="#9C27B0"></uni-icons>
          <text class="action-text">名片</text>
        </view>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="details-section">
      <view class="detail-group">
        <view class="group-title">
          <uni-icons type="phone" size="16" color="#666666"></uni-icons>
          <text class="title-text">联系方式</text>
        </view>
        <view class="detail-item" v-if="contact.phone">
          <text class="detail-label">手机号码</text>
          <view class="detail-value">
            <text class="value-text">{{ contact.phone }}</text>
            <uni-icons class="copy-icon" type="copy" size="16" color="#2B85E4" @click="copyText(contact.phone)"></uni-icons>
          </view>
        </view>
        <view class="detail-item" v-if="contact.email">
          <text class="detail-label">邮箱地址</text>
          <view class="detail-value">
            <text class="value-text">{{ contact.email }}</text>
            <uni-icons class="copy-icon" type="copy" size="16" color="#2B85E4" @click="copyText(contact.email)"></uni-icons>
          </view>
        </view>
        <view class="detail-item" v-if="contact.wechat">
          <text class="detail-label">微信号</text>
          <view class="detail-value">
            <text class="value-text">{{ contact.wechat }}</text>
            <uni-icons class="copy-icon" type="copy" size="16" color="#2B85E4" @click="copyText(contact.wechat)"></uni-icons>
          </view>
        </view>
      </view>

      <view class="detail-group" v-if="contact.address">
        <view class="group-title">
          <uni-icons type="location" size="16" color="#666666"></uni-icons>
          <text class="title-text">地址信息</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">工作地址</text>
          <view class="detail-value">
            <text class="value-text">{{ contact.address }}</text>
            <uni-icons class="map-icon" type="map" size="16" color="#2B85E4" @click="openMap"></uni-icons>
          </view>
        </view>
      </view>

      <view class="detail-group" v-if="contact.introduction">
        <view class="group-title">
          <uni-icons type="person" size="16" color="#666666"></uni-icons>
          <text class="title-text">个人介绍</text>
        </view>
        <view class="detail-item">
          <text class="introduction-text">{{ contact.introduction }}</text>
        </view>
      </view>
      
      <!-- 备注信息 -->
      <view class="detail-group" v-if="contact.remark">
        <view class="group-title">
          <uni-icons type="compose" size="16" color="#666666"></uni-icons>
          <text class="title-text">备注信息</text>
        </view>
        <view class="detail-item">
          <text class="remark-text">{{ contact.remark }}</text>
        </view>
      </view>
      
      <!-- 分组信息 -->
      <view class="detail-group" v-if="contact.groupName">
        <view class="group-title">
          <uni-icons type="folder" size="16" color="#666666"></uni-icons>
          <text class="title-text">所属分组</text>
        </view>
        <view class="detail-item">
          <view class="group-badge">{{ contact.groupName }}</view>
        </view>
      </view>

      <view class="detail-group" v-if="contact.skills && contact.skills.length > 0">
        <view class="group-title">
          <uni-icons type="star" size="16" color="#666666"></uni-icons>
          <text class="title-text">专业技能</text>
        </view>
        <view class="skills-container">
          <text v-for="(skill, index) in contact.skills" :key="index" class="skill-tag">{{ skill }}</text>
        </view>
      </view>
    </view>

    <!-- 互动记录 -->
    <view class="interaction-section">
      <view class="section-header">
        <text class="section-title">互动记录</text>
        <text class="view-all" @click="viewAllInteractions">查看全部</text>
      </view>
      <view class="interaction-list">
        <view v-for="(record, index) in interactionRecords" :key="index" class="interaction-item">
          <view class="interaction-icon" :class="record.type">
            <uni-icons :type="getInteractionIcon(record.type)" size="16" color="#FFFFFF"></uni-icons>
          </view>
          <view class="interaction-info">
            <text class="interaction-desc">{{ record.description }}</text>
            <text class="interaction-time">{{ formatTime(record.time) }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      contactId: '',
      contact: {
        id: 1,
        name: '张三',
        position: '产品经理',
        company: '阿里巴巴科技有限公司',
        phone: '13800138001',
        email: '<EMAIL>',
        wechat: 'zhangsan_ali',
        address: '浙江省杭州市余杭区文三西路969号',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
        isOnline: true,
        tags: ['产品专家', '用户体验', 'B端产品'],
        introduction: '拥有8年产品经验，专注于B端产品设计和用户体验优化，曾主导多个千万级用户产品的设计和迭代。',
        skills: ['产品设计', '用户研究', '数据分析', '项目管理', 'Axure', 'Figma']
      },
      interactionRecords: [
        {
          id: 1,
          type: 'call',
          description: '通话 5分钟',
          time: '2024-01-15 14:30:00'
        },
        {
          id: 2,
          type: 'message',
          description: '发送了消息',
          time: '2024-01-15 11:20:00'
        },
        {
          id: 3,
          type: 'email',
          description: '发送了邮件',
          time: '2024-01-14 16:45:00'
        },
        {
          id: 4,
          type: 'view',
          description: '查看了名片',
          time: '2024-01-14 09:15:00'
        }
      ]
    }
  },
  onLoad(options) {
    if (options.id) {
      this.contactId = options.id
      this.loadContactDetail()
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    showMore() {
      uni.showActionSheet({
        itemList: ['编辑联系人', '添加到收藏', '删除联系人'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.editContact()
              break
            case 1:
              this.addToFavorites()
              break
            case 2:
              this.deleteContact()
              break
          }
        }
      })
    },
    
    loadContactDetail() {
      // 调用API获取联系人详情
      // this.$api.contact.getDetail({ id: this.contactId }).then(res => {
      //   this.contact = res.data.contact
      //   this.interactionRecords = res.data.interactionRecords
      // })
    },
    
    makeCall() {
      if (!this.contact.phone) {
        uni.showToast({
          title: '暂无电话号码',
          icon: 'error'
        })
        return
      }
      
      uni.makePhoneCall({
        phoneNumber: this.contact.phone,
        success: () => {
          // 记录通话记录
          this.recordInteraction('call', '发起通话')
        },
        fail: () => {
          uni.showToast({
            title: '拨号失败',
            icon: 'error'
          })
        }
      })
    },
    
    sendMessage() {
      uni.navigateTo({
        url: `/pages/chat/index?userId=${this.contact.id}&userName=${encodeURIComponent(this.contact.name)}`
      })
    },
    
    sendEmail() {
      if (!this.contact.email) {
        uni.showToast({
          title: '暂无邮箱地址',
          icon: 'error'
        })
        return
      }
      
      // 这里可以调用系统邮件应用或者跳转到邮件发送页面
      uni.showToast({
        title: '邮件功能开发中',
        icon: 'none'
      })
    },
    
    viewBusinessCard() {
      uni.navigateTo({
        url: `/pages/card/detail?id=${this.contact.cardId || this.contact.id}`
      })
    },
    
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          })
        }
      })
    },
    
    openMap() {
      uni.openLocation({
        latitude: 30.2741,
        longitude: 120.1551,
        name: this.contact.company,
        address: this.contact.address
      })
    },
    
    editContact() {
      uni.navigateTo({
        url: `/pages/contacts/edit?id=${this.contact.id}`
      })
    },
    
    addToFavorites() {
      uni.showToast({
        title: '已添加到收藏',
        icon: 'success'
      })
    },
    
    deleteContact() {
      uni.showModal({
        title: '删除联系人',
        content: `确定要删除联系人「${this.contact.name}」吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用API删除联系人
            // this.$api.contact.delete({ id: this.contact.id }).then(() => {
            //   uni.showToast({
            //     title: '删除成功',
            //     icon: 'success'
            //   })
            //   uni.navigateBack()
            // })
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            uni.navigateBack()
          }
        }
      })
    },
    
    viewAllInteractions() {
      uni.navigateTo({
        url: `/pages/contacts/interactions?contactId=${this.contact.id}`
      })
    },
    
    recordInteraction(type, description) {
      const record = {
        id: Date.now(),
        type,
        description,
        time: new Date().toISOString()
      }
      this.interactionRecords.unshift(record)
    },
    
    getInteractionIcon(type) {
      const iconMap = {
        call: 'phone',
        message: 'chat',
        email: 'email',
        view: 'eye'
      }
      return iconMap[type] || 'circle'
    },
    
    formatTime(timeStr) {
      const now = new Date()
      const time = new Date(timeStr)
      const diff = now.getTime() - time.getTime()
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) {
        return '刚刚'
      } else if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        const date = new Date(timeStr)
        return `${date.getMonth() + 1}-${date.getDate()}`
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon,
.more-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.profile-section {
  padding: 32rpx;
}

.profile-card {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.avatar-container {
  position: relative;
  margin-right: 32rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
}

.online-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #52C41A;
  border: 4rpx solid #FFFFFF;
  border-radius: 16rpx;
}

.profile-info {
  flex: 1;
}

.contact-name {
  font-size: 24px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  display: block;
}

.contact-position {
  font-size: 16px;
  color: #666666;
  margin-bottom: 8rpx;
  display: block;
}

.contact-company {
  font-size: 14px;
  color: #999999;
  margin-bottom: 16rpx;
  display: block;
}

.contact-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background-color: #E8F4FD;
  color: #2B85E4;
  font-size: 12px;
  border-radius: 16rpx;
}

.actions-section {
  padding: 0 32rpx 32rpx;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.action-text {
  font-size: 12px;
  color: #666666;
  margin-top: 12rpx;
}

.details-section {
  padding: 0 32rpx;
}

.detail-group {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.group-title {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-left: 12rpx;
}

.detail-item {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 12rpx;
  display: block;
}

.detail-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.value-text {
  font-size: 16px;
  color: #333333;
  flex: 1;
}

.copy-icon,
.map-icon {
  margin-left: 16rpx;
}

.introduction-text {
  font-size: 16px;
  color: #333333;
  line-height: 1.6;
}

.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 24rpx 32rpx;
}

.skill-tag {
  padding: 12rpx 20rpx;
  background-color: #f8f9fa;
  color: #666666;
  font-size: 14px;
  border-radius: 20rpx;
  border: 1rpx solid #E9ECEF;
}

.interaction-section {
  padding: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.view-all {
  font-size: 14px;
  color: #2B85E4;
}

.interaction-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.interaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.interaction-item:last-child {
  border-bottom: none;
}

.interaction-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32rpx;
  margin-right: 24rpx;
}

.interaction-icon.call {
  background-color: #2B85E4;
}

.interaction-icon.message {
  background-color: #52C41A;
}

.interaction-icon.email {
  background-color: #FF8A00;
}

.interaction-icon.view {
  background-color: #9C27B0;
}

.interaction-info {
  flex: 1;
}

.interaction-desc {
  font-size: 16px;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.interaction-time {
  font-size: 12px;
  color: #999999;
}
</style>