<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">互动记录</text>
      <view class="nav-right">
        <uni-icons type="more-filled" size="20" color="#333333" @click="showMoreActions"></uni-icons>
      </view>
    </view>

    <!-- 联系人信息 -->
    <view class="contact-info" v-if="contactInfo.id">
      <view class="contact-avatar">
        <image
          class="avatar"
          :src="contactInfo.avatar || '/static/images/default-avatar.png'"
          mode="aspectFill"
        ></image>
        <view class="online-status" :class="{online: contactInfo.isOnline}"></view>
      </view>

      <view class="contact-details">
        <view class="contact-name">{{ contactInfo.name }}</view>
        <view class="contact-position" v-if="contactInfo.position">
          {{ contactInfo.position }}
        </view>
        <view class="contact-company" v-if="contactInfo.company">
          {{ contactInfo.company }}
        </view>
      </view>

      <view class="contact-actions">
        <button class="action-btn" @click="sendMessage">
          <uni-icons type="chatbubbles" size="16" color="#ff6b35"></uni-icons>
          <text>发消息</text>
        </button>
      </view>
    </view>

    <!-- 互动统计 -->
    <view class="stats-section">
      <view class="section-title">互动统计</view>
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-number">{{ interactionStats.totalInteractions }}</view>
          <view class="stats-label">总互动次数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ interactionStats.messageCount }}</view>
          <view class="stats-label">消息数量</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ interactionStats.visitCount }}</view>
          <view class="stats-label">访问次数</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">{{ interactionStats.lastInteractionDays }}</view>
          <view class="stats-label">最近互动</view>
        </view>
      </view>
    </view>

    <!-- 互动记录 -->
    <view class="interactions-section">
      <view class="section-title">
        <text>互动记录</text>
        <view class="filter-tabs">
          <view
            class="filter-tab"
            :class="{active: activeFilter === 'all'}"
            @click="setFilter('all')"
          >
            全部
          </view>
          <view
            class="filter-tab"
            :class="{active: activeFilter === 'message'}"
            @click="setFilter('message')"
          >
            消息
          </view>
          <view
            class="filter-tab"
            :class="{active: activeFilter === 'visit'}"
            @click="setFilter('visit')"
          >
            访问
          </view>
          <view
            class="filter-tab"
            :class="{active: activeFilter === 'exchange'}"
            @click="setFilter('exchange')"
          >
            名片交换
          </view>
        </view>
      </view>

      <view class="interactions-list">
        <view
          class="interaction-item"
          v-for="interaction in filteredInteractions"
          :key="interaction.id"
          @click="viewInteractionDetail(interaction)"
        >
          <view class="interaction-icon">
            <uni-icons
              :type="getInteractionIcon(interaction.type)"
              size="20"
              :color="getInteractionColor(interaction.type)"
            ></uni-icons>
          </view>

          <view class="interaction-content">
            <view class="interaction-title">
              {{ getInteractionTitle(interaction.type) }}
            </view>
            <view class="interaction-desc" v-if="interaction.description">
              {{ interaction.description }}
            </view>
            <view class="interaction-time">
              {{ formatTime(interaction.createTime) }}
            </view>
          </view>

          <view class="interaction-arrow">
            <uni-icons type="right" size="14" color="#cccccc"></uni-icons>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredInteractions.length === 0">
          <uni-icons type="list" size="60" color="#cccccc"></uni-icons>
          <text class="empty-text">暂无互动记录</text>
          <text class="empty-desc">开始与联系人互动吧</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore" @click="loadMore">
        <text>加载更多</text>
      </view>
    </view>

    <!-- 更多操作弹窗 -->
    <uni-popup ref="moreActionsPopup" type="bottom">
      <view class="more-actions">
        <view class="action-item" @click="exportInteractions">
          <uni-icons type="download" size="20" color="#333333"></uni-icons>
          <text>导出记录</text>
        </view>
        <view class="action-item" @click="clearInteractions">
          <uni-icons type="trash" size="20" color="#ff4d4f"></uni-icons>
          <text class="danger">清空记录</text>
        </view>
        <view class="action-item cancel" @click="hideMoreActions">
          <text>取消</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import api from '@/api/index.js'

export default {
  data() {
    return {
      contactId: null,
      contactInfo: {},
      interactionStats: {
        totalInteractions: 0,
        messageCount: 0,
        visitCount: 0,
        lastInteractionDays: '0天前'
      },
      interactions: [],
      activeFilter: 'all',
      hasMore: true,
      page: 1,
      pageSize: 20
    }
  },

  computed: {
    // 过滤后的互动记录
    filteredInteractions() {
      if (this.activeFilter === 'all') {
        return this.interactions
      }
      return this.interactions.filter(item => item.type === this.activeFilter)
    }
  },

  onLoad(options) {
    this.contactId = options.contactId

    this.loadContactInfo()
    this.loadInteractionStats()
    this.loadInteractions()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 加载联系人信息
    async loadContactInfo() {
      try {
        const res = await api.get(`/contact/info/${this.contactId}`)
        if (res.success) {
          this.contactInfo = res.data
        }
      } catch (error) {
        console.error('加载联系人信息失败:', error)
      }
    },

    // 加载互动统计
    async loadInteractionStats() {
      try {
        const res = await api.get('/contact/interaction/stats', {
          contactId: this.contactId
        })
        if (res.success) {
          this.interactionStats = res.data
        }
      } catch (error) {
        console.error('加载互动统计失败:', error)
      }
    },

    // 加载互动记录
    async loadInteractions(loadMore = false) {
      try {
        if (!loadMore) {
          this.page = 1
          this.interactions = []
        }

        const res = await api.get('/contact/interactions', {
          contactId: this.contactId,
          page: this.page,
          pageSize: this.pageSize
        })

        if (res.success) {
          const newInteractions = res.data.records || []
          this.interactions = loadMore ? [...this.interactions, ...newInteractions] : newInteractions
          this.hasMore = newInteractions.length === this.pageSize
        }
      } catch (error) {
        console.error('加载互动记录失败:', error)
      }
    },

    // 设置过滤条件
    setFilter(filter) {
      this.activeFilter = filter
    },

    // 加载更多
    loadMore() {
      this.page++
      this.loadInteractions(true)
    },

    // 发送消息
    sendMessage() {
      uni.navigateTo({
        url: `/pages/chat/chat?userId=${this.contactInfo.userId}`
      })
    },

    // 查看互动详情
    viewInteractionDetail(interaction) {
      // 根据互动类型跳转到不同页面
      switch (interaction.type) {
        case 'message':
          this.sendMessage()
          break
        case 'visit':
          uni.navigateTo({
            url: `/pages/visitors/detail?visitorId=${this.contactInfo.userId}&cardId=${interaction.cardId}`
          })
          break
        case 'exchange':
          uni.navigateTo({
            url: `/pages/card/detail?cardId=${interaction.cardId}`
          })
          break
      }
    },

    // 显示更多操作
    showMoreActions() {
      this.$refs.moreActionsPopup.open()
    },

    // 隐藏更多操作
    hideMoreActions() {
      this.$refs.moreActionsPopup.close()
    },

    // 导出互动记录
    async exportInteractions() {
      try {
        uni.showLoading({ title: '导出中...' })

        const res = await api.get('/contact/interactions/export', {
          contactId: this.contactId
        })

        if (res.success) {
          // 这里可以实现文件下载逻辑
          uni.showToast({
            title: '导出成功',
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('导出失败:', error)
        uni.showToast({
          title: '导出失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
        this.hideMoreActions()
      }
    },

    // 清空互动记录
    clearInteractions() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有互动记录吗？此操作不可恢复。',
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await api.delete('/contact/interactions', {
                contactId: this.contactId
              })

              if (result.success) {
                this.interactions = []
                this.loadInteractionStats()
                uni.showToast({
                  title: '清空成功',
                  icon: 'success'
                })
              }
            } catch (error) {
              console.error('清空失败:', error)
              uni.showToast({
                title: '清空失败',
                icon: 'none'
              })
            }
          }
        }
      })
      this.hideMoreActions()
    },

    // 获取互动图标
    getInteractionIcon(type) {
      const icons = {
        message: 'chatbubbles',
        visit: 'eye',
        exchange: 'swap',
        like: 'heart',
        comment: 'chatboxes'
      }
      return icons[type] || 'help'
    },

    // 获取互动颜色
    getInteractionColor(type) {
      const colors = {
        message: '#1890ff',
        visit: '#52c41a',
        exchange: '#ff6b35',
        like: '#ff4d4f',
        comment: '#722ed1'
      }
      return colors[type] || '#999999'
    },

    // 获取互动标题
    getInteractionTitle(type) {
      const titles = {
        message: '发送消息',
        visit: '访问名片',
        exchange: '交换名片',
        like: '点赞名片',
        comment: '评论名片'
      }
      return titles[type] || '未知互动'
    },

    // 格式化时间
    formatTime(dateTime) {
      const date = new Date(dateTime)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      const minute = 60 * 1000
      const hour = 60 * minute
      const day = 24 * hour

      if (diff < minute) {
        return '刚刚'
      } else if (diff < hour) {
        return `${Math.floor(diff / minute)}分钟前`
      } else if (diff < day) {
        return `${Math.floor(diff / hour)}小时前`
      } else if (diff < 7 * day) {
        return `${Math.floor(diff / day)}天前`
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 20rpx;
  flex: 1;
}

.nav-right {
  padding: 10rpx;
}

/* 联系人信息 */
.contact-info {
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.contact-avatar {
  position: relative;
  margin-right: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
}

.online-status {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  background-color: #cccccc;
  border: 3rpx solid #ffffff;
}

.online-status.online {
  background-color: #52c41a;
}

.contact-details {
  flex: 1;
}

.contact-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.contact-position {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.contact-company {
  font-size: 24rpx;
  color: #999999;
}

.contact-actions {
  margin-left: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid #ff6b35;
  background-color: transparent;
  color: #ff6b35;
}

.action-btn text {
  margin-left: 8rpx;
}

/* 统计区域 */
.stats-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 20rpx 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
}

/* 互动记录区域 */
.interactions-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 0 20rpx;
}

.filter-tabs {
  display: flex;
  gap: 16rpx;
}

.filter-tab {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.filter-tab.active {
  background-color: #ff6b35;
  color: #ffffff;
}

.interactions-list {
  margin-top: 30rpx;
}

.interaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.interaction-item:last-child {
  border-bottom: none;
}

.interaction-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.interaction-content {
  flex: 1;
}

.interaction-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.interaction-desc {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.interaction-time {
  font-size: 22rpx;
  color: #999999;
}

.interaction-arrow {
  margin-left: 20rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin: 20rpx 0 8rpx;
}

.empty-desc {
  display: block;
  font-size: 24rpx;
  color: #cccccc;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

/* 更多操作弹窗 */
.more-actions {
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 20rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  font-size: 28rpx;
  color: #333333;
  border-bottom: 1rpx solid #f0f0f0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-item text {
  margin-left: 16rpx;
}

.action-item.cancel {
  justify-content: center;
  margin-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item.cancel text {
  margin-left: 0;
}

.action-item .danger {
  color: #ff4d4f;
}
</style>
