<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">编辑联系人</text>
      <text class="save-btn" @click="saveContact">保存</text>
    </view>

    <!-- 编辑表单 -->
    <scroll-view class="form-container" scroll-y>
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="label required">姓名</text>
          <input class="input" type="text" placeholder="请输入姓名" v-model="form.name" maxlength="20" />
        </view>
        
        <view class="form-item">
          <text class="label">职位</text>
          <input class="input" type="text" placeholder="请输入职位" v-model="form.position" maxlength="30" />
        </view>
        
        <view class="form-item">
          <text class="label">公司</text>
          <input class="input" type="text" placeholder="请输入公司" v-model="form.company" maxlength="50" />
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        
        <view class="form-item">
          <text class="label">手机号码</text>
          <input class="input" type="number" placeholder="请输入手机号码" v-model="form.phone" maxlength="11" />
        </view>
        
        <view class="form-item">
          <text class="label">邮箱地址</text>
          <input class="input" type="text" placeholder="请输入邮箱地址" v-model="form.email" maxlength="50" />
        </view>
        
        <view class="form-item">
          <text class="label">地址</text>
          <input class="input" type="text" placeholder="请输入地址" v-model="form.address" maxlength="100" />
        </view>
      </view>

      <!-- 分组信息 -->
      <view class="form-section">
        <view class="section-title">分组信息</view>
        
        <view class="form-item">
          <text class="label">所属分组</text>
          <picker class="picker" :range="groupList" range-key="name" @change="onGroupChange">
            <view class="picker-value">
              <text>{{ selectedGroup ? selectedGroup.name : '请选择分组' }}</text>
              <uni-icons type="right" size="16" color="#999999"></uni-icons>
            </view>
          </picker>
        </view>
        
        <view class="form-item">
          <text class="label">备注</text>
          <textarea class="textarea" placeholder="请输入备注信息" v-model="form.remark" maxlength="200" />
        </view>
      </view>

      <!-- 标签信息 -->
      <view class="form-section">
        <view class="section-title">标签信息</view>
        
        <view class="tags-container">
          <view 
            v-for="(tag, index) in tags" 
            :key="index" 
            class="tag-item" 
            :class="{ 'selected': selectedTags.includes(tag) }"
            @click="toggleTag(tag)"
          >
            {{ tag }}
          </view>
          <view class="tag-item add-tag" @click="showAddTagModal">
            <uni-icons type="plus" size="14" color="#1296db"></uni-icons>
            <text>添加标签</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 添加标签弹窗 -->
    <uni-popup ref="addTagPopup" type="dialog">
      <uni-popup-dialog
        title="添加标签"
        :before-close="true"
        @confirm="addTag"
        @close="closeAddTagModal"
      >
        <input class="tag-input" type="text" v-model="newTag" placeholder="请输入标签名称" maxlength="10" />
      </uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      contactId: '',
      form: {
        id: '',
        name: '',
        position: '',
        company: '',
        phone: '',
        email: '',
        address: '',
        avatar: '',
        remark: '', // 备注字段，与ContactUpdateDTO保持一致
        groupId: null // 分组ID字段，与ContactUpdateDTO保持一致
      },
      groupList: [
        { id: 1, name: '同事' },
        { id: 2, name: '朋友' },
        { id: 3, name: '家人' },
        { id: 4, name: '客户' },
        { id: 5, name: '供应商' }
      ],
      selectedGroup: null,
      tags: ['重要', '常联系', '潜在客户', '合作伙伴', '技术专家', '管理层', '决策者'],
      selectedTags: [],
      newTag: ''
    }
  },
  onLoad(options) {
    if (options.id) {
      this.contactId = options.id
      this.loadContactDetail()
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    loadContactDetail() {
      // 调用API获取联系人详情
      // this.$api.contact.getDetail({ id: this.contactId }).then(res => {
      //   const contact = res.data
      //   this.form = {
      //     id: contact.id,
      //     name: contact.name,
      //     position: contact.position,
      //     company: contact.company,
      //     phone: contact.phone,
      //     email: contact.email,
      //     address: contact.address,
      //     avatar: contact.avatar,
      //     remark: contact.remark,
      //     groupId: contact.groupId
      //   }
      //   
      //   // 设置选中的分组
      //   if (contact.groupId) {
      //     this.selectedGroup = this.groupList.find(group => group.id === contact.groupId) || null
      //   }
      //   
      //   // 设置选中的标签
      //   if (contact.tags) {
      //     try {
      //       this.selectedTags = JSON.parse(contact.tags) || []
      //     } catch (e) {
      //       this.selectedTags = []
      //     }
      //   }
      // })
      
      // 模拟数据
      const contact = {
        id: this.contactId,
        name: '张三',
        position: '产品经理',
        company: '阿里巴巴科技有限公司',
        phone: '13800138001',
        email: '<EMAIL>',
        address: '浙江省杭州市余杭区文三西路969号',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
        remark: '在2023年会上认识的产品专家',
        groupId: 4,
        tags: JSON.stringify(['重要', '技术专家'])
      }
      
      this.form = {
        id: contact.id,
        name: contact.name,
        position: contact.position,
        company: contact.company,
        phone: contact.phone,
        email: contact.email,
        address: contact.address,
        avatar: contact.avatar,
        remark: contact.remark,
        groupId: contact.groupId
      }
      
      // 设置选中的分组
      if (contact.groupId) {
        this.selectedGroup = this.groupList.find(group => group.id === contact.groupId) || null
      }
      
      // 设置选中的标签
      if (contact.tags) {
        try {
          this.selectedTags = JSON.parse(contact.tags) || []
        } catch (e) {
          this.selectedTags = []
        }
      }
    },
    
    onGroupChange(e) {
      const index = e.detail.value
      this.selectedGroup = this.groupList[index]
      this.form.groupId = this.selectedGroup.id
    },
    
    toggleTag(tag) {
      const index = this.selectedTags.indexOf(tag)
      if (index > -1) {
        this.selectedTags.splice(index, 1)
      } else {
        this.selectedTags.push(tag)
      }
    },
    
    showAddTagModal() {
      this.newTag = ''
      this.$refs.addTagPopup.open()
    },
    
    closeAddTagModal() {
      this.$refs.addTagPopup.close()
    },
    
    addTag() {
      if (this.newTag.trim()) {
        const tag = this.newTag.trim()
        if (!this.tags.includes(tag)) {
          this.tags.push(tag)
        }
        if (!this.selectedTags.includes(tag)) {
          this.selectedTags.push(tag)
        }
        this.newTag = ''
      }
      this.$refs.addTagPopup.close()
    },
    
    saveContact() {
      // 表单验证
      if (!this.form.name) {
        uni.showToast({
          title: '请输入联系人姓名',
          icon: 'none'
        })
        return
      }
      
      // 准备提交的数据
      const contactData = {
        ...this.form,
        tags: JSON.stringify(this.selectedTags)
      }
      
      // 调用API保存联系人
      // this.$api.post('/contact/update', contactData).then(() => {
      //   uni.showToast({
      //     title: '保存成功',
      //     icon: 'success'
      //   })
      //   uni.navigateBack()
      // })
      
      // 模拟保存成功
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style>
page {
  height: 100%;
  background-color: #f5f6f7;
}

.container {
  height: 100%;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  position: relative;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.save-btn {
  font-size: 28rpx;
  color: #1296db;
  padding: 10rpx;
}

.form-container {
  height: calc(100% - 88rpx);
  padding: 20rpx 32rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.label {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 10rpx;
  display: block;
}

.required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4rpx;
}

.input {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  background-color: #ffffff;
}

.textarea {
  width: 100%;
  height: 160rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 26rpx;
  background-color: #ffffff;
}

.picker {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #ffffff;
}

.picker-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  color: #333333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.tag-item {
  display: flex;
  align-items: center;
  height: 60rpx;
  padding: 0 20rpx;
  margin: 10rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #f5f5f5;
}

.tag-item.selected {
  color: #1296db;
  background-color: rgba(18, 150, 219, 0.1);
  border: 1px solid #1296db;
}

.add-tag {
  color: #1296db;
  border: 1px dashed #1296db;
  background-color: transparent;
}

.tag-input {
  height: 80rpx;
  border: 1px solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  margin-top: 20rpx;
}
</style>