<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">通讯录</text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-box">
        <uni-icons class="search-icon" type="search" size="16" color="#999999"></uni-icons>
        <input class="search-input" type="text" placeholder="搜索联系人" v-model="searchText" @input="onSearch" />
      </view>
    </view>

    <!-- 联系人列表 -->
    <scroll-view class="contact-list" scroll-y>
      <template v-if="filteredContacts.length > 0">
        <view v-for="(contact, index) in filteredContacts" :key="index" class="contact-item" @click="viewContactDetail(contact)">
          <view class="avatar">
            <image class="avatar-img" :src="contact.avatar" mode="aspectFill"></image>
          </view>
          <view class="contact-info">
            <view class="contact-name">{{ contact.name }}</view>
            <view class="contact-company">{{ contact.company }} - {{ contact.position }}</view>
            <view class="contact-phone">{{ contact.phone }}</view>
          </view>
          <view class="contact-action" @click.stop="callContact(contact.phone)">
            <uni-icons class="phone-icon" type="phone" size="20" color="#2B85E4"></uni-icons>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">{{ searchText ? '未找到相关联系人' : '通讯录暂无联系人' }}</text>
          <text class="empty-desc" v-if="!searchText">快去添加联系人吧~</text>
        </view>
      </template>
    </scroll-view>

    <!-- 字母导航 -->
    <view class="letter-nav" v-if="!searchText && filteredContacts.length > 0">
      <text v-for="letter in letters" :key="letter" class="letter-item" @click="scrollToLetter(letter)">{{ letter }}</text>
    </view>

    <!-- 添加联系人按钮 -->
    <view class="add-contact-btn" @click="addContact">
      <uni-icons type="plus" size="24" color="#FFFFFF"></uni-icons>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      contacts: [
        {
          id: 1,
          name: '张三',
          company: '阿里巴巴科技有限公司',
          position: '产品经理',
          phone: '13800138001',
          email: '<EMAIL>',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          firstLetter: 'Z'
        },
        {
          id: 2,
          name: '李四',
          company: '腾讯科技有限公司',
          position: '技术总监',
          phone: '13800138002',
          email: '<EMAIL>',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          firstLetter: 'L'
        },
        {
          id: 3,
          name: '王五',
          company: '百度在线网络技术有限公司',
          position: '架构师',
          phone: '13800138003',
          email: '<EMAIL>',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
          firstLetter: 'W'
        },
        {
          id: 4,
          name: '赵六',
          company: '字节跳动科技有限公司',
          position: '运营总监',
          phone: '13800138004',
          email: '<EMAIL>',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          firstLetter: 'Z'
        }
      ],
      emptyImageUrl: 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg'
    }
  },
  computed: {
    filteredContacts() {
      if (!this.searchText) {
        return this.contacts.sort((a, b) => a.name.localeCompare(b.name, 'zh-CN'))
      }
      return this.contacts.filter(contact => 
        contact.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
        contact.company.toLowerCase().includes(this.searchText.toLowerCase()) ||
        contact.position.toLowerCase().includes(this.searchText.toLowerCase()) ||
        contact.phone.includes(this.searchText)
      )
    }
  },
  onLoad() {
    this.loadContacts()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    onSearch() {
      // 搜索逻辑已在computed中处理
    },
    
    loadContacts() {
      // 调用ContactController.getMyContactList方法获取联系人列表
      this.$api.get('/contacts', {
        params: {
          keyword: this.searchText || undefined
        }
      }).then(res => {
        if (res && Array.isArray(res)) {
          // 处理联系人数据，添加首字母用于索引
          this.contacts = res.map(contact => {
            // 获取姓名首字母（中文使用拼音首字母）
            const firstLetter = this.getFirstLetter(contact.name);
            return {
              ...contact,
              firstLetter: firstLetter.toUpperCase()
            };
          });
        } else {
          this.contacts = [];
        }
      }).catch(err => {
        console.error('获取联系人列表失败', err);
        uni.showToast({
          title: '获取联系人列表失败',
          icon: 'none'
        });
      });
    },
    
    // 获取姓名首字母（简化处理，实际项目中可使用完整的汉字转拼音库）
    getFirstLetter(name) {
      if (!name) return 'A';
      const first = name.charAt(0);
      // 简单判断是否为英文字母
      if (/[a-zA-Z]/.test(first)) {
        return first.toUpperCase();
      }
      // 中文字符返回默认值，实际项目中应使用汉字转拼音
      return 'A';
    },
    
    viewContactDetail(contact) {
      // 跳转到联系人详情页，传递联系人ID
      uni.navigateTo({
        url: `/pages/contacts/detail?id=${contact.id}`
      })
    },
    
    callContact(phone) {
      // 调用系统拨号功能
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: () => {
          uni.showToast({
            title: '拨号失败',
            icon: 'error'
          })
        }
      })
    },
    
    scrollToLetter(letter) {
      // 滚动到指定字母的联系人
      const targetContact = this.filteredContacts.find(contact => 
        contact.firstLetter === letter
      )
      if (targetContact) {
        // 这里可以实现滚动到指定位置的逻辑
        uni.showToast({
          title: `跳转到${letter}`,
          icon: 'none'
        })
      }
    },
    
    addContact() {
      uni.navigateTo({
        url: '/pages/contacts/edit'
      })
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
  position: relative;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.search-bar {
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.search-input-box {
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #f8f9fa;
  border-radius: 36rpx;
}

.search-icon {
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

.contact-list {
  flex: 1;
  padding: 0 32rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  margin-right: 24rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 48rpx;
}

.contact-info {
  flex: 1;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.contact-company {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4rpx;
}

.contact-phone {
  font-size: 12px;
  color: #999999;
}

.contact-action {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 36rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
}

.letter-nav {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 10rpx 0;
}

.letter-item {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666666;
}

.add-contact-btn {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background-color: #2B85E4;
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(43, 133, 228, 0.3);
}
</style>