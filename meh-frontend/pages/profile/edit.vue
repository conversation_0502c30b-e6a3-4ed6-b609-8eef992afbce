<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">编辑资料</text>
      <text class="save-btn" @click="saveProfile">保存</text>
    </view>

    <!-- 内容区域 -->
    <scroll-view class="content-scroll" scroll-y>
      <!-- 头像设置 -->
      <view class="avatar-section">
        <view class="avatar-container" @click="chooseAvatar">
          <image class="avatar" :src="userInfo.avatar || defaultAvatar" mode="aspectFill"></image>
          <view class="avatar-mask">
            <uni-icons type="camera" size="24" color="#ffffff"></uni-icons>
            <text class="avatar-text">更换头像</text>
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <view class="form-item">
          <text class="label">昵称</text>
          <input class="input" v-model="userInfo.nickname" placeholder="请输入昵称" maxlength="20" />
        </view>

        <view class="form-item">
          <text class="label">性别</text>
          <view class="gender-container">
            <view class="gender-item" :class="{active: userInfo.gender === 'MALE'}" @click="selectGender('MALE')">
              <uni-icons type="person" size="16" :color="userInfo.gender === 'MALE' ? '#ff6b35' : '#999999'"></uni-icons>
              <text class="gender-text">男</text>
            </view>
            <view class="gender-item" :class="{active: userInfo.gender === 'FEMALE'}" @click="selectGender('FEMALE')">
              <uni-icons type="person-filled" size="16" :color="userInfo.gender === 'FEMALE' ? '#ff6b35' : '#999999'"></uni-icons>
              <text class="gender-text">女</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="label">生日</text>
          <picker mode="date" :value="userInfo.birthday" @change="onBirthdayChange">
            <view class="picker-content">
              <text class="picker-text" :class="{placeholder: !userInfo.birthday}">{{ userInfo.birthday || '请选择生日' }}</text>
              <uni-icons type="right" size="14" color="#cccccc"></uni-icons>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="label">个人简介</text>
          <textarea class="textarea" v-model="userInfo.bio" placeholder="请输入个人简介" maxlength="200" auto-height></textarea>
        </view>
      </view>

      <!-- 联系信息 -->
      <view class="form-section">
        <view class="section-title">联系信息</view>

        <view class="form-item">
          <text class="label">手机号</text>
          <input class="input readonly" v-model="userInfo.phone" placeholder="未绑定" disabled />
          <text class="link-text" @click="bindPhone">{{ userInfo.phone ? '更换' : '绑定' }}</text>
        </view>

        <view class="form-item">
          <text class="label">邮箱</text>
          <input class="input" v-model="userInfo.email" placeholder="请输入邮箱" type="email" />
        </view>
      </view>

      <!-- 职业信息 -->
      <view class="form-section">
        <view class="section-title">职业信息</view>

        <view class="form-item">
          <text class="label">公司</text>
          <input class="input" v-model="userInfo.company" placeholder="请输入公司名称" maxlength="50" />
        </view>

        <view class="form-item">
          <text class="label">职位</text>
          <input class="input" v-model="userInfo.position" placeholder="请输入职位" maxlength="50" />
        </view>

        <view class="form-item">
          <text class="label">行业</text>
          <picker :range="industryList" range-key="name" @change="onIndustryChange">
            <view class="picker-content">
              <text class="picker-text" :class="{placeholder: !userInfo.industry}">{{ userInfo.industry || '请选择行业' }}</text>
              <uni-icons type="right" size="14" color="#cccccc"></uni-icons>
            </view>
          </picker>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-title">地址信息</view>

        <view class="form-item">
          <text class="label">所在地区</text>
          <picker mode="region" @change="onRegionChange">
            <view class="picker-content">
              <text class="picker-text" :class="{placeholder: !userInfo.region}">{{ userInfo.region || '请选择地区' }}</text>
              <uni-icons type="right" size="14" color="#cccccc"></uni-icons>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <text class="label">详细地址</text>
          <input class="input" v-model="userInfo.address" placeholder="请输入详细地址" maxlength="100" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import api from '@/api/index.js'
import config from '@/config/index.js'

export default {
  data() {
    return {
      userInfo: {
        nickname: '',
        avatar: '',
        gender: '',
        birthday: '',
        bio: '',
        phone: '',
        email: '',
        company: '',
        position: '',
        industry: '',
        region: '',
        address: ''
      },
      defaultAvatar: config.defaultAvatar,
      industryList: [
        { name: '互联网/IT' },
        { name: '金融/投资' },
        { name: '房地产/建筑' },
        { name: '教育/培训' },
        { name: '医疗/健康' },
        { name: '制造业' },
        { name: '贸易/零售' },
        { name: '服务业' },
        { name: '媒体/广告' },
        { name: '政府/非营利' },
        { name: '其他' }
      ]
    }
  },

  computed: {
    ...mapState(['user'])
  },

  onLoad() {
    this.loadUserInfo()
  },

  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const res = await api.get('/users/current')
        if (res) {
          this.userInfo = {
            ...this.userInfo,
            nickname: res.nickname || '',
            avatar: res.avatar || '',
            gender: res.gender || '',
            phone: res.phone || '',
            email: res.email || '',
            realName: res.realName || ''
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 保存资料
    async saveProfile() {
      // 验证必填项
      if (!this.userInfo.nickname.trim()) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '保存中...' })

        const res = await api.put('/users/current', this.userInfo)

        if (res) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: '保存失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.uploadAvatar(res.tempFilePaths[0])
        }
      })
    },

    // 上传头像
    async uploadAvatar(filePath) {
      try {
        uni.showLoading({ title: '上传中...' })

        uni.uploadFile({
          url: config.baseUrl + '/file/upload/avatar',
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': 'Bearer ' + uni.getStorageSync('token')
          },
          success: (uploadRes) => {
            const result = JSON.parse(uploadRes.data)
            if (result.code === 200) {
              this.userInfo.avatar = result.data
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              })
            } else {
              uni.showToast({
                title: result.message || '上传失败',
                icon: 'none'
              })
            }
          },
          fail: () => {
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          },
          complete: () => {
            uni.hideLoading()
          }
        })
      } catch (error) {
        console.error('上传头像失败:', error)
        uni.hideLoading()
      }
    },

    // 选择性别
    selectGender(gender) {
      this.userInfo.gender = gender
    },

    // 生日变更
    onBirthdayChange(e) {
      this.userInfo.birthday = e.detail.value
    },

    // 行业变更
    onIndustryChange(e) {
      this.userInfo.industry = this.industryList[e.detail.value].name
    },

    // 地区变更
    onRegionChange(e) {
      this.userInfo.region = e.detail.value.join(' ')
    },

    // 绑定手机号
    bindPhone() {
      uni.navigateTo({
        url: '/pages/settings/phone'
      })
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.save-btn {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: 600;
  padding: 10rpx;
}

/* 内容区域 */
.content-scroll {
  height: calc(100vh - 100rpx);
}

/* 头像设置 */
.avatar-section {
  background-color: #ffffff;
  padding: 60rpx 0;
  text-align: center;
  margin-bottom: 20rpx;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  border: 4rpx solid #f0f0f0;
}

.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.avatar-container:active .avatar-mask {
  opacity: 1;
}

.avatar-text {
  font-size: 20rpx;
  color: #ffffff;
  margin-top: 8rpx;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  padding: 30rpx 0 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.form-item:last-child {
  border-bottom: none;
  padding-bottom: 30rpx;
}

.label {
  font-size: 30rpx;
  color: #333333;
  width: 140rpx;
  flex-shrink: 0;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  padding: 0 20rpx;
}

.input.readonly {
  color: #999999;
}

.textarea {
  flex: 1;
  font-size: 30rpx;
  color: #333333;
  padding: 20rpx;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.link-text {
  font-size: 28rpx;
  color: #ff6b35;
  padding: 10rpx;
}

/* 性别选择 */
.gender-container {
  flex: 1;
  display: flex;
  gap: 40rpx;
  padding: 0 20rpx;
}

.gender-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 24rpx;
  transition: all 0.3s;
}

.gender-item.active {
  border-color: #ff6b35;
  background-color: #fff5f0;
}

.gender-text {
  font-size: 28rpx;
  color: #666666;
}

.gender-item.active .gender-text {
  color: #ff6b35;
}

/* 选择器 */
.picker-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.picker-text {
  font-size: 30rpx;
  color: #333333;
}

.picker-text.placeholder {
  color: #cccccc;
}
</style>
