<template>
  <view class="page">
    <view class="container">
      <!-- 个人信息区 -->
      <view class="profile-card">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
        <view class="user-info">
          <text class="username">{{ userInfo.name }}</text>
          <view class="member-tag">
            <uni-icons :size="14" type="vip" color="#FFB800"/>
            <text class="member-level">{{ userInfo.memberLevel }}</text>
          </view>
        </view>
      </view>

      <!-- 积分区域 -->
      <view class="points-card">
        <view class="points-info">
          <text class="points-label">当前积分</text>
          <text class="points-value">{{ userInfo.points }}</text>
        </view>
        <view class="points-mall" @click="goToPointsMall">
          <text class="mall-text">积分商城</text>
          <uni-icons :size="16" type="right" color="#666666"/>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-card">
        <view class="menu-item" v-for="(item, index) in menuItems" :key="index" @click="handleMenuClick(item.action)">
          <view class="menu-item-left">
            <uni-icons :size="20" :type="item.icon" color="#2B85E4"/>
            <text class="menu-item-text">{{ item.title }}</text>
          </view>
          <uni-icons :size="16" type="right" color="#666666"/>
        </view>
      </view>

      <!-- 退出登录按钮 -->
      <button class="logout-btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '张三',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/d6137f26593408e25632f7b97a47c96f.jpg',
        memberLevel: 'MEH金牌会员',
        points: 320
      },
      menuItems: [
        { title: '编辑个人资料', icon: 'person', action: 'editProfile' },
        { title: '我的名片管理', icon: 'card', action: 'manageCard' },
        { title: '我的二维码', icon: 'qrcode', action: 'showQRCode' },
        { title: '积分兑换记录', icon: 'medal', action: 'pointsHistory' },
        { title: '设置与隐私', icon: 'settings', action: 'settings' }
      ]
    }
  },
  methods: {
    goToPointsMall() {
      uni.navigateTo({
        url: '/pages/points/mall'
      })
    },
    handleMenuClick(action) {
      switch(action) {
        case 'editProfile':
          uni.navigateTo({
            url: '/pages/profile/edit'
          })
          break
        case 'manageCard':
          uni.navigateTo({
            url: '/pages/card/my'
          })
          break
        case 'showQRCode':
          uni.navigateTo({
            url: '/pages/card/share'
          })
          break
        case 'pointsHistory':
          uni.navigateTo({
            url: '/pages/points/history'
          })
          break
        case 'settings':
          uni.navigateTo({
            url: '/pages/settings/index'
          })
          break
      }
    },
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除用户信息
            this.$store.dispatch('user/logout')
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }
        }
      })
    }
  }
}
</script>

<style>
page {
  height: 100%;
  background-color: #F5F6F7;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  gap: 30rpx;
}

.profile-card {
  display: flex;
  align-items: center;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.member-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.member-level {
  font-size: 14px;
  color: #FFB800;
}

.points-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.points-info {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 24px;
  font-weight: bold;
  color: #2B85E4;
}

.points-mall {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.mall-text {
  font-size: 14px;
  color: #666666;
}

.menu-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #F5F6F7;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.menu-item-text {
  font-size: 16px;
  color: #333333;
}

.logout-btn {
  background-color: #FF4757;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  padding: 30rpx;
  font-size: 16px;
  font-weight: bold;
}

.logout-btn:active {
  background-color: #FF3742;
}
</style>