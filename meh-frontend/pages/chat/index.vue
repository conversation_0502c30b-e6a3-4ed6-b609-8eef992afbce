<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <view class="contact-info">
        <text class="contact-name">{{ contactInfo.name }}</text>
        <text class="contact-company">{{ contactInfo.company }}</text>
      </view>
      <uni-icons class="more-icon" type="more-filled" size="20" color="#333333" @click="showMore"></uni-icons>
    </view>

    <!-- 聊天内容区域 -->
    <scroll-view 
      class="chat-content" 
      scroll-y 
      :scroll-top="scrollTop" 
      :scroll-into-view="scrollIntoView"
      @scrolltoupper="loadMoreMessages"
    >
      <!-- 系统消息 -->
      <view class="system-message">
        <text class="system-text">{{ systemMessage }}</text>
      </view>

      <!-- 消息列表 -->
      <view v-for="(message, index) in messages" :key="index" class="message-item" :id="`msg-${index}`">
        <!-- 接收的消息 (根据receiverId判断) -->
        <view v-if="message.receiverId !== userInfo.id" class="message-received">
          <view class="avatar">
            <image class="avatar-img" :src="message.avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <view class="message-bubble received-bubble">
              <text class="message-text" v-if="message.type === 1">{{ message.content }}</text>
              <image v-else-if="message.type === 2" class="message-image" :src="message.mediaUrl || message.content" mode="aspectFit" @click="previewImage(message.mediaUrl || message.content)"></image>
              <view v-else-if="message.type === 3" class="voice-message" @click="playVoice(message)">
                <uni-icons type="sound" size="16" color="#333333"></uni-icons>
                <text class="voice-duration">{{ message.mediaDuration }}"</text>
              </view>
              <video v-else-if="message.type === 4" class="message-video" :src="message.mediaUrl" controls></video>
            </view>
            <text class="message-time">{{ formatTime(message.timestamp) }}</text>
          </view>
        </view>

        <!-- 发送的消息 -->
        <view v-else class="message-sent">
          <view class="message-content">
            <view class="message-bubble sent-bubble">
              <text class="message-text" v-if="message.type === 1">{{ message.content }}</text>
              <image v-else-if="message.type === 2" class="message-image" :src="message.mediaUrl || message.content" mode="aspectFit" @click="previewImage(message.mediaUrl || message.content)"></image>
              <view v-else-if="message.type === 3" class="voice-message" @click="playVoice(message)">
                <uni-icons type="sound" size="16" color="#FFFFFF"></uni-icons>
                <text class="voice-duration white">{{ message.mediaDuration }}"</text>
              </view>
              <video v-else-if="message.type === 4" class="message-video" :src="message.mediaUrl" controls></video>
            </view>
            <text class="message-time">{{ formatTime(message.timestamp) }}</text>
          </view>
          <view class="avatar">
            <image class="avatar-img" :src="userAvatar" mode="aspectFill"></image>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="input-area">
      <view class="input-toolbar">
        <uni-icons class="toolbar-icon" type="emotion" size="24" color="#666666" @click="showEmoji"></uni-icons>
        <uni-icons class="toolbar-icon" type="mic" size="24" color="#666666" @click="startVoiceRecord"></uni-icons>
        <uni-icons class="toolbar-icon" type="image" size="24" color="#666666" @click="selectImage"></uni-icons>
      </view>
      <view class="input-box">
        <input 
          class="message-input" 
          type="text" 
          placeholder="请输入消息..." 
          v-model="inputMessage" 
          @confirm="sendMessage"
          :focus="inputFocus"
        />
        <view class="send-btn" :class="{ active: inputMessage.trim() }" @click="sendMessage">
          <text class="send-text">发送</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      contactInfo: {
        id: null,
        name: '张三',
        company: '阿里巴巴科技有限公司'
      },
      userInfo: {
        id: null,
        name: '',
        avatar: ''
      },
      userAvatar: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
      systemMessage: '您已与张三建立联系，可以开始聊天了',
      inputMessage: '',
      inputFocus: false,
      scrollTop: 0,
      scrollIntoView: '',
      chatId: null // 会话ID，与ChatMessageDTO保持一致
      messages: [
        {
          id: 1,
          type: 1, // 消息类型：1-文本，2-图片，3-语音，4-视频，与ChatMessageDTO保持一致
          content: '您好，很高兴认识您！',
          timestamp: '2024-01-15 14:30:00',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          receiverId: 1001, // 接收者ID
          mediaUrl: '', // 媒体文件URL
          mediaSize: 0, // 媒体文件大小(kb)
          mediaDuration: 0 // 媒体文件时长(秒)
        },
        {
          id: 2,
          type: 1,
          content: '您好！我对您的产品很感兴趣，能详细介绍一下吗？',
          timestamp: '2024-01-15 14:31:00',
          receiverId: 1002,
          mediaUrl: '',
          mediaSize: 0,
          mediaDuration: 0
        },
        {
          id: 3,
          type: 1,
          content: '当然可以！我们的产品主要解决企业数字化转型中的痛点问题...',
          timestamp: '2024-01-15 14:32:00',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          receiverId: 1001,
          mediaUrl: '',
          mediaSize: 0,
          mediaDuration: 0
        },
        {
          id: 4,
          type: 1,
          content: '听起来很不错，能否安排一个详细的演示？',
          timestamp: '2024-01-15 14:35:00',
          receiverId: 1002,
          mediaUrl: '',
          mediaSize: 0,
          mediaDuration: 0
        }
      ]
    }
  },
  onLoad(options) {
    // 获取当前登录用户信息
    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo) {
      this.userInfo = userInfo
      this.userAvatar = userInfo.avatar || this.userAvatar
    }
    
    if (options.userId && options.userName) {
      this.contactInfo.id = options.userId
      this.contactInfo.name = decodeURIComponent(options.userName)
      this.loadChatHistory(options.userId)
    }
    this.scrollToBottom()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    showMore() {
      uni.showActionSheet({
        itemList: ['查看名片', '添加到通讯录', '清空聊天记录'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.viewBusinessCard()
              break
            case 1:
              this.addToContacts()
              break
            case 2:
              this.clearChatHistory()
              break
          }
        }
      })
    },
    
    loadChatHistory(userId) {
      // 调用API加载聊天记录
      this.$api.get('/chat/history', { userId }).then(res => {
        if (res && res.data) {
          this.chatId = res.data.chatId
          this.messages = res.data.messages.map(msg => ({
            id: msg.id,
            chatId: msg.chatId,
            type: msg.type, // 1-文本，2-图片，3-语音，4-视频
            content: msg.content,
            timestamp: msg.createTime || new Date().toISOString(),
            avatar: msg.senderAvatar,
            receiverId: msg.receiverId,
            mediaUrl: msg.mediaUrl || '',
            mediaSize: msg.mediaSize || 0,
            mediaDuration: msg.mediaDuration || 0
          }))
          this.contactInfo = {
            id: res.data.contactInfo.id,
            name: res.data.contactInfo.name,
            company: res.data.contactInfo.company
          }
        }
      }).catch(err => {
        console.error('加载聊天记录失败', err)
        uni.showToast({
          title: '加载聊天记录失败',
          icon: 'none'
        })
      })
    },
    
    loadMoreMessages() {
      // 加载更多历史消息
      uni.showToast({
        title: '没有更多消息了',
        icon: 'none'
      })
    },
    
    sendMessage() {
      if (!this.inputMessage.trim()) {
        return
      }
      
      const message = {
        id: Date.now(),
        chatId: this.chatId, // 会话ID
        type: 1, // 1-文本
        content: this.inputMessage.trim(),
        timestamp: new Date().toISOString(),
        receiverId: this.contactInfo.id || this.contactInfo.userId, // 接收者ID
        mediaUrl: '', // 媒体文件URL
        mediaSize: 0, // 媒体文件大小
        mediaDuration: 0 // 媒体文件时长
      }
      
      // 调用API发送消息
      this.$api.post('/chat/send', {
        chatId: message.chatId,
        content: message.content,
        type: message.type,
        receiverId: message.receiverId,
        mediaUrl: message.mediaUrl,
        mediaSize: message.mediaSize,
        mediaDuration: message.mediaDuration
      }).then(() => {
        console.log('消息发送成功')
      }).catch(err => {
        console.error('消息发送失败', err)
      })
      
      this.messages.push(message)
      this.inputMessage = ''
      
      // 调用API发送消息
      // this.$api.chat.sendMessage({
      //   receiverId: this.contactInfo.id,
      //   content: message.content,
      //   type: 'text'
      // })
      
      this.scrollToBottom()
    },
    
    showEmoji() {
      uni.showToast({
        title: '表情功能开发中',
        icon: 'none'
      })
    },
    
    startVoiceRecord() {
      uni.showToast({
        title: '语音功能开发中',
        icon: 'none'
      })
    },
    
    selectImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          const tempFile = res.tempFiles[0]
          
          // 上传图片
          uni.showLoading({
            title: '上传中...'
          })
          
          // 上传图片到服务器
          this.$api.upload('/file/upload', tempFilePath, 'file').then(res => {
            uni.hideLoading()
            
            const message = {
              id: Date.now(),
              chatId: this.chatId,
              type: 2, // 2-图片
              content: '',
              timestamp: new Date().toISOString(),
              receiverId: this.contactInfo.id || this.contactInfo.userId,
              mediaUrl: res.url,
              mediaSize: Math.floor(tempFile.size / 1024), // 转换为kb
              mediaDuration: 0
            }
            
            // 调用API发送消息
            this.$api.post('/chat/send', {
              chatId: message.chatId,
              content: message.content,
              type: message.type,
              receiverId: message.receiverId,
              mediaUrl: message.mediaUrl,
              mediaSize: message.mediaSize,
              mediaDuration: message.mediaDuration
            })
            
            this.messages.push(message)
            this.scrollToBottom()
          }).catch(() => {
            uni.hideLoading()
            uni.showToast({
              title: '图片上传失败',
              icon: 'none'
            })
          })
        }
      })
    },
    
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },
    
    playVoice(message) {
      uni.showToast({
        title: '播放语音',
        icon: 'none'
      })
    },
    
    startVoiceRecord() {
      const recorderManager = uni.getRecorderManager()
      
      recorderManager.onStart(() => {
        uni.showToast({
          title: '开始录音...',
          icon: 'none',
          duration: 60000
        })
      })
      
      recorderManager.onStop((res) => {
        uni.hideToast()
        const { tempFilePath, duration } = res
        
        // 上传语音文件
        uni.showLoading({
          title: '上传中...'
        })
        
        // 上传语音到服务器
        this.$api.upload('/file/upload', tempFilePath, 'file').then(res => {
          uni.hideLoading()
          
          const message = {
            id: Date.now(),
            chatId: this.chatId,
            type: 3, // 3-语音
            content: '',
            timestamp: new Date().toISOString(),
            receiverId: this.contactInfo.id || this.contactInfo.userId,
            mediaUrl: res.url,
            mediaSize: Math.floor(res.size / 1024), // 转换为kb
            mediaDuration: Math.floor(duration / 1000) // 转换为秒
          }
          
          // 调用API发送消息
          this.$api.post('/chat/send', {
            chatId: message.chatId,
            content: message.content,
            type: message.type,
            receiverId: message.receiverId,
            mediaUrl: message.mediaUrl,
            mediaSize: message.mediaSize,
            mediaDuration: message.mediaDuration
          })
          
          this.messages.push(message)
          this.scrollToBottom()
        }).catch(() => {
          uni.hideLoading()
          uni.showToast({
            title: '语音上传失败',
            icon: 'none'
          })
        })
      })
      
      recorderManager.start({
        duration: 60000, // 最长录音时间，单位ms
        format: 'mp3'
      })
      
      // 点击停止录音
      uni.showModal({
        title: '录音中',
        content: '点击确定停止录音',
        success: (res) => {
          if (res.confirm) {
            recorderManager.stop()
          }
        }
      })
    },
    
    viewBusinessCard() {
      uni.navigateTo({
        url: `/pages/card/detail?id=${this.contactInfo.id}`
      })
    },
    
    addToContacts() {
      uni.showModal({
        title: '添加联系人',
        content: `确定要将 ${this.contactInfo.name} 添加到通讯录吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '添加成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    clearChatHistory() {
      uni.showModal({
        title: '清空聊天记录',
        content: '确定要清空与该联系人的所有聊天记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.messages = []
            uni.showToast({
              title: '清空成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.messages.length > 0) {
          this.scrollIntoView = `msg-${this.messages.length - 1}`
        }
      })
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (date.toDateString() === now.toDateString()) { // 今天
        return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      } else {
        return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.contact-company {
  font-size: 12px;
  color: #666666;
}

.more-icon {
  width: 40rpx;
  height: 40rpx;
}

.chat-content {
  flex: 1;
  padding: 24rpx 32rpx;
}

.system-message {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.system-text {
  padding: 12rpx 24rpx;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
  font-size: 12px;
  color: #666666;
}

.message-item {
  margin-bottom: 32rpx;
}

.message-received {
  display: flex;
  align-items: flex-start;
}

.message-sent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.avatar {
  width: 72rpx;
  height: 72rpx;
  margin: 0 16rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 36rpx;
}

.message-content {
  max-width: 480rpx;
  display: flex;
  flex-direction: column;
}

.message-sent .message-content {
  align-items: flex-end;
}

.message-bubble {
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 8rpx;
}

.received-bubble {
  background-color: #ffffff;
  border-top-left-radius: 4rpx;
}

.sent-bubble {
  background-color: #2B85E4;
  border-top-right-radius: 4rpx;
}

.message-text {
  font-size: 16px;
  line-height: 1.4;
  color: #333333;
}

.sent-bubble .message-text {
  color: #FFFFFF;
}

.message-image {
  max-width: 400rpx;
  max-height: 400rpx;
  border-radius: 8rpx;
}

.voice-message {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.voice-duration {
  margin-left: 16rpx;
  font-size: 14px;
  color: #333333;
}

.voice-duration.white {
  color: #FFFFFF;
}

.message-time {
  font-size: 12px;
  color: #999999;
}

.input-area {
  background-color: #ffffff;
  border-top: 1rpx solid #E9ECEF;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

.input-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.toolbar-icon {
  margin-right: 32rpx;
}

.input-box {
  display: flex;
  align-items: center;
}

.message-input {
  flex: 1;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #f8f9fa;
  border-radius: 36rpx;
  font-size: 16px;
  margin-right: 16rpx;
}

.send-btn {
  width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #E9ECEF;
  border-radius: 36rpx;
  transition: all 0.3s;
}

.send-btn.active {
  background-color: #2B85E4;
}

.send-text {
  font-size: 16px;
  color: #999999;
}

.send-btn.active .send-text {
  color: #FFFFFF;
}
</style>