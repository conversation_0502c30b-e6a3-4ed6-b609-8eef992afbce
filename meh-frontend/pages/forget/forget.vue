<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">忘记密码</text>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="app-name">MEH 电子名片</text>
        <text class="subtitle">找回您的账户密码</text>
      </view>

      <!-- 步骤指示器 -->
      <view class="step-indicator">
        <view class="step-item" :class="{active: currentStep >= 1, completed: currentStep > 1}">
          <view class="step-number">1</view>
          <text class="step-text">验证手机</text>
        </view>
        <view class="step-line" :class="{active: currentStep > 1}"></view>
        <view class="step-item" :class="{active: currentStep >= 2, completed: currentStep > 2}">
          <view class="step-number">2</view>
          <text class="step-text">重置密码</text>
        </view>
        <view class="step-line" :class="{active: currentStep > 2}"></view>
        <view class="step-item" :class="{active: currentStep >= 3}">
          <view class="step-number">3</view>
          <text class="step-text">完成</text>
        </view>
      </view>

      <!-- 第一步：验证手机号 -->
      <view class="form-section" v-if="currentStep === 1">
        <view class="form-title">验证手机号</view>
        <view class="form-subtitle">请输入您注册时使用的手机号</view>

        <view class="form-item">
          <view class="input-group">
            <uni-icons type="phone" size="20" color="#cccccc"></uni-icons>
            <input class="input" v-model="phone" placeholder="请输入手机号" type="number" maxlength="11" />
          </view>
        </view>

        <view class="form-item">
          <view class="input-group">
            <uni-icons type="chatboxes" size="20" color="#cccccc"></uni-icons>
            <input class="input" v-model="verifyCode" placeholder="请输入验证码" type="number" maxlength="6" />
            <button class="code-btn" :disabled="codeDisabled" @click="sendCode">
              {{ codeText }}
            </button>
          </view>
        </view>

        <button class="submit-btn" :disabled="!canSubmitStep1" @click="verifyPhone">
          下一步
        </button>
      </view>

      <!-- 第二步：重置密码 -->
      <view class="form-section" v-if="currentStep === 2">
        <view class="form-title">设置新密码</view>
        <view class="form-subtitle">请设置您的新密码</view>

        <view class="form-item">
          <view class="input-group">
            <uni-icons type="locked" size="20" color="#cccccc"></uni-icons>
            <input class="input" v-model="newPassword" placeholder="请输入新密码" :password="!showPassword" />
            <uni-icons
              class="eye-icon"
              :type="showPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#cccccc"
              @click="togglePassword"
            ></uni-icons>
          </view>
        </view>

        <view class="form-item">
          <view class="input-group">
            <uni-icons type="locked" size="20" color="#cccccc"></uni-icons>
            <input class="input" v-model="confirmPassword" placeholder="请确认新密码" :password="!showConfirmPassword" />
            <uni-icons
              class="eye-icon"
              :type="showConfirmPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#cccccc"
              @click="toggleConfirmPassword"
            ></uni-icons>
          </view>
        </view>

        <!-- 密码强度提示 -->
        <view class="password-strength">
          <text class="strength-text">密码强度：</text>
          <view class="strength-bar">
            <view class="strength-item" :class="{active: passwordStrength >= 1}"></view>
            <view class="strength-item" :class="{active: passwordStrength >= 2}"></view>
            <view class="strength-item" :class="{active: passwordStrength >= 3}"></view>
          </view>
          <text class="strength-label">{{ strengthText }}</text>
        </view>

        <button class="submit-btn" :disabled="!canSubmitStep2" @click="resetPassword">
          重置密码
        </button>
      </view>

      <!-- 第三步：完成 -->
      <view class="success-section" v-if="currentStep === 3">
        <view class="success-icon">
          <uni-icons type="checkmarkempty" size="60" color="#52c41a"></uni-icons>
        </view>
        <view class="success-title">密码重置成功</view>
        <view class="success-subtitle">您的密码已成功重置，请使用新密码登录</view>

        <button class="submit-btn" @click="goToLogin">
          立即登录
        </button>
      </view>

      <!-- 底部链接 -->
      <view class="footer-links" v-if="currentStep < 3">
        <text class="link-text" @click="goToLogin">返回登录</text>
        <text class="link-text" @click="goToRegister">没有账号？立即注册</text>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/api/index.js'

export default {
  data() {
    return {
      currentStep: 1,
      phone: '',
      verifyCode: '',
      newPassword: '',
      confirmPassword: '',
      showPassword: false,
      showConfirmPassword: false,
      codeDisabled: false,
      codeText: '获取验证码',
      countdown: 60,
      timer: null
    }
  },

  computed: {
    // 第一步是否可提交
    canSubmitStep1() {
      return this.phone.length === 11 && this.verifyCode.length === 6
    },

    // 第二步是否可提交
    canSubmitStep2() {
      return this.newPassword.length >= 6 &&
             this.confirmPassword.length >= 6 &&
             this.newPassword === this.confirmPassword
    },

    // 密码强度
    passwordStrength() {
      if (!this.newPassword) return 0

      let strength = 0
      if (this.newPassword.length >= 6) strength++
      if (/[a-zA-Z]/.test(this.newPassword) && /[0-9]/.test(this.newPassword)) strength++
      if (/[^a-zA-Z0-9]/.test(this.newPassword)) strength++

      return strength
    },

    // 密码强度文本
    strengthText() {
      const texts = ['弱', '中', '强']
      return texts[this.passwordStrength - 1] || '弱'
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      if (this.currentStep > 1) {
        this.currentStep--
      } else {
        uni.navigateBack()
      }
    },

    // 发送验证码
    async sendCode() {
      if (!this.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!/^1[3-9]\d{9}$/.test(this.phone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }

      try {
        const res = await api.post('/auth/send-reset-code', {
          phone: this.phone
        })

        if (res.success) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          })

          this.startCountdown()
        } else {
          uni.showToast({
            title: res.message || '发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    },

    // 开始倒计时
    startCountdown() {
      this.codeDisabled = true
      this.countdown = 60
      this.codeText = `${this.countdown}s`

      this.timer = setInterval(() => {
        this.countdown--
        this.codeText = `${this.countdown}s`

        if (this.countdown <= 0) {
          this.clearCountdown()
        }
      }, 1000)
    },

    // 清除倒计时
    clearCountdown() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.codeDisabled = false
      this.codeText = '获取验证码'
    },

    // 验证手机号
    async verifyPhone() {
      try {
        uni.showLoading({ title: '验证中...' })

        const res = await api.post('/auth/verify-reset-code', {
          phone: this.phone,
          code: this.verifyCode
        })

        if (res.success) {
          this.currentStep = 2
        } else {
          uni.showToast({
            title: res.message || '验证失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('验证失败:', error)
        uni.showToast({
          title: '验证失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 重置密码
    async resetPassword() {
      try {
        uni.showLoading({ title: '重置中...' })

        const res = await api.post('/auth/reset-password', {
          phone: this.phone,
          code: this.verifyCode,
          newPassword: this.newPassword
        })

        if (res.success) {
          this.currentStep = 3
        } else {
          uni.showToast({
            title: res.message || '重置失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('重置失败:', error)
        uni.showToast({
          title: '重置失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    // 跳转到登录
    goToLogin() {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    },

    // 跳转到注册
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      })
    }
  },

  onUnload() {
    this.clearCountdown()
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: transparent;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-left: 20rpx;
}

/* 内容区域 */
.content {
  padding: 40rpx 60rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step-item.active {
  opacity: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  transition: background-color 0.3s;
}

.step-item.active .step-number {
  background-color: #ffffff;
  color: #667eea;
}

.step-item.completed .step-number {
  background-color: #52c41a;
  color: #ffffff;
}

.step-text {
  font-size: 24rpx;
  color: #ffffff;
}

.step-line {
  width: 80rpx;
  height: 2rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
  transition: background-color 0.3s;
}

.step-line.active {
  background-color: #ffffff;
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
}

.form-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  text-align: center;
  margin-bottom: 16rpx;
}

.form-subtitle {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.input-group {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 100rpx;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  margin-left: 20rpx;
}

.code-btn {
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.code-btn:disabled {
  background-color: #cccccc;
}

.eye-icon {
  padding: 10rpx;
}

.submit-btn {
  width: 100%;
  height: 100rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-top: 20rpx;
}

.submit-btn:disabled {
  background-color: #cccccc;
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.strength-text {
  font-size: 24rpx;
  color: #666666;
  margin-right: 16rpx;
}

.strength-bar {
  display: flex;
  gap: 8rpx;
  margin-right: 16rpx;
}

.strength-item {
  width: 40rpx;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  transition: background-color 0.3s;
}

.strength-item.active {
  background-color: #52c41a;
}

.strength-label {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 成功页面 */
.success-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #f6ffed;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx auto;
}

.success-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 20rpx;
}

.success-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 60rpx;
}

/* 底部链接 */
.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
}

.link-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}
</style>
