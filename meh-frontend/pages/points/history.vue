<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">兑换历史</text>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view class="tab-list">
        <view
          v-for="(tab, index) in filterTabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTab === tab.value }"
          @click="switchTab(tab.value)"
        >
          <text class="tab-text">{{ tab.label }}</text>
        </view>
      </view>
    </view>

    <!-- 兑换记录列表 -->
    <scroll-view class="records-list" scroll-y @scrolltolower="loadMore">
      <template v-if="filteredRecords.length > 0">
        <view v-for="(record, index) in filteredRecords" :key="index" class="record-item" @click="viewDetail(record)">
          <view class="record-image">
            <image class="image" :src="record.goodsImage" mode="aspectFill"></image>
          </view>
          <view class="record-info">
            <view class="record-title">{{ record.goodsName }}</view>
            <view class="record-meta">
              <text class="quantity">数量：{{ record.quantity }}</text>
              <text class="points">{{ record.totalPoints }}积分</text>
            </view>
            <view class="record-time">{{ formatTime(record.createTime) }}</view>
          </view>
          <view class="record-status">
            <view class="status-badge" :class="getStatusClass(record.status)">
              <text class="status-text">{{ getStatusText(record.status) }}</text>
            </view>
            <uni-icons class="arrow-icon" type="right" size="16" color="#cccccc"></uni-icons>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">暂无兑换记录</text>
          <text class="empty-desc">快去积分商城兑换商品吧</text>
          <view class="go-mall-btn" @click="goToMall">
            <text class="btn-text">去逛逛</text>
          </view>
        </view>
      </template>

      <!-- 加载更多 -->
      <view v-if="hasMore && filteredRecords.length > 0" class="load-more">
        <text class="load-text">{{ loading ? '加载中...' : '上拉加载更多' }}</text>
      </view>

      <view v-if="!hasMore && filteredRecords.length > 0" class="no-more">
        <text class="no-more-text">没有更多了</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { api } from '@/api/index.js'
import { config } from '@/config/index.js'

export default {
  data() {
    return {
      activeTab: 'all',
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '待发货', value: 'pending' },
        { label: '已发货', value: 'shipped' },
        { label: '已完成', value: 'completed' },
        { label: '已取消', value: 'cancelled' }
      ],
      records: [],
      loading: false,
      hasMore: true,
      page: 1,
      pageSize: 10,
      emptyImageUrl: config.defaultAvatar
    }
  },

  computed: {
    filteredRecords() {
      if (this.activeTab === 'all') {
        return this.records
      }
      return this.records.filter(record => this.getStatusValue(record.status) === this.activeTab)
    }
  },

  onLoad() {
    this.loadRecords()
  },

  onPullDownRefresh() {
    this.refreshRecords()
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    switchTab(value) {
      this.activeTab = value
    },

    async loadRecords(isRefresh = false) {
      if (this.loading) return

      this.loading = true

      try {
        const res = await api.get('/points/exchange/history', {
          page: isRefresh ? 1 : this.page,
          pageSize: this.pageSize
        })

        if (res.code === 200) {
          const newRecords = res.data.records || []

          if (isRefresh) {
            this.records = newRecords
            this.page = 1
          } else {
            this.records = [...this.records, ...newRecords]
          }

          this.hasMore = newRecords.length === this.pageSize
          this.page++
        } else {
          uni.showToast({
            title: res.message || '获取记录失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取兑换记录失败:', error)
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        })
      } finally {
        this.loading = false
        if (isRefresh) {
          uni.stopPullDownRefresh()
        }
      }
    },

    async refreshRecords() {
      this.page = 1
      this.hasMore = true
      await this.loadRecords(true)
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadRecords()
      }
    },

    viewDetail(record) {
      uni.navigateTo({
        url: `/pages/points/exchange-detail?id=${record.id}`
      })
    },

    goToMall() {
      uni.navigateTo({
        url: '/pages/points/mall'
      })
    },

    getStatusClass(status) {
      const statusMap = {
        0: 'pending',
        1: 'shipped',
        2: 'completed',
        3: 'cancelled'
      }
      return statusMap[status] || 'pending'
    },

    getStatusText(status) {
      const statusMap = {
        0: '待发货',
        1: '已发货',
        2: '已完成',
        3: '已取消'
      }
      return statusMap[status] || '待发货'
    },

    getStatusValue(status) {
      const statusMap = {
        0: 'pending',
        1: 'shipped',
        2: 'completed',
        3: 'cancelled'
      }
      return statusMap[status] || 'pending'
    },

    formatTime(timeStr) {
      if (!timeStr) return ''

      const time = new Date(timeStr)
      const now = new Date()
      const diff = now.getTime() - time.getTime()

      const minute = 60 * 1000
      const hour = 60 * minute
      const day = 24 * hour

      if (diff < minute) {
        return '刚刚'
      } else if (diff < hour) {
        return Math.floor(diff / minute) + '分钟前'
      } else if (diff < day) {
        return Math.floor(diff / hour) + '小时前'
      } else if (diff < 7 * day) {
        return Math.floor(diff / day) + '天前'
      } else {
        return time.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 筛选标签 */
.filter-tabs {
  background-color: #ffffff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-list {
  display: flex;
  padding: 0 30rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  position: relative;
}

.tab-item.active .tab-text {
  color: #ff6b35;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #ff6b35;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #666666;
  transition: all 0.3s;
}

/* 记录列表 */
.records-list {
  height: calc(100vh - 200rpx);
  padding: 20rpx;
}

.record-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.record-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.image {
  width: 100%;
  height: 100%;
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.record-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.quantity {
  font-size: 26rpx;
  color: #666666;
}

.points {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 600;
}

.record-time {
  font-size: 24rpx;
  color: #999999;
}

.record-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  flex-shrink: 0;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.status-badge.pending {
  background-color: #fff3e0;
  color: #ff9800;
}

.status-badge.shipped {
  background-color: #e3f2fd;
  color: #2196f3;
}

.status-badge.completed {
  background-color: #e8f5e8;
  color: #4caf50;
}

.status-badge.cancelled {
  background-color: #ffebee;
  color: #f44336;
}

.status-text {
  font-size: 22rpx;
}

.arrow-icon {
  margin-top: 10rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

.go-mall-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 600;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx;
}

.load-text {
  font-size: 26rpx;
  color: #999999;
}

.no-more {
  text-align: center;
  padding: 30rpx;
}

.no-more-text {
  font-size: 26rpx;
  color: #cccccc;
}
</style>
