<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">积分商城</text>
      <uni-icons class="search-icon" type="search" size="20" color="#333333" @click="showSearch"></uni-icons>
    </view>

    <!-- 积分信息 -->
    <view class="points-section">
      <view class="points-card">
        <view class="points-info">
          <text class="points-label">我的积分</text>
          <text class="points-value">{{ userPoints }}</text>
        </view>
        <view class="checkin-btn" @click="dailyCheckin">
          <uni-icons class="checkin-icon" type="gift" size="16" color="#FFFFFF"></uni-icons>
          <text class="checkin-text">签到赚积分</text>
        </view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <scroll-view class="tabs-scroll" scroll-x>
        <view class="tab-list">
          <view 
            v-for="(category, index) in categories" 
            :key="index" 
            class="tab-item" 
            :class="{ active: activeCategory === category.id }"
            @click="switchCategory(category.id)"
          >
            <text class="tab-text">{{ category.name }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 商品列表 -->
    <scroll-view class="goods-list" scroll-y>
      <template v-if="filteredGoods.length > 0">
        <view class="goods-grid">
          <view 
            v-for="(goods, index) in filteredGoods" 
            :key="index" 
            class="goods-item" 
            @click="viewGoodsDetail(goods)"
          >
            <view class="goods-image">
              <image class="image" :src="goods.image" mode="aspectFill"></image>
              <view class="hot-tag" v-if="goods.isHot">
                <text class="hot-text">热门</text>
              </view>
            </view>
            <view class="goods-info">
              <text class="goods-title">{{ goods.title }}</text>
              <view class="goods-meta">
                <view class="points-price">
                  <text class="points-text">{{ goods.points }}</text>
                  <text class="points-unit">积分</text>
                </view>
                <text class="exchange-count">已兑{{ goods.exchangeCount }}件</text>
              </view>
              <view class="exchange-btn" @click.stop="exchangeGoods(goods)">
                <text class="exchange-text">立即兑换</text>
              </view>
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">暂无商品</text>
          <text class="empty-desc">敬请期待更多精彩商品</text>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userPoints: 1580,
      activeCategory: 0,
      categories: [
        { id: 0, name: '全部' },
        { id: 1, name: '数码产品' },
        { id: 2, name: '生活用品' },
        { id: 3, name: '美食饮品' },
        { id: 4, name: '图书文具' },
        { id: 5, name: '服装配饰' }
      ],
      goods: [
        {
          id: 1,
          title: 'AirPods Pro 无线耳机',
          image: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          points: 2999,
          exchangeCount: 128,
          categoryId: 1,
          isHot: true,
          description: '主动降噪，沉浸式音效体验'
        },
        {
          id: 2,
          title: '小米充电宝 20000mAh',
          image: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          points: 299,
          exchangeCount: 256,
          categoryId: 1,
          isHot: false,
          description: '大容量快充，出行必备'
        },
        {
          id: 3,
          title: '保温杯 316不锈钢',
          image: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
          points: 199,
          exchangeCount: 89,
          categoryId: 2,
          isHot: false,
          description: '24小时保温，健康饮水'
        },
        {
          id: 4,
          title: '星巴克咖啡券',
          image: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          points: 88,
          exchangeCount: 512,
          categoryId: 3,
          isHot: true,
          description: '任意门店通用，有效期一年'
        },
        {
          id: 5,
          title: '得力文具套装',
          image: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          points: 158,
          exchangeCount: 76,
          categoryId: 4,
          isHot: false,
          description: '办公学习必备，品质保证'
        },
        {
          id: 6,
          title: '时尚帆布包',
          image: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
          points: 388,
          exchangeCount: 43,
          categoryId: 5,
          isHot: false,
          description: '简约设计，百搭时尚'
        }
      ],
      emptyImageUrl: 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg'
    }
  },
  computed: {
    filteredGoods() {
      if (this.activeCategory === 0) {
        return this.goods
      }
      return this.goods.filter(goods => goods.categoryId === this.activeCategory)
    }
  },
  onLoad() {
    this.loadUserPoints()
    this.loadGoods()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    showSearch() {
      uni.showToast({
        title: '搜索功能开发中',
        icon: 'none'
      })
    },
    
    loadUserPoints() {
      // 调用PointsController.getMyPoints方法获取用户积分信息
      this.$api.get('/points/my').then(res => {
        if (res && res.points) {
          this.userPoints = res.points;
        }
      }).catch(err => {
        console.error('获取积分信息失败', err);
        uni.showToast({
          title: '获取积分信息失败',
          icon: 'none'
        });
      });
    },
    
    loadGoods() {
      // 调用积分商品列表接口获取商品数据
      this.$api.get('/points/goods', {
        categoryId: this.activeCategory !== 0 ? this.activeCategory : undefined
      }).then(res => {
        if (res && Array.isArray(res)) {
          this.goods = res.map(item => ({
            id: item.id,
            title: item.name,
            image: item.image || this.emptyImageUrl,
            points: item.points,
            exchangeCount: item.exchangeCount || 0,
            categoryId: item.categoryId,
            isHot: item.isHot || false,
            description: item.description || ''
          }));
        }
      }).catch(err => {
        console.error('获取积分商品列表失败', err);
        // 保留模拟数据用于展示
      });
    },
    
    dailyCheckin() {
      uni.showModal({
        title: '每日签到',
        content: '签到可获得10积分，确定要签到吗？',
        success: (res) => {
          if (res.confirm) {
            // 调用PointsController.dailyCheckin方法进行签到
            this.$api.post('/points/checkin').then(res => {
              // 签到成功后重新获取积分信息
              this.loadUserPoints();

              uni.showToast({
                title: res.message || '签到成功',
                icon: 'success'
              });
            }).catch(err => {
              console.error('签到失败', err);
              uni.showToast({
                title: '签到失败，请稍后重试',
                icon: 'none'
              });
            });
          }
        }
      })
    },
    
    switchCategory(categoryId) {
      this.activeCategory = categoryId
    },
    
    viewGoodsDetail(goods) {
      uni.navigateTo({
        url: `/pages/points/detail?id=${goods.id}`
      })
    },
    
    exchangeGoods(goods) {
      if (this.userPoints < goods.points) {
        uni.showToast({
          title: '积分不足',
          icon: 'error'
        })
        return
      }
      
      uni.showModal({
        title: '确认兑换',
        content: `确定要用${goods.points}积分兑换「${goods.title}」吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用积分兑换接口
            this.$api.post('/points/exchange', {
              goodsId: goods.id,
              quantity: 1
            }).then(res => {
              // 兑换成功后重新获取积分信息
              this.loadUserPoints();
              // 更新商品兑换数量
              goods.exchangeCount += 1;

              uni.showToast({
                title: '兑换成功',
                icon: 'success'
              });

              // 跳转到兑换详情页
              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/points/exchange?id=${res.id || ''}`
                });
              }, 1500);
            }).catch(err => {
              console.error('兑换失败', err);
              uni.showToast({
                title: '兑换失败，请稍后重试',
                icon: 'none'
              });
            });
          }
        }
      })
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
}

.points-section {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.points-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #2B85E4 0%, #1E6FCC 100%);
  border-radius: 16rpx;
}

.points-info {
  display: flex;
  flex-direction: column;
}

.points-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 28px;
  font-weight: 600;
  color: #FFFFFF;
}

.checkin-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.checkin-icon {
  margin-right: 8rpx;
}

.checkin-text {
  font-size: 14px;
  color: #FFFFFF;
}

.category-tabs {
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.tabs-scroll {
  white-space: nowrap;
}

.tab-list {
  display: flex;
  padding: 0 32rpx;
}

.tab-item {
  flex-shrink: 0;
  padding: 24rpx 32rpx;
  margin-right: 16rpx;
  border-radius: 24rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #2B85E4;
}

.tab-text {
  font-size: 14px;
  color: #666666;
  white-space: nowrap;
}

.tab-item.active .tab-text {
  color: #FFFFFF;
}

.goods-list {
  flex: 1;
  padding: 24rpx 32rpx;
}

.goods-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.goods-item {
  width: calc(50% - 12rpx);
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.goods-image {
  position: relative;
  width: 100%;
  height: 240rpx;
}

.image {
  width: 100%;
  height: 100%;
}

.hot-tag {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  padding: 4rpx 12rpx;
  background-color: #FF4757;
  border-radius: 12rpx;
}

.hot-text {
  font-size: 10px;
  color: #FFFFFF;
}

.goods-info {
  padding: 24rpx;
}

.goods-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.goods-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.points-price {
  display: flex;
  align-items: baseline;
}

.points-text {
  font-size: 18px;
  font-weight: 600;
  color: #FF4757;
}

.points-unit {
  font-size: 12px;
  color: #FF4757;
  margin-left: 4rpx;
}

.exchange-count {
  font-size: 12px;
  color: #999999;
}

.exchange-btn {
  width: 100%;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2B85E4;
  border-radius: 8rpx;
}

.exchange-text {
  font-size: 14px;
  color: #FFFFFF;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
}
</style>