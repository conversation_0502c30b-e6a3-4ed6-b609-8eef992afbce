<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">积分兑换</text>
    </view>

    <!-- 商品信息 -->
    <view class="goods-section">
      <view class="goods-card">
        <view class="goods-image">
          <image class="image" :src="goodsInfo.image" mode="aspectFill"></image>
        </view>
        <view class="goods-info">
          <text class="goods-title">{{ goodsInfo.name }}</text>
          <text class="goods-desc">{{ goodsInfo.description }}</text>
          <view class="goods-meta">
            <view class="points-price">
              <text class="points-text">{{ goodsInfo.points }}</text>
              <text class="points-unit">积分</text>
            </view>
            <text class="stock-info">库存：{{ goodsInfo.stock }}件</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 兑换数量 -->
    <view class="quantity-section">
      <view class="section-title">
        <text class="title-text">兑换数量</text>
      </view>
      <view class="quantity-selector">
        <view class="quantity-btn" :class="{ disabled: quantity <= 1 }" @click="decreaseQuantity">
          <uni-icons type="minus" size="16" color="#666666"></uni-icons>
        </view>
        <input class="quantity-input" type="number" v-model="quantity" @input="onQuantityChange" />
        <view class="quantity-btn" :class="{ disabled: quantity >= goodsInfo.stock }" @click="increaseQuantity">
          <uni-icons type="plus" size="16" color="#666666"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 积分信息 -->
    <view class="points-section">
      <view class="points-item">
        <text class="points-label">我的积分</text>
        <text class="points-value">{{ userPoints }}</text>
      </view>
      <view class="points-item">
        <text class="points-label">需要积分</text>
        <text class="points-value need">{{ totalPoints }}</text>
      </view>
      <view class="points-item">
        <text class="points-label">剩余积分</text>
        <text class="points-value" :class="{ insufficient: remainingPoints < 0 }">{{ remainingPoints }}</text>
      </view>
    </view>

    <!-- 兑换须知 -->
    <view class="notice-section">
      <view class="section-title">
        <text class="title-text">兑换须知</text>
      </view>
      <view class="notice-list">
        <text class="notice-item">• 积分兑换后不可退换，请谨慎选择</text>
        <text class="notice-item">• 商品将在3-7个工作日内发货</text>
        <text class="notice-item">• 如有质量问题，请联系客服处理</text>
        <text class="notice-item">• 兑换记录可在积分历史中查看</text>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="total-info">
        <text class="total-label">总计：</text>
        <text class="total-points">{{ totalPoints }}积分</text>
      </view>
      <view class="exchange-btn" :class="{ disabled: !canExchange }" @click="confirmExchange">
        <text class="exchange-text">{{ canExchange ? '确认兑换' : '积分不足' }}</text>
      </view>
    </view>

    <!-- 确认弹窗 -->
    <uni-popup ref="confirmPopup" type="dialog">
      <uni-popup-dialog
        type="info"
        title="确认兑换"
        :content="confirmContent"
        @confirm="handleExchange"
        @close="closeConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { api } from '@/api/index.js'
import { config } from '@/config/index.js'

export default {
  data() {
    return {
      goodsId: null,
      quantity: 1,
      userPoints: 0,
      goodsInfo: {
        id: null,
        name: '',
        description: '',
        image: '',
        points: 0,
        stock: 0
      },
      loading: false
    }
  },

  computed: {
    totalPoints() {
      return this.goodsInfo.points * this.quantity
    },

    remainingPoints() {
      return this.userPoints - this.totalPoints
    },

    canExchange() {
      return this.remainingPoints >= 0 && this.quantity > 0 && this.quantity <= this.goodsInfo.stock
    },

    confirmContent() {
      return `确认兑换 ${this.goodsInfo.name} × ${this.quantity}？\n将消耗 ${this.totalPoints} 积分`
    }
  },

  onLoad(options) {
    if (options.goodsId) {
      this.goodsId = parseInt(options.goodsId)
      this.loadGoodsInfo()
    }
    this.loadUserPoints()
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadGoodsInfo() {
      try {
        const res = await api.get(`/points/goods/${this.goodsId}`)
        if (res.code === 200) {
          this.goodsInfo = res.data
        } else {
          uni.showToast({
            title: res.message || '获取商品信息失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取商品信息失败:', error)
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    },

    async loadUserPoints() {
      try {
        const res = await api.get('/user/points')
        if (res.code === 200) {
          this.userPoints = res.data.points || 0
        }
      } catch (error) {
        console.error('获取用户积分失败:', error)
      }
    },

    decreaseQuantity() {
      if (this.quantity > 1) {
        this.quantity--
      }
    },

    increaseQuantity() {
      if (this.quantity < this.goodsInfo.stock) {
        this.quantity++
      }
    },

    onQuantityChange(e) {
      let value = parseInt(e.detail.value) || 1
      if (value < 1) value = 1
      if (value > this.goodsInfo.stock) value = this.goodsInfo.stock
      this.quantity = value
    },

    confirmExchange() {
      if (!this.canExchange) return

      this.$refs.confirmPopup.open()
    },

    closeConfirm() {
      this.$refs.confirmPopup.close()
    },

    async handleExchange() {
      if (this.loading) return

      this.loading = true

      try {
        const res = await api.post('/points/exchange', {
          goodsId: this.goodsId,
          quantity: this.quantity
        })

        if (res.code === 200) {
          uni.showToast({
            title: '兑换成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/points/history'
            })
          }, 1500)
        } else {
          uni.showToast({
            title: res.message || '兑换失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('兑换失败:', error)
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.closeConfirm()
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

/* 商品信息 */
.goods-section {
  margin: 20rpx;
}

.goods-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.goods-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.goods-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

.goods-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.goods-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.points-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.points-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
}

.points-unit {
  font-size: 24rpx;
  color: #ff6b35;
}

.stock-info {
  font-size: 24rpx;
  color: #999999;
}

/* 数量选择 */
.quantity-section {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
}

.quantity-btn.disabled {
  opacity: 0.5;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  font-size: 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
}

/* 积分信息 */
.points-section {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.points-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.points-item:last-child {
  border-bottom: none;
}

.points-label {
  font-size: 28rpx;
  color: #666666;
}

.points-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.points-value.need {
  color: #ff6b35;
}

.points-value.insufficient {
  color: #ff4757;
}

/* 兑换须知 */
.notice-section {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notice-item {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.total-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.total-label {
  font-size: 28rpx;
  color: #666666;
}

.total-points {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff6b35;
}

.exchange-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  min-width: 200rpx;
  text-align: center;
}

.exchange-btn.disabled {
  background: #cccccc;
}

.exchange-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
</style>
