<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">积分明细</text>
    </view>

    <!-- 积分概览 -->
    <view class="points-overview">
      <view class="overview-card">
        <view class="current-points">
          <text class="points-label">当前积分</text>
          <text class="points-value">{{ currentPoints }}</text>
        </view>
        <view class="points-stats">
          <view class="stat-item">
            <text class="stat-value">+{{ totalEarned }}</text>
            <text class="stat-label">累计获得</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">-{{ totalSpent }}</text>
            <text class="stat-label">累计消费</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view class="tab-list">
        <view 
          v-for="(tab, index) in filterTabs" 
          :key="index" 
          class="tab-item" 
          :class="{ active: activeTab === tab.value }"
          @click="switchTab(tab.value)"
        >
          <text class="tab-text">{{ tab.label }}</text>
        </view>
      </view>
    </view>

    <!-- 积分记录列表 -->
    <scroll-view class="records-list" scroll-y>
      <template v-if="filteredRecords.length > 0">
        <view v-for="(record, index) in filteredRecords" :key="index" class="record-item">
          <view class="record-icon">
            <uni-icons 
              :type="getRecordIcon(record.type)" 
              size="20" 
              :color="getRecordColor(record.type)"
            ></uni-icons>
          </view>
          <view class="record-info">
            <view class="record-title">{{ record.title }}</view>
            <view class="record-desc">{{ record.description }}</view>
            <view class="record-time">{{ formatTime(record.createTime) }}</view>
          </view>
          <view class="record-points" :class="{ earn: record.type === 'earn', spend: record.type === 'spend' }">
            <text class="points-text">{{ record.type === 'earn' ? '+' : '-' }}{{ record.points }}</text>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">暂无积分记录</text>
          <text class="empty-desc">快去完成任务赚取积分吧</text>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentPoints: 1580,
      totalEarned: 3250,
      totalSpent: 1670,
      activeTab: 'all',
      filterTabs: [
        { label: '全部', value: 'all' },
        { label: '获得', value: 'earn' },
        { label: '消费', value: 'spend' }
      ],
      records: [
        {
          id: 1,
          type: 'earn',
          title: '每日签到',
          description: '连续签到第3天',
          points: 10,
          createTime: '2024-01-15 09:00:00'
        },
        {
          id: 2,
          type: 'spend',
          title: '兑换商品',
          description: '星巴克咖啡券',
          points: 88,
          createTime: '2024-01-14 16:30:00'
        },
        {
          id: 3,
          type: 'earn',
          title: '完善资料',
          description: '完善个人名片信息',
          points: 50,
          createTime: '2024-01-14 14:20:00'
        },
        {
          id: 4,
          type: 'earn',
          title: '分享名片',
          description: '分享名片给好友',
          points: 20,
          createTime: '2024-01-14 11:15:00'
        },
        {
          id: 5,
          type: 'spend',
          title: '兑换商品',
          description: '小米充电宝 20000mAh',
          points: 299,
          createTime: '2024-01-13 15:45:00'
        },
        {
          id: 6,
          type: 'earn',
          title: '每日签到',
          description: '连续签到第2天',
          points: 10,
          createTime: '2024-01-13 08:30:00'
        },
        {
          id: 7,
          type: 'earn',
          title: '新用户注册',
          description: '欢迎加入MEH名片',
          points: 100,
          createTime: '2024-01-12 10:00:00'
        },
        {
          id: 8,
          type: 'earn',
          title: '创建名片',
          description: '创建第一张名片',
          points: 30,
          createTime: '2024-01-12 10:30:00'
        }
      ],
      emptyImageUrl: 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg'
    }
  },
  computed: {
    filteredRecords() {
      if (this.activeTab === 'all') {
        return this.records
      }
      return this.records.filter(record => record.type === this.activeTab)
    }
  },
  onLoad() {
    this.loadPointsDetail()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    loadPointsDetail() {
      // 调用API获取积分明细
      // this.$api.points.getDetail().then(res => {
      //   this.currentPoints = res.data.currentPoints
      //   this.totalEarned = res.data.totalEarned
      //   this.totalSpent = res.data.totalSpent
      //   this.records = res.data.records
      // })
    },
    
    switchTab(tabValue) {
      this.activeTab = tabValue
    },
    
    getRecordIcon(type) {
      const iconMap = {
        earn: 'plus-filled',
        spend: 'minus-filled'
      }
      return iconMap[type] || 'circle'
    },
    
    getRecordColor(type) {
      const colorMap = {
        earn: '#52C41A',
        spend: '#FF4757'
      }
      return colorMap[type] || '#666666'
    },
    
    formatTime(timeStr) {
      const now = new Date()
      const recordTime = new Date(timeStr)
      const diff = now.getTime() - recordTime.getTime()
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) {
        return '刚刚'
      } else if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        const date = new Date(timeStr)
        return `${date.getMonth() + 1}-${date.getDate()}`
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.points-overview {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.overview-card {
  padding: 32rpx;
  background: linear-gradient(135deg, #2B85E4 0%, #1E6FCC 100%);
  border-radius: 16rpx;
}

.current-points {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
}

.points-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.points-value {
  font-size: 36px;
  font-weight: 600;
  color: #FFFFFF;
}

.points-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.filter-tabs {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.tab-list {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 4rpx;
}

.tab-item {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  transition: all 0.3s;
}

.tab-item.active {
  background-color: #FFFFFF;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-text {
  font-size: 14px;
  color: #666666;
}

.tab-item.active .tab-text {
  color: #2B85E4;
  font-weight: 500;
}

.records-list {
  flex: 1;
  padding: 0 32rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.record-item:last-child {
  margin-bottom: 0;
}

.record-icon {
  width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 36rpx;
  margin-right: 24rpx;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.record-desc {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: 12px;
  color: #999999;
}

.record-points {
  display: flex;
  align-items: center;
}

.record-points.earn .points-text {
  color: #52C41A;
}

.record-points.spend .points-text {
  color: #FF4757;
}

.points-text {
  font-size: 18px;
  font-weight: 600;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
}
</style>