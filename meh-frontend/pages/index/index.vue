<template>
  <view class="container">
    <!-- 启动页 -->
    <view class="splash" v-if="showSplash">
      <image class="splash-logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <view class="splash-title">MEH电子名片</view>
      <view class="splash-subtitle">连接你我，共创未来</view>
    </view>
    
    <!-- 主内容区 -->
    <view class="content" v-else>
      <!-- 顶部问候 -->
      <view class="greeting">
        <view class="greeting-text">{{greeting}}，{{userInfo.nickname || '游客'}}</view>
        <view class="points-info" @click="navigateTo('/pages/points/detail')">
          <text class="points-icon">积分</text>
          <text class="points-value">{{userInfo.points || 0}}</text>
        </view>
      </view>
      
      <!-- 功能区 -->
      <view class="function-area">
        <view class="function-item" @click="navigateTo('/pages/card/list')">
          <image class="function-icon" src="/static/images/icons/card.png" mode="aspectFit"></image>
          <text class="function-text">我的名片</text>
        </view>
        <view class="function-item" @click="scanCode">
          <image class="function-icon" src="/static/images/icons/scan.png" mode="aspectFit"></image>
          <text class="function-text">扫码添加</text>
        </view>
        <view class="function-item" @click="navigateTo('/pages/contacts/index')">
          <image class="function-icon" src="/static/images/icons/contact.png" mode="aspectFit"></image>
          <text class="function-text">通讯录</text>
        </view>
        <view class="function-item" @click="navigateTo('/pages/visitors/index')">
          <image class="function-icon" src="/static/images/icons/visit.png" mode="aspectFit"></image>
          <text class="function-text">访客记录</text>
        </view>
      </view>
      
      <!-- 热门推荐 -->
      <view class="recommend-area">
        <view class="section-title">
          <text>热门推荐</text>
          <text class="more" @click="navigateTo('/pages/card/list')">更多</text>
        </view>
        <scroll-view class="recommend-list" scroll-x="true" show-scrollbar="false">
          <view class="recommend-item" v-for="(item, index) in recommendList" :key="index" @click="viewCard(item.id)">
            <image class="recommend-avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            <view class="recommend-info">
              <view class="recommend-name">{{item.name}}</view>
              <view class="recommend-position">{{item.position}}</view>
              <view class="recommend-company">{{item.company}}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      
      <!-- 营销活动 -->
      <view class="activity-area" v-if="activityList.length > 0">
        <view class="section-title">
          <text>营销活动</text>
        </view>
        <swiper class="activity-swiper" circular autoplay interval="3000" duration="500" indicator-dots indicator-color="rgba(255, 255, 255, .5)" indicator-active-color="#ffffff">
          <swiper-item v-for="(item, index) in activityList" :key="index" @click="navigateTo(item.url)">
            <image class="activity-image" :src="item.image" mode="aspectFill"></image>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showSplash: true,
      userInfo: {},
      greeting: '上午好',
      recommendList: [],
      activityList: []
    }
  },
  onLoad() {
    // 显示启动页3秒
    setTimeout(() => {
      this.showSplash = false
      this.checkLogin()
    }, 3000)
    
    // 设置问候语
    this.setGreeting()
  },
  onShow() {
    // 如果已经关闭启动页，则检查登录状态
    if (!this.showSplash) {
      this.checkLogin()
    }
    
    // 获取推荐列表
    this.getRecommendList()
    
    // 获取活动列表
    this.getActivityList()
  },
  methods: {
    // 检查登录状态
    checkLogin() {
      const token = uni.getStorageSync('token')
      if (token) {
        // 已登录，获取用户信息
        this.getUserInfo()
      } else {
        // 未登录，跳转到登录页
        uni.navigateTo({
          url: '/pages/login/login'
        })
      }
    },
    
    // 获取用户信息
    getUserInfo() {
      const userInfo = uni.getStorageSync('userInfo')
      if (userInfo) {
        this.userInfo = userInfo
      } else {
        this.$api.get('/user/info').then(res => {
          this.userInfo = res
          uni.setStorageSync('userInfo', res)
          this.$store.commit('updateUserInfo', res)
        })
      }
    },
    
    // 设置问候语
    setGreeting() {
      const hour = new Date().getHours()
      if (hour < 6) {
        this.greeting = '凌晨好'
      } else if (hour < 9) {
        this.greeting = '早上好'
      } else if (hour < 12) {
        this.greeting = '上午好'
      } else if (hour < 14) {
        this.greeting = '中午好'
      } else if (hour < 17) {
        this.greeting = '下午好'
      } else if (hour < 19) {
        this.greeting = '傍晚好'
      } else {
        this.greeting = '晚上好'
      }
    },
    
    // 获取推荐列表
    getRecommendList() {
      this.$api.get('/card/recommend').then(res => {
        this.recommendList = res
      })
    },
    
    // 获取活动列表
    getActivityList() {
      this.$api.get('/activity/list').then(res => {
        this.activityList = res
      })
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({
        url
      })
    },
    
    // 扫码
    scanCode() {
      uni.scanCode({
        success: (res) => {
          // 处理扫码结果
          if (res.result) {
            try {
              const data = JSON.parse(res.result)
              if (data.type === 'card') {
                // 扫描名片
                this.navigateTo(`/pages/card/detail?id=${data.id}`)
              } else {
                uni.showToast({
                  title: '无效的二维码',
                  icon: 'none'
                })
              }
            } catch (e) {
              uni.showToast({
                title: '无效的二维码',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 查看名片
    viewCard(id) {
      this.navigateTo(`/pages/card/detail?id=${id}`)
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 启动页样式 */
.splash {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #1296db;
  
  .splash-logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 30rpx;
  }
  
  .splash-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20rpx;
  }
  
  .splash-subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 主内容区样式 */
.content {
  padding: 30rpx;
}

/* 顶部问候 */
.greeting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  
  .greeting-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .points-info {
    display: flex;
    align-items: center;
    background-color: #f0f9ff;
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    
    .points-icon {
      font-size: 24rpx;
      color: #1296db;
      margin-right: 10rpx;
    }
    
    .points-value {
      font-size: 28rpx;
      font-weight: bold;
      color: #1296db;
    }
  }
}

/* 功能区 */
.function-area {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  
  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 160rpx;
    
    .function-icon {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 10rpx;
    }
    
    .function-text {
      font-size: 24rpx;
      color: #333333;
    }
  }
}

/* 热门推荐 */
.recommend-area {
  margin-bottom: 40rpx;
  
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    
    .more {
      font-size: 24rpx;
      font-weight: normal;
      color: #999999;
    }
  }
  
  .recommend-list {
    white-space: nowrap;
    
    .recommend-item {
      display: inline-flex;
      align-items: center;
      background-color: #ffffff;
      padding: 20rpx;
      margin-right: 20rpx;
      border-radius: 10rpx;
      box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
      width: 400rpx;
      
      .recommend-avatar {
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      
      .recommend-info {
        flex: 1;
        overflow: hidden;
        
        .recommend-name {
          font-size: 28rpx;
          font-weight: bold;
          color: #333333;
          margin-bottom: 6rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .recommend-position {
          font-size: 24rpx;
          color: #666666;
          margin-bottom: 6rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .recommend-company {
          font-size: 24rpx;
          color: #999999;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

/* 营销活动 */
.activity-area {
  margin-bottom: 40rpx;
  
  .section-title {
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .activity-swiper {
    height: 200rpx;
    border-radius: 10rpx;
    overflow: hidden;
    
    .activity-image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
