<template>
  <view class="splash-container">
    <view class="content">
      <view class="logo-container">
        <image class="logo" :src="logoUrl" mode="aspectFit" />
      </view>
      <view class="brand">
        <text class="brand-cn">码布斯企业家俱乐部</text>
        <text class="brand-en">Mabus Entrepreneurs' Hub</text>
      </view>
      <text class="slogan">赋能数字未来，连接价值人脉</text>
      <view class="loading">
        <uni-icons type="spinner-cycle" size="24" color="#999999" />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      logoUrl: 'https://ai-public.mastergo.com/ai/img_res/e78d809a847ab5cd068f26a98bd0420b.jpg'
    }
  },
  onLoad() {
    // 模拟启动加载过程
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }, 2000)
  }
}
</script>

<style>
page {
  height: 100%;
}

.splash-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #ffffff;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 40rpx;
}

.logo-container {
  width: 280rpx;
  height: 280rpx;
  margin-bottom: 60rpx;
}

.logo {
  width: 100%;
  height: 100%;
}

.brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.brand-cn {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.brand-en {
  font-size: 18px;
  color: #666666;
}

.slogan {
  font-size: 14px;
  color: #999999;
  margin-bottom: 120rpx;
}

.loading {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>