<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">用户注册</text>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
        <text class="app-name">MEH 电子名片</text>
        <text class="subtitle">创建您的专属名片</text>
      </view>

      <!-- 注册表单 -->
      <view class="form-section">
        <view class="form-title">注册账号</view>

        <!-- 手机号 -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="phone" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="phone"
              placeholder="请输入手机号"
              type="number"
              maxlength="11"
            />
          </view>
        </view>

        <!-- 验证码 -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="chatboxes" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="verifyCode"
              placeholder="请输入验证码"
              type="number"
              maxlength="6"
            />
            <button class="code-btn" :disabled="codeDisabled" @click="sendCode">
              {{ codeText }}
            </button>
          </view>
        </view>

        <!-- 昵称 -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="person" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="nickname"
              placeholder="请输入昵称"
              maxlength="20"
            />
          </view>
        </view>

        <!-- 密码 -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="locked" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="password"
              placeholder="请输入密码"
              :password="!showPassword"
            />
            <uni-icons
              class="eye-icon"
              :type="showPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#cccccc"
              @click="togglePassword"
            ></uni-icons>
          </view>
        </view>

        <!-- 确认密码 -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="locked" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="confirmPassword"
              placeholder="请确认密码"
              :password="!showConfirmPassword"
            />
            <uni-icons
              class="eye-icon"
              :type="showConfirmPassword ? 'eye-slash' : 'eye'"
              size="20"
              color="#cccccc"
              @click="toggleConfirmPassword"
            ></uni-icons>
          </view>
        </view>

        <!-- 密码强度提示 -->
        <view class="password-strength" v-if="password">
          <text class="strength-text">密码强度：</text>
          <view class="strength-bar">
            <view class="strength-item" :class="{active: passwordStrength >= 1}"></view>
            <view class="strength-item" :class="{active: passwordStrength >= 2}"></view>
            <view class="strength-item" :class="{active: passwordStrength >= 3}"></view>
          </view>
          <text class="strength-label">{{ strengthText }}</text>
        </view>

        <!-- 邀请码（可选） -->
        <view class="form-item">
          <view class="input-group">
            <uni-icons type="gift" size="20" color="#cccccc"></uni-icons>
            <input
              class="input"
              v-model="inviteCode"
              placeholder="邀请码（可选）"
              maxlength="10"
            />
          </view>
        </view>

        <!-- 协议同意 -->
        <view class="agreement-section">
          <view class="checkbox-group" @click="toggleAgreement">
            <view class="checkbox" :class="{checked: agreedToTerms}">
              <uni-icons v-if="agreedToTerms" type="checkmarkempty" size="14" color="#ffffff"></uni-icons>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="viewUserAgreement">《用户服务协议》</text>
              和
              <text class="link-text" @click.stop="viewPrivacyPolicy">《隐私政策》</text>
            </text>
          </view>
        </view>

        <!-- 注册按钮 -->
        <button class="submit-btn" :disabled="!canSubmit" @click="register">
          立即注册
        </button>

        <!-- 底部链接 -->
        <view class="footer-links">
          <text class="link-text" @click="goToLogin">已有账号？立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/api/index.js'

export default {
  data() {
    return {
      phone: '',
      verifyCode: '',
      nickname: '',
      password: '',
      confirmPassword: '',
      inviteCode: '',
      showPassword: false,
      showConfirmPassword: false,
      agreedToTerms: false,
      codeDisabled: false,
      codeText: '获取验证码',
      countdown: 60,
      timer: null
    }
  },

  computed: {
    // 是否可以提交
    canSubmit() {
      return this.phone.length === 11 &&
             this.verifyCode.length === 6 &&
             this.nickname.trim().length > 0 &&
             this.password.length >= 6 &&
             this.confirmPassword.length >= 6 &&
             this.password === this.confirmPassword &&
             this.agreedToTerms
    },

    // 密码强度
    passwordStrength() {
      if (!this.password) return 0

      let strength = 0
      if (this.password.length >= 6) strength++
      if (/[a-zA-Z]/.test(this.password) && /[0-9]/.test(this.password)) strength++
      if (/[^a-zA-Z0-9]/.test(this.password)) strength++

      return strength
    },

    // 密码强度文本
    strengthText() {
      const texts = ['弱', '中', '强']
      return texts[this.passwordStrength - 1] || '弱'
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 发送验证码
    async sendCode() {
      if (!this.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!/^1[3-9]\d{9}$/.test(this.phone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }

      try {
        const res = await api.post('/auth/send-register-code', {
          phone: this.phone
        })

        if (res.success) {
          uni.showToast({
            title: '验证码已发送',
            icon: 'success'
          })

          this.startCountdown()
        } else {
          uni.showToast({
            title: res.message || '发送失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    },

    // 开始倒计时
    startCountdown() {
      this.codeDisabled = true
      this.countdown = 60
      this.codeText = `${this.countdown}s`

      this.timer = setInterval(() => {
        this.countdown--
        this.codeText = `${this.countdown}s`

        if (this.countdown <= 0) {
          this.clearCountdown()
        }
      }, 1000)
    },

    // 清除倒计时
    clearCountdown() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.codeDisabled = false
      this.codeText = '获取验证码'
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },

    // 切换协议同意状态
    toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms
    },

    // 查看用户协议
    viewUserAgreement() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },

    // 查看隐私政策
    viewPrivacyPolicy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },

    // 注册
    async register() {
      try {
        uni.showLoading({ title: '注册中...' })

        const registerData = {
          phone: this.phone,
          code: this.verifyCode,
          nickname: this.nickname.trim(),
          password: this.password
        }

        // 如果有邀请码，添加到请求数据中
        if (this.inviteCode.trim()) {
          registerData.inviteCode = this.inviteCode.trim()
        }

        const res = await api.post('/auth/register', registerData)

        if (res.success) {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          })

          // 延迟跳转到登录页面
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/login'
            })
          }, 1500)
        } else {
          uni.showToast({
            title: res.message || '注册失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('注册失败:', error)
        uni.showToast({
          title: '注册失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 跳转到登录
    goToLogin() {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  },

  onUnload() {
    this.clearCountdown()
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: transparent;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  margin-left: 20rpx;
}

/* 内容区域 */
.content {
  padding: 40rpx 60rpx;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 表单区域 */
.form-section {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
}

.form-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  text-align: center;
  margin-bottom: 60rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.input-group {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  padding: 0 30rpx;
  height: 100rpx;
}

.input {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  margin-left: 20rpx;
}

.code-btn {
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  margin-left: 20rpx;
}

.code-btn:disabled {
  background-color: #cccccc;
}

.eye-icon {
  padding: 10rpx;
}

/* 密码强度 */
.password-strength {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.strength-text {
  font-size: 24rpx;
  color: #666666;
  margin-right: 16rpx;
}

.strength-bar {
  display: flex;
  gap: 8rpx;
  margin-right: 16rpx;
}

.strength-item {
  width: 40rpx;
  height: 6rpx;
  background-color: #f0f0f0;
  border-radius: 3rpx;
  transition: background-color 0.3s;
}

.strength-item.active {
  background-color: #52c41a;
}

.strength-label {
  font-size: 24rpx;
  color: #52c41a;
  font-weight: 600;
}

/* 协议同意 */
.agreement-section {
  margin-bottom: 40rpx;
}

.checkbox-group {
  display: flex;
  align-items: flex-start;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #cccccc;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  margin-top: 4rpx;
  transition: all 0.3s;
}

.checkbox.checked {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.agreement-text {
  flex: 1;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

.link-text {
  color: #ff6b35;
  text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 100rpx;
  background-color: #ff6b35;
  color: #ffffff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 40rpx;
}

.submit-btn:disabled {
  background-color: #cccccc;
}

/* 底部链接 */
.footer-links {
  text-align: center;
}

.footer-links .link-text {
  font-size: 28rpx;
  color: #666666;
}
</style>
