<template>
  <view class="container">
    <view class="header">
      <view class="title">名片创建</view>
    </view>
    
    <scroll-view class="form-container" scroll-y>
      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>
        
        <view class="form-item">
          <text class="label required">姓名</text>
          <input class="input" type="text" placeholder="请输入姓名" v-model="form.name" maxlength="20" />
        </view>
        
        <view class="form-item">
          <text class="label required">职位</text>
          <input class="input" type="text" placeholder="请输入职位" v-model="form.position" maxlength="30" />
        </view>
        
        <view class="form-item">
          <text class="label required">公司</text>
          <input class="input" type="text" placeholder="请输入公司名称" v-model="form.company" maxlength="50" />
        </view>
        
        <view class="form-item">
          <text class="label">头像</text>
          <view class="upload-box" @click="uploadAvatar">
            <image v-if="form.avatar" class="avatar-preview" :src="form.avatar" mode="aspectFill"></image>
            <view v-else class="upload-placeholder">
              <text class="upload-icon">+</text>
              <text class="upload-text">上传头像</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 联系方式 -->
      <view class="form-section">
        <view class="section-title">联系方式</view>
        
        <view class="form-item">
          <text class="label required">手机号</text>
          <input class="input" type="number" placeholder="请输入手机号" v-model="form.phone" maxlength="11" />
        </view>
        
        <view class="form-item">
          <text class="label">邮箱</text>
          <input class="input" type="text" placeholder="请输入邮箱" v-model="form.email" maxlength="50" />
        </view>
        
        <view class="form-item">
          <text class="label">微信</text>
          <input class="input" type="text" placeholder="请输入微信号" v-model="form.wechat" maxlength="30" />
        </view>
        
        <view class="form-item">
          <text class="label">地址</text>
          <textarea class="textarea" placeholder="请输入地址" v-model="form.address" maxlength="100" />
        </view>
      </view>
      
      <!-- 个人介绍 -->
      <view class="form-section">
        <view class="section-title">个人介绍</view>
        
        <view class="form-item">
          <text class="label">个人简介</text>
          <textarea class="textarea" placeholder="请输入个人简介" v-model="form.introduction" maxlength="500" />
        </view>
        
        <view class="form-item">
          <text class="label">专业技能</text>
          <textarea class="textarea" placeholder="请输入专业技能，多个技能用逗号分隔" v-model="form.skills" maxlength="200" />
        </view>
      </view>
      
      <!-- 资源与项目 -->
      <view class="form-section">
        <view class="section-title">资源与项目</view>
        
        <view class="form-item">
          <text class="label">我的资源</text>
          <textarea class="textarea" placeholder="请描述您拥有的资源，如人脉、渠道、技术等" v-model="form.resources" maxlength="500" />
        </view>
        
        <view class="form-item">
          <text class="label">我的项目</text>
          <textarea class="textarea" placeholder="请描述您参与或负责的项目经历和成果" v-model="form.projects" maxlength="500" />
        </view>
      </view>
      
      <!-- 名片模板 -->
      <view class="form-section">
        <view class="section-title">名片模板</view>
        
        <scroll-view class="template-list" scroll-x="true" show-scrollbar="false">
          <view 
            class="template-item" 
            v-for="(item, index) in templateList" 
            :key="index"
            :class="{'active': form.templateId === item.id}"
            @click="selectTemplate(item.id)"
          >
            <image class="template-image" :src="item.image" mode="aspectFill"></image>
            <view class="template-name">{{item.name}}</view>
          </view>
        </scroll-view>
      </view>
    </scroll-view>
    
    <!-- 底部按钮 -->
    <view class="footer">
      <button class="cancel-btn" @click="goBack">取消</button>
      <button class="save-btn" @click="saveCard" :disabled="loading">
        <text v-if="!loading">保存</text>
        <text v-else>保存中...</text>
      </button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: '',
        position: '',
        company: '',
        avatar: '',
        phone: '',
        email: '',
        wechat: '',
        address: '',
        introduction: '',
        skills: '',
        resources: '',
        projects: '',
        templateId: 1
      },
      templateList: [],
      loading: false,
      isEdit: false,
      cardId: null
    }
  },
  onLoad(options) {
    // 获取名片模板列表
    this.getTemplateList()
    
    // 判断是否为编辑模式
    if (options.id) {
      this.isEdit = true
      this.cardId = options.id
      this.getCardDetail(options.id)
    }
  },
  methods: {
    // 获取名片模板列表
    getTemplateList() {
      // 调用后端获取模板列表接口
      // 注意：后端未提供专门的模板列表接口，这里需要创建相应接口或使用其他方式获取
      this.$api.get('/cards/templates').then(res => {
        this.templateList = res || []
        if (this.templateList.length > 0 && !this.form.templateId) {
          this.form.templateId = this.templateList[0].id
        }
      }).catch(() => {
        // 模拟数据，实际项目中应从后端获取
        this.templateList = [
          {id: 1, name: '商务风格', image: '/static/images/templates/template1.png'},
          {id: 2, name: '科技风格', image: '/static/images/templates/template2.png'},
          {id: 3, name: '创意风格', image: '/static/images/templates/template3.png'}
        ]
        if (!this.form.templateId) {
          this.form.templateId = this.templateList[0].id
        }
      })
    },
    
    // 获取名片详情
    getCardDetail(id) {
      // 调用CardController.getCardDetail方法
      this.$api.get(`/cards/${id}`).then(res => {
        if (res) {
          // 将后端返回的Card对象数据映射到表单
          this.form = {
            ...this.form,
            ...res
          }
        }
      })
    },
    
    // 上传头像
    uploadAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          
          // 显示上传中
          uni.showLoading({
            title: '上传中...'
          })
          
          // 上传图片
          this.$api.upload('/file/upload', tempFilePath, 'file').then(res => {
            this.form.avatar = res.url
            uni.hideLoading()
          }).catch(() => {
            uni.hideLoading()
          })
        }
      })
    },
    
    // 选择模板
    selectTemplate(id) {
      this.form.templateId = id
    },
    
    // 保存名片
    saveCard() {
      // 表单验证
      if (!this.form.name) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        })
        return
      }
      
      if (!this.form.position) {
        uni.showToast({
          title: '请输入职位',
          icon: 'none'
        })
        return
      }
      
      if (!this.form.company) {
        uni.showToast({
          title: '请输入公司名称',
          icon: 'none'
        })
        return
      }
      
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }
      
      // 构建名片数据对象，与后端CardCreateDTO/CardUpdateDTO保持一致
      const cardData = {
        name: this.form.name,
        position: this.form.position,
        company: this.form.company,
        avatar: this.form.avatar,
        phone: this.form.phone,
        email: this.form.email,
        wechat: this.form.wechat,
        address: this.form.address,
        introduction: this.form.introduction,
        skills: this.form.skills,
        resources: this.form.resources, // 资源展示，与后端字段保持一致
        projects: this.form.projects,   // 项目展示，与后端字段保持一致
        templateId: this.form.templateId
      }
      
      // 保存请求
      this.loading = true
      
      if (this.isEdit) {
        // 编辑名片，调用CardController.updateCard方法
        this.$api.put(`/cards/${this.cardId}`, cardData).then(() => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }).finally(() => {
          this.loading = false
        })
      } else {
        // 创建名片，调用CardController.createCard方法
        this.$api.post('/cards', cardData).then(() => {
          uni.showToast({
            title: '创建成功',
            icon: 'success'
          })
          
          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }).finally(() => {
          this.loading = false
        })
      }
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
}

.form-container {
  flex: 1;
  padding: 30rpx;
}

.form-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 30rpx;
    position: relative;
    padding-left: 20rpx;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 30rpx;
      background-color: #1296db;
      border-radius: 3rpx;
    }
  }
  
  .form-item {
    margin-bottom: 30rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      display: block;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 10rpx;
      
      &.required::before {
        content: '*';
        color: #ff4d4f;
        margin-right: 4rpx;
      }
    }
    
    .input {
      height: 80rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
    }
    
    .textarea {
      height: 160rpx;
      background-color: #f8f8f8;
      border-radius: 8rpx;
      padding: 20rpx;
      font-size: 28rpx;
      width: 100%;
      box-sizing: border-box;
    }
    
    .upload-box {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
      overflow: hidden;
      
      .avatar-preview {
        width: 100%;
        height: 100%;
      }
      
      .upload-placeholder {
        width: 100%;
        height: 100%;
        background-color: #f8f8f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        
        .upload-icon {
          font-size: 48rpx;
          color: #999999;
          margin-bottom: 10rpx;
        }
        
        .upload-text {
          font-size: 24rpx;
          color: #999999;
        }
      }
    }
  }
}

.template-list {
  white-space: nowrap;
  
  .template-item {
    display: inline-block;
    width: 200rpx;
    margin-right: 20rpx;
    
    &.active {
      .template-image {
        border: 4rpx solid #1296db;
      }
      
      .template-name {
        color: #1296db;
      }
    }
    
    .template-image {
      width: 200rpx;
      height: 300rpx;
      border-radius: 8rpx;
      margin-bottom: 10rpx;
      border: 4rpx solid transparent;
      box-sizing: border-box;
    }
    
    .template-name {
      font-size: 24rpx;
      color: #666666;
      text-align: center;
    }
  }
}

.footer {
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
  
  .cancel-btn {
    flex: 1;
    height: 80rpx;
    background-color: #f8f8f8;
    color: #666666;
    font-size: 28rpx;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .save-btn {
    flex: 1;
    height: 80rpx;
    background-color: #1296db;
    color: #ffffff;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &[disabled] {
      opacity: 0.6;
      background-color: #1296db;
      color: #ffffff;
    }
  }
}
</style>
