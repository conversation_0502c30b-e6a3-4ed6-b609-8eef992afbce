<template>
  <view class="container">
    <view class="header">
      <view class="title">我的名片</view>
      <view class="create-btn" @click="createCard">
        <text class="create-icon">+</text>
        <text>新建名片</text>
      </view>
    </view>
    
    <!-- 名片列表 -->
    <view class="card-list" v-if="cardList.length > 0">
      <view class="card-item" v-for="(item, index) in cardList" :key="index" @click="viewCard(item.id)">
        <view class="card-info">
          <view class="card-name">{{item.name}}</view>
          <view class="card-position">{{item.position}}</view>
          <view class="card-time">创建时间：{{formatDate(item.createTime)}}</view>
        </view>
        <view class="card-actions">
          <view class="card-action" @click.stop="editCard(item.id)">
            <image class="action-icon" src="/static/images/icons/edit.png" mode="aspectFit"></image>
            <text class="action-text">编辑</text>
          </view>
          <view class="card-action" @click.stop="shareCard(item.id)">
            <image class="action-icon" src="/static/images/icons/share.png" mode="aspectFit"></image>
            <text class="action-text">分享</text>
          </view>
          <view class="card-action" @click.stop="deleteCard(item.id)">
            <image class="action-icon" src="/static/images/icons/delete.png" mode="aspectFit"></image>
            <text class="action-text">删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <image class="empty-image" src="/static/images/empty-card.png" mode="aspectFit"></image>
      <view class="empty-text">暂无名片，快去创建吧！</view>
      <button class="empty-btn" @click="createCard">立即创建</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cardList: []
    }
  },
  onLoad() {
    this.getCardList()
  },
  onShow() {
    this.getCardList()
  },
  methods: {
    // 获取名片列表
    getCardList() {
      this.$api.get('/card/list').then(res => {
        this.cardList = res || []
      })
    },
    
    // 查看名片
    viewCard(id) {
      uni.navigateTo({
        url: `/pages/card/detail?id=${id}`
      })
    },
    
    // 创建名片
    createCard() {
      uni.navigateTo({
        url: '/pages/card/create'
      })
    },
    
    // 编辑名片
    editCard(id) {
      uni.navigateTo({
        url: `/pages/card/create?id=${id}`
      })
    },
    
    // 分享名片
    shareCard(id) {
      uni.navigateTo({
        url: `/pages/card/share?id=${id}`
      })
    },
    
    // 删除名片
    deleteCard(id) {
      uni.showModal({
        title: '提示',
        content: '确定要删除此名片吗？',
        success: (res) => {
          if (res.confirm) {
            this.$api.delete(`/card/${id}`).then(() => {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              this.getCardList()
            })
          }
        }
      })
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  padding: 30rpx;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .create-btn {
    display: flex;
    align-items: center;
    background-color: #1296db;
    color: #ffffff;
    font-size: 28rpx;
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    
    .create-icon {
      font-size: 32rpx;
      margin-right: 6rpx;
    }
  }
}

.card-list {
  .card-item {
    background-color: #ffffff;
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .card-info {
      margin-bottom: 20rpx;
      
      .card-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 10rpx;
      }
      
      .card-position {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 10rpx;
      }
      
      .card-time {
        font-size: 24rpx;
        color: #999999;
      }
    }
    
    .card-actions {
      display: flex;
      border-top: 1px solid #f0f0f0;
      padding-top: 20rpx;
      
      .card-action {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .action-icon {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 6rpx;
        }
        
        .action-text {
          font-size: 24rpx;
          color: #666666;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100rpx;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999999;
    margin-bottom: 40rpx;
  }
  
  .empty-btn {
    background-color: #1296db;
    color: #ffffff;
    font-size: 28rpx;
    padding: 20rpx 60rpx;
    border-radius: 40rpx;
  }
}
</style>
