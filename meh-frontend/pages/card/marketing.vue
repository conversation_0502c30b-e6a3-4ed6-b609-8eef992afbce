<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">名片营销</text>
      <uni-icons class="help-icon" type="help" size="20" color="#666666" @click="showHelp"></uni-icons>
    </view>

    <!-- 数据概览 -->
    <view class="overview-section">
      <view class="overview-card">
        <view class="overview-header">
          <text class="overview-title">今日数据</text>
          <text class="overview-date">{{ todayDate }}</text>
        </view>
        <view class="overview-stats">
          <view class="stat-item">
            <text class="stat-value">{{ todayStats.views }}</text>
            <text class="stat-label">浏览量</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ todayStats.visitors }}</text>
            <text class="stat-label">访客数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ todayStats.shares }}</text>
            <text class="stat-label">分享数</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ todayStats.contacts }}</text>
            <text class="stat-label">新增联系</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 营销工具 -->
    <view class="tools-section">
      <view class="section-header">
        <text class="section-title">营销工具</text>
      </view>
      <view class="tools-grid">
        <view v-for="(tool, index) in marketingTools" :key="index" class="tool-item" @click="useTool(tool)">
          <view class="tool-icon" :style="{ backgroundColor: tool.color }">
            <uni-icons :type="tool.icon" size="24" color="#FFFFFF"></uni-icons>
          </view>
          <text class="tool-name">{{ tool.name }}</text>
          <text class="tool-desc">{{ tool.description }}</text>
        </view>
      </view>
    </view>

    <!-- 数据分析 -->
    <view class="analytics-section">
      <view class="section-header">
        <text class="section-title">数据分析</text>
        <view class="time-filter">
          <text 
            v-for="(period, index) in timePeriods" 
            :key="index" 
            class="period-item" 
            :class="{ active: activePeriod === period.value }"
            @click="switchPeriod(period.value)"
          >
            {{ period.label }}
          </text>
        </view>
      </view>
      
      <!-- 趋势图表 -->
      <view class="chart-container">
        <view class="chart-header">
          <text class="chart-title">访问趋势</text>
          <text class="chart-total">总计: {{ totalViews }}</text>
        </view>
        <view class="chart-area">
          <!-- 这里可以集成图表组件，如 uCharts -->
          <view class="chart-placeholder">
            <uni-icons type="bar-chart" size="48" color="#E9ECEF"></uni-icons>
            <text class="chart-text">图表数据加载中...</text>
          </view>
        </view>
      </view>

      <!-- 数据列表 -->
      <view class="data-list">
        <view v-for="(item, index) in analyticsData" :key="index" class="data-item">
          <view class="data-info">
            <text class="data-label">{{ item.label }}</text>
            <text class="data-desc">{{ item.description }}</text>
          </view>
          <view class="data-value">
            <text class="value-number">{{ item.value }}</text>
            <view class="value-trend" :class="{ up: item.trend > 0, down: item.trend < 0 }">
              <uni-icons 
                :type="item.trend > 0 ? 'up' : item.trend < 0 ? 'down' : 'minus'" 
                size="12" 
                :color="item.trend > 0 ? '#52C41A' : item.trend < 0 ? '#FF4757' : '#999999'"
              ></uni-icons>
              <text class="trend-text">{{ Math.abs(item.trend) }}%</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 推广建议 -->
    <view class="suggestions-section">
      <view class="section-header">
        <text class="section-title">推广建议</text>
      </view>
      <view class="suggestions-list">
        <view v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
          <view class="suggestion-icon">
            <uni-icons type="lightbulb" size="16" color="#FFA500"></uni-icons>
          </view>
          <view class="suggestion-content">
            <text class="suggestion-title">{{ suggestion.title }}</text>
            <text class="suggestion-desc">{{ suggestion.description }}</text>
          </view>
          <view class="suggestion-action" @click="applySuggestion(suggestion)">
            <text class="action-text">去执行</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      todayDate: '',
      activePeriod: '7d',
      todayStats: {
        views: 128,
        visitors: 89,
        shares: 23,
        contacts: 12
      },
      marketingTools: [
        {
          id: 1,
          name: '二维码推广',
          description: '生成专属二维码',
          icon: 'scan',
          color: '#2B85E4',
          action: 'qrcode'
        },
        {
          id: 2,
          name: '社交分享',
          description: '分享到社交平台',
          icon: 'redo',
          color: '#52C41A',
          action: 'share'
        },
        {
          id: 3,
          name: '邮件推广',
          description: '发送邮件邀请',
          icon: 'email',
          color: '#FF8A00',
          action: 'email'
        },
        {
          id: 4,
          name: '短信推广',
          description: '发送短信邀请',
          icon: 'chat',
          color: '#9C27B0',
          action: 'sms'
        }
      ],
      timePeriods: [
        { label: '7天', value: '7d' },
        { label: '30天', value: '30d' },
        { label: '90天', value: '90d' }
      ],
      totalViews: 2580,
      analyticsData: [
        {
          label: '页面浏览量',
          description: '名片被查看的次数',
          value: 2580,
          trend: 12.5
        },
        {
          label: '独立访客',
          description: '访问名片的用户数',
          value: 1890,
          trend: 8.3
        },
        {
          label: '平均停留时间',
          description: '用户浏览名片的时长',
          value: '2分30秒',
          trend: -5.2
        },
        {
          label: '转化率',
          description: '访客转为联系人的比例',
          value: '15.6%',
          trend: 3.8
        }
      ],
      suggestions: [
        {
          id: 1,
          title: '完善名片信息',
          description: '添加更多个人介绍和技能标签，提高名片吸引力',
          action: 'complete_profile'
        },
        {
          id: 2,
          title: '定期更新动态',
          description: '发布行业见解和工作动态，保持名片活跃度',
          action: 'update_content'
        },
        {
          id: 3,
          title: '主动分享名片',
          description: '在社交媒体和行业群组中分享您的名片',
          action: 'share_more'
        }
      ]
    }
  },
  onLoad() {
    this.initData()
    this.loadAnalyticsData()
  },
  methods: {
    initData() {
      const today = new Date()
      this.todayDate = `${today.getMonth() + 1}月${today.getDate()}日`
    },
    
    goBack() {
      uni.navigateBack()
    },
    
    showHelp() {
      uni.showModal({
        title: '营销帮助',
        content: '名片营销功能可以帮助您更好地推广个人品牌，提升影响力。通过数据分析了解访客行为，优化营销策略。',
        showCancel: false
      })
    },
    
    loadAnalyticsData() {
      // 调用API获取分析数据
      // this.$api.analytics.getData({ period: this.activePeriod }).then(res => {
      //   this.todayStats = res.data.todayStats
      //   this.analyticsData = res.data.analyticsData
      //   this.totalViews = res.data.totalViews
      // })
    },
    
    switchPeriod(period) {
      this.activePeriod = period
      this.loadAnalyticsData()
    },
    
    useTool(tool) {
      switch (tool.action) {
        case 'qrcode':
          this.generateQRCode()
          break
        case 'share':
          this.shareToSocial()
          break
        case 'email':
          this.sendEmail()
          break
        case 'sms':
          this.sendSMS()
          break
      }
    },
    
    generateQRCode() {
      uni.navigateTo({
        url: '/pages/card/share'
      })
    },
    
    shareToSocial() {
      uni.showActionSheet({
        itemList: ['分享到微信', '分享到朋友圈', '分享到微博', '复制链接'],
        success: (res) => {
          const actions = ['wechat', 'moments', 'weibo', 'copy']
          const action = actions[res.tapIndex]
          uni.showToast({
            title: `分享到${['微信', '朋友圈', '微博', '复制链接'][res.tapIndex]}`,
            icon: 'success'
          })
        }
      })
    },
    
    sendEmail() {
      uni.showToast({
        title: '邮件推广功能开发中',
        icon: 'none'
      })
    },
    
    sendSMS() {
      uni.showToast({
        title: '短信推广功能开发中',
        icon: 'none'
      })
    },
    
    applySuggestion(suggestion) {
      switch (suggestion.action) {
        case 'complete_profile':
          uni.navigateTo({
            url: '/pages/card/create'
          })
          break
        case 'update_content':
          uni.showToast({
            title: '动态功能开发中',
            icon: 'none'
          })
          break
        case 'share_more':
          this.shareToSocial()
          break
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.help-icon {
  width: 40rpx;
  height: 40rpx;
}

.overview-section {
  padding: 24rpx 32rpx;
}

.overview-card {
  padding: 32rpx;
  background: linear-gradient(135deg, #2B85E4 0%, #1E6FCC 100%);
  border-radius: 16rpx;
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.overview-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
}

.overview-date {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.overview-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.stat-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
}

.tools-section,
.analytics-section,
.suggestions-section {
  margin-top: 24rpx;
  padding: 0 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.time-filter {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 4rpx;
}

.period-item {
  padding: 12rpx 24rpx;
  font-size: 14px;
  color: #666666;
  border-radius: 6rpx;
  transition: all 0.3s;
}

.period-item.active {
  background-color: #FFFFFF;
  color: #2B85E4;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tools-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.tool-item {
  width: calc(50% - 12rpx);
  padding: 32rpx 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.tool-icon {
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 48rpx;
  margin-bottom: 16rpx;
}

.tool-name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.tool-desc {
  font-size: 12px;
  color: #666666;
  text-align: center;
}

.chart-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.chart-total {
  font-size: 14px;
  color: #666666;
}

.chart-area {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-text {
  font-size: 14px;
  color: #999999;
  margin-top: 16rpx;
}

.data-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.data-item:last-child {
  border-bottom: none;
}

.data-info {
  flex: 1;
}

.data-label {
  font-size: 16px;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.data-desc {
  font-size: 12px;
  color: #666666;
}

.data-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.value-number {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.value-trend {
  display: flex;
  align-items: center;
}

.trend-text {
  font-size: 12px;
  margin-left: 4rpx;
}

.value-trend.up .trend-text {
  color: #52C41A;
}

.value-trend.down .trend-text {
  color: #FF4757;
}

.suggestions-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FFF8E1;
  border-radius: 32rpx;
  margin-right: 24rpx;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 16px;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.suggestion-desc {
  font-size: 14px;
  color: #666666;
}

.suggestion-action {
  padding: 12rpx 24rpx;
  background-color: #2B85E4;
  border-radius: 20rpx;
}

.action-text {
  font-size: 14px;
  color: #FFFFFF;
}
</style>