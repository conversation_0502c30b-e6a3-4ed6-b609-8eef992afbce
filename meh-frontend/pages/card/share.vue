<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#fff" @click="goBack"/>
      <text class="nav-title">分享名片</text>
    </view>

    <!-- 二维码展示区 -->
    <view class="qr-container">
      <view class="qr-card">
        <image class="qr-image" :src="qrCodeUrl" mode="aspectFit"/>
      </view>
      <text class="qr-tip">扫一扫，快速添加名片</text>
    </view>

    <!-- 操作按钮区 -->
    <view class="action-container">
      <uni-button class="save-btn" type="primary" @click="saveQrCode">
        <uni-icons class="btn-icon" type="download" size="20" color="#fff"/>
        <text>保存二维码到相册</text>
      </uni-button>

      <view class="share-group">
        <uni-button class="share-btn" @click="shareToWechat">
          <uni-icons class="btn-icon" type="weixin" size="20" color="#07C160"/>
          <text>微信好友</text>
        </uni-button>
        <uni-button class="share-btn" @click="shareToTimeline">
          <uni-icons class="btn-icon" type="pyq" size="20" color="#07C160"/>
          <text>朋友圈</text>
        </uni-button>
      </view>

      <uni-button class="link-btn" @click="generateLink">
        <uni-icons class="btn-icon" type="link" size="20" color="#666"/>
        <text>生成名片外链</text>
      </uni-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cardId: null,
      qrCodeUrl: 'https://ai-public.mastergo.com/ai/img_res/cd80956f861f8756fd726d3bbbc690b2.jpg'
    }
  },
  onLoad(options) {
    if (options.cardId) {
      this.cardId = options.cardId
      this.generateQRCode()
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 生成二维码
    generateQRCode() {
      // 调用API生成二维码
      // this.$api.card.generateQRCode(this.cardId).then(res => {
      //   this.qrCodeUrl = res.data.qrCodeUrl
      // })
    },

    // 保存二维码
    saveQrCode() {
      uni.showLoading({ title: '保存中...' })
      
      // 下载图片到本地
      uni.downloadFile({
        url: this.qrCodeUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 保存到相册
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                uni.showToast({ title: '保存成功', icon: 'success' })
              },
              fail: () => {
                uni.showToast({ title: '保存失败', icon: 'error' })
              }
            })
          }
        },
        fail: () => {
          uni.showToast({ title: '下载失败', icon: 'error' })
        },
        complete: () => {
          uni.hideLoading()
        }
      })
    },

    // 分享到微信
    shareToWechat() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: `https://example.com/card/${this.cardId}`,
        title: '我的名片',
        summary: '快来查看我的电子名片吧！',
        imageUrl: this.qrCodeUrl,
        success: () => {
          uni.showToast({ title: '分享成功', icon: 'success' })
        },
        fail: () => {
          uni.showToast({ title: '分享失败', icon: 'error' })
        }
      })
    },

    // 分享到朋友圈
    shareToTimeline() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneTimeline',
        type: 0,
        href: `https://example.com/card/${this.cardId}`,
        title: '我的名片',
        summary: '快来查看我的电子名片吧！',
        imageUrl: this.qrCodeUrl,
        success: () => {
          uni.showToast({ title: '分享成功', icon: 'success' })
        },
        fail: () => {
          uni.showToast({ title: '分享失败', icon: 'error' })
        }
      })
    },

    // 生成外链
    generateLink() {
      const link = `https://example.com/card/${this.cardId}`
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({ title: '链接已复制', icon: 'success' })
        }
      })
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  padding-top: calc(20rpx + var(--status-bar-height));
}

.back-icon {
  margin-right: 20rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
}

.qr-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 40rpx;
}

.qr-card {
  width: 400rpx;
  height: 400rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.qr-image {
  width: 100%;
  height: 100%;
}

.qr-tip {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.9;
}

.action-container {
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 40rpx 40rpx 0 0;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background-color: #2B85E4;
  border: none;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 16px;
  font-weight: bold;
}

.share-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.share-btn {
  flex: 1;
  height: 88rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 14px;
  color: #333333;
}

.link-btn {
  width: 100%;
  height: 88rpx;
  background-color: #F8F9FA;
  border: 1rpx solid #E9ECEF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-size: 16px;
  color: #666666;
}

.btn-icon {
  margin-right: 8rpx;
}
</style>