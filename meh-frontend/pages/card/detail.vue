<template>
  <view class="container">
    <view class="header">
      <view class="title">名片详情</view>
      <view class="actions">
        <view class="action-btn" @click="shareCard">
          <image class="action-icon" src="/static/images/icons/share.png" mode="aspectFit"></image>
          <text class="action-text">分享</text>
        </view>
      </view>
    </view>
    
    <!-- 名片内容 -->
    <scroll-view class="card-container" scroll-y>
      <!-- 名片基本信息 -->
      <view class="card-header" :style="{'background-image': `url(${cardInfo.templateBg || '/static/images/default-card-bg.png'})`}">
        <view class="card-avatar">
          <image class="avatar" :src="cardInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
        </view>
        <view class="card-basic">
          <view class="card-name">{{cardInfo.name || ''}}</view>
          <view class="card-position">{{cardInfo.position || ''}} | {{cardInfo.company || ''}}</view>
        </view>
      </view>
      
      <!-- 名片详细信息 -->
      <view class="card-info">
        <view class="info-section">
          <view class="section-title">联系方式</view>
          
          <view class="info-item" v-if="cardInfo.phone">
            <image class="info-icon" src="/static/images/icons/phone.png" mode="aspectFit"></image>
            <text class="info-text">{{cardInfo.phone}}</text>
            <view class="info-action" @click="callPhone(cardInfo.phone)">
              <image class="action-icon" src="/static/images/icons/call.png" mode="aspectFit"></image>
            </view>
          </view>
          
          <view class="info-item" v-if="cardInfo.email">
            <image class="info-icon" src="/static/images/icons/email.png" mode="aspectFit"></image>
            <text class="info-text">{{cardInfo.email}}</text>
            <view class="info-action" @click="copyText(cardInfo.email)">
              <image class="action-icon" src="/static/images/icons/copy.png" mode="aspectFit"></image>
            </view>
          </view>
          
          <view class="info-item" v-if="cardInfo.wechat">
            <image class="info-icon" src="/static/images/icons/wechat.png" mode="aspectFit"></image>
            <text class="info-text">{{cardInfo.wechat}}</text>
            <view class="info-action" @click="copyText(cardInfo.wechat)">
              <image class="action-icon" src="/static/images/icons/copy.png" mode="aspectFit"></image>
            </view>
          </view>
          
          <view class="info-item" v-if="cardInfo.address">
            <image class="info-icon" src="/static/images/icons/address.png" mode="aspectFit"></image>
            <text class="info-text">{{cardInfo.address}}</text>
            <view class="info-action" @click="openLocation(cardInfo.address)">
              <image class="action-icon" src="/static/images/icons/location.png" mode="aspectFit"></image>
            </view>
          </view>
        </view>
        
        <!-- 个人介绍 -->
        <view class="info-section" v-if="cardInfo.introduction || cardInfo.skills">
          <view class="section-title">个人介绍</view>
          
          <view class="info-block" v-if="cardInfo.introduction">
            <view class="block-title">个人简介</view>
            <view class="block-content">{{cardInfo.introduction}}</view>
          </view>
          
          <view class="info-block" v-if="cardInfo.skills">
            <view class="block-title">专业技能</view>
            <view class="skills-list">
              <view class="skill-tag" v-for="(skill, index) in skillsList" :key="index">{{skill}}</view>
            </view>
          </view>
        </view>
        
        <!-- 资源与项目 -->
        <view class="info-section" v-if="cardInfo.resources || cardInfo.projects">
          <view class="section-title">资源与项目</view>
          
          <view class="info-block" v-if="cardInfo.resources">
            <view class="block-title">我的资源</view>
            <view class="block-content">{{cardInfo.resources}}</view>
          </view>
          
          <view class="info-block" v-if="cardInfo.projects">
            <view class="block-title">我的项目</view>
            <view class="block-content">{{cardInfo.projects}}</view>
          </view>
        </view>
      </view>
      
      <!-- 社交互动区 -->
      <view class="social-section">
        <view class="social-stats">
          <view class="stat-item">
            <text class="stat-value">{{cardInfo.visitCount || 0}}</text>
            <text class="stat-label">访问</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{cardInfo.likeCount || 0}}</text>
            <text class="stat-label">点赞</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{cardInfo.commentCount || 0}}</text>
            <text class="stat-label">留言</text>
          </view>
        </view>
        
        <view class="social-actions">
          <view class="social-btn" :class="{'active': isLiked}" @click="handleLike">
            <image class="social-icon" :src="isLiked ? '/static/images/icons/like-active.png' : '/static/images/icons/like.png'" mode="aspectFit"></image>
            <text class="social-text">{{isLiked ? '已点赞' : '点赞'}}</text>
          </view>
          <view class="social-btn" @click="navigateTo('/pages/card/comment?id=' + cardId)">
            <image class="social-icon" src="/static/images/icons/comment.png" mode="aspectFit"></image>
            <text class="social-text">留言</text>
          </view>
          <view class="social-btn" @click="handleSaveContact">
            <image class="social-icon" src="/static/images/icons/contact-add.png" mode="aspectFit"></image>
            <text class="social-text">保存</text>
          </view>
          <view class="social-btn" @click="navigateTo('/pages/chat/index?userId=' + cardInfo.userId)">
            <image class="social-icon" src="/static/images/icons/message.png" mode="aspectFit"></image>
            <text class="social-text">咨询</text>
          </view>
        </view>
      </view>
      
      <!-- 二维码区域 -->
      <view class="qrcode-section">
        <view class="qrcode-title">扫码添加我的名片</view>
        <image class="qrcode-image" :src="cardInfo.qrcode || ''" mode="aspectFit"></image>
        <button class="save-qrcode-btn" @click="saveQrcode">保存二维码</button>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      cardId: null,
      cardInfo: {},
      isLiked: false,
      skillsList: []
    }
  },
  onLoad(options) {
    if (options.id) {
      this.cardId = options.id
      this.getCardDetail(options.id)
      this.checkIsLiked(options.id)
    }
  },
  methods: {
    // 获取名片详情
    getCardDetail(id) {
      // 调用CardController.getCardDetail方法
      this.$api.get(`/cards/${id}`).then(res => {
        if (res) {
          this.cardInfo = res
          
          // 处理技能标签
          if (this.cardInfo.skills) {
            this.skillsList = this.cardInfo.skills.split(',').map(item => item.trim()).filter(item => item)
          }
          
          // 记录访问
          this.recordVisit(id)
        }
      })
    },
    
    // 记录访问
    recordVisit(id) {
      // 调用CardController.visitCard方法
      this.$api.post(`/cards/${id}/visit`).then(() => {
        // 访问记录成功，后端会增加访问计数
        console.log('访问记录已保存')
      })
    },
    
    // 检查是否已点赞
    checkIsLiked(id) {
      // 这个接口需要在后端实现，检查当前用户是否已点赞该名片
      // 可以通过查询CardLike表实现
      this.$api.get(`/cards/${id}/like/check`).then(res => {
        this.isLiked = res
      }).catch(() => {
        // 如果接口不存在，默认为未点赞
        this.isLiked = false
      })
    },
    
    // 处理点赞
    handleLike() {
      if (this.isLiked) {
        // 取消点赞，调用CardController.unlikeCard方法
        this.$api.delete(`/cards/${this.cardId}/like`).then(() => {
          this.isLiked = false
          this.cardInfo.likeCount = Math.max(0, (this.cardInfo.likeCount || 0) - 1)
          
          uni.showToast({
            title: '已取消点赞',
            icon: 'none'
          })
        })
      } else {
        // 点赞，调用CardController.likeCard方法
        this.$api.post(`/cards/${this.cardId}/like`).then(() => {
          this.isLiked = true
          this.cardInfo.likeCount = (this.cardInfo.likeCount || 0) + 1
          
          uni.showToast({
            title: '点赞成功',
            icon: 'success'
          })
        })
      }
    },
    
    // 保存为联系人
    handleSaveContact() {
      uni.showModal({
        title: '保存联系人',
        content: '是否将此名片保存到通讯录？',
        success: (res) => {
          if (res.confirm) {
            // 构建联系人数据对象，与后端ContactCreateDTO保持一致
            const contactData = {
              name: this.cardInfo.name,
              phone: this.cardInfo.phone,
              company: this.cardInfo.company,
              position: this.cardInfo.position,
              email: this.cardInfo.email,
              wechat: this.cardInfo.wechat,
              address: this.cardInfo.address,
              remark: '',
              cardId: this.cardId
            }
            
            // 调用ContactController.createContact方法
            this.$api.post('/contacts', contactData).then(() => {
              uni.showToast({
                title: '已保存到通讯录',
                icon: 'success'
              })
            })
          }
        }
      })
    },
    
    // 分享名片
    shareCard() {
      uni.navigateTo({
        url: `/pages/card/share?id=${this.cardId}`
      })
    },
    
    // 保存二维码
    saveQrcode() {
      if (!this.cardInfo.qrcode) {
        uni.showToast({
          title: '二维码不存在',
          icon: 'none'
        })
        return
      }
      
      // 获取二维码临时路径
      uni.getImageInfo({
        src: this.cardInfo.qrcode,
        success: (res) => {
          // 保存图片到相册
          uni.saveImageToPhotosAlbum({
            filePath: res.path,
            success: () => {
              uni.showToast({
                title: '二维码已保存到相册',
                icon: 'success'
              })
            },
            fail: () => {
              uni.showToast({
                title: '保存失败，请检查权限',
                icon: 'none'
              })
            }
          })
        },
        fail: () => {
          uni.showToast({
            title: '获取二维码失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 拨打电话
    callPhone(phone) {
      uni.makePhoneCall({
        phoneNumber: phone
      })
    },
    
    // 复制文本
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          })
        }
      })
    },
    
    // 打开地图
    openLocation(address) {
      // 由于地址是文本，需要先进行地理编码
      uni.showToast({
        title: '正在获取位置信息',
        icon: 'loading',
        duration: 2000
      })
      
      // 实际项目中应调用地图API进行地理编码
      // 这里简化处理，仅显示地址
      setTimeout(() => {
        uni.showModal({
          title: '地址信息',
          content: address,
          showCancel: false
        })
      }, 1000)
    },
    
    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({
        url
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }
  
  .actions {
    display: flex;
    
    .action-btn {
      display: flex;
      align-items: center;
      
      .action-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 10rpx;
      }
      
      .action-text {
        font-size: 28rpx;
        color: #666666;
      }
    }
  }
}

.card-container {
  flex: 1;
}

.card-header {
  padding: 40rpx 30rpx;
  background-size: cover;
  background-position: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
  }
  
  .card-avatar {
    position: relative;
    z-index: 1;
    margin-bottom: 20rpx;
    
    .avatar {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      border: 4rpx solid #ffffff;
    }
  }
  
  .card-basic {
    position: relative;
    z-index: 1;
    
    .card-name {
      font-size: 40rpx;
      font-weight: bold;
      color: #ffffff;
      margin-bottom: 10rpx;
    }
    
    .card-position {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.card-info {
  padding: 30rpx;
  
  .info-section {
    background-color: #ffffff;
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      margin-bottom: 20rpx;
      position: relative;
      padding-left: 20rpx;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 30rpx;
        background-color: #1296db;
        border-radius: 3rpx;
      }
    }
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .info-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 20rpx;
      }
      
      .info-text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      
      .info-action {
        .action-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
    
    .info-block {
      margin-bottom: 30rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .block-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 10rpx;
      }
      
      .block-content {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.6;
      }
      
      .skills-list {
        display: flex;
        flex-wrap: wrap;
        
        .skill-tag {
          background-color: #f0f9ff;
          color: #1296db;
          font-size: 24rpx;
          padding: 6rpx 20rpx;
          border-radius: 30rpx;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
        }
      }
    }
  }
}

.social-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .social-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
    
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .stat-value {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 6rpx;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  
  .social-actions {
    display: flex;
    justify-content: space-around;
    
    .social-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      &.active {
        .social-text {
          color: #1296db;
        }
      }
      
      .social-icon {
        width: 50rpx;
        height: 50rpx;
        margin-bottom: 10rpx;
      }
      
      .social-text {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
}

.qrcode-section {
  background-color: #ffffff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
  
  .qrcode-title {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 20rpx;
  }
  
  .qrcode-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 20rpx;
  }
  
  .save-qrcode-btn {
    background-color: #1296db;
    color: #ffffff;
    font-size: 28rpx;
    padding: 10rpx 40rpx;
    border-radius: 30rpx;
  }
}
</style>
