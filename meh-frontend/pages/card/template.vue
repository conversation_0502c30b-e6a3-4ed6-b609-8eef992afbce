<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#333333" @click="goBack"/>
      <text class="title">选择名片模板</text>
    </view>
    
    <!-- 分类筛选栏 -->
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          v-for="(item, index) in categories" 
          :key="index"
          :class="['category-item', currentCategory === index ? 'active' : '']"
          @tap="selectCategory(index)"
        >
          {{ item }}
        </view>
      </view>
    </scroll-view>

    <!-- 模板展示区 -->
    <scroll-view class="template-container" scroll-y>
      <view class="template-grid">
        <view 
          v-for="(template, index) in filteredTemplates" 
          :key="index"
          class="template-item"
          @tap="selectTemplate(index)"
        >
          <view class="template-image-wrapper">
            <image :src="template.image" mode="aspectFill" class="template-image"/>
            <uni-icons 
              v-if="selectedTemplate === index" 
              class="check-icon" 
              type="checkbox-filled" 
              size="24" 
              color="#2B85E4"
            />
          </view>
          <text class="template-name">{{ template.name }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <button class="confirm-button" @tap="confirmSelection">确认选择</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      categories: ['全部', '商务型', '时尚型', '简洁型', '创意型', '科技型'],
      currentCategory: 0,
      selectedTemplate: -1,
      templates: [
        {
          id: 1,
          name: '商务简约风格',
          category: 1,
          image: 'https://ai-public.mastergo.com/ai/img_res/205710c1dbdee1c3f4201fc30654753b.jpg'
        },
        {
          id: 2,
          name: '科技创新风格',
          category: 5,
          image: 'https://ai-public.mastergo.com/ai/img_res/da7941ccd8be8378be442930486666e4.jpg'
        },
        {
          id: 3,
          name: '艺术时尚风格',
          category: 2,
          image: 'https://ai-public.mastergo.com/ai/img_res/771599a8e2086813d5f5217cabc2dd28.jpg'
        },
        {
          id: 4,
          name: '简约轻奢风格',
          category: 3,
          image: 'https://ai-public.mastergo.com/ai/img_res/dd773cce6cdfa7123ae8ddea8427d730.jpg'
        },
        {
          id: 5,
          name: '创意个性风格',
          category: 4,
          image: 'https://ai-public.mastergo.com/ai/img_res/205710c1dbdee1c3f4201fc30654753b.jpg'
        },
        {
          id: 6,
          name: '经典商务风格',
          category: 1,
          image: 'https://ai-public.mastergo.com/ai/img_res/da7941ccd8be8378be442930486666e4.jpg'
        }
      ]
    }
  },
  computed: {
    filteredTemplates() {
      if (this.currentCategory === 0) {
        return this.templates
      }
      return this.templates.filter(template => template.category === this.currentCategory)
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    selectCategory(index) {
      this.currentCategory = index
      this.selectedTemplate = -1 // 重置选择
    },

    selectTemplate(index) {
      this.selectedTemplate = index
    },

    confirmSelection() {
      if (this.selectedTemplate === -1) {
        uni.showToast({
          title: '请选择模板',
          icon: 'none'
        })
        return
      }
      
      const selectedTemplateData = this.filteredTemplates[this.selectedTemplate]
      
      // 返回上一页并传递选中的模板数据
      const pages = getCurrentPages()
      const prevPage = pages[pages.length - 2]
      
      if (prevPage) {
        prevPage.$vm.selectedTemplate = selectedTemplateData
        prevPage.$vm.formData.templateId = selectedTemplateData.id
      }
      
      uni.showToast({
        title: '已选择模板',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    }
  }
}
</script>

<style>
page {
  height: 100%;
  background-color: #F8F9FA;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  margin-right: 20rpx;
}

.title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.category-scroll {
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #E9ECEF;
}

.category-list {
  display: flex;
  padding: 20rpx 30rpx;
  white-space: nowrap;
}

.category-item {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background-color: #F8F9FA;
  border-radius: 40rpx;
  font-size: 14px;
  color: #666666;
  white-space: nowrap;
}

.category-item.active {
  background-color: #2B85E4;
  color: #FFFFFF;
}

.template-container {
  flex: 1;
  padding: 30rpx;
}

.template-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.template-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.template-image-wrapper {
  position: relative;
  height: 300rpx;
}

.template-image {
  width: 100%;
  height: 100%;
}

.check-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  padding: 4rpx;
}

.template-name {
  display: block;
  padding: 20rpx;
  font-size: 14px;
  color: #333333;
  text-align: center;
}

.bottom-button {
  padding: 30rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #E9ECEF;
}

.confirm-button {
  width: 100%;
  height: 88rpx;
  background-color: #2B85E4;
  color: #FFFFFF;
  border: none;
  border-radius: 16rpx;
  font-size: 16px;
  font-weight: bold;
}

.confirm-button:active {
  background-color: #1E6BB8;
}
</style>