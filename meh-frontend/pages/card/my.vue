<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="nav-title">我的名片</text>
      <view class="nav-action" @click="createCard">
        <uni-icons type="plus" size="18" color="#2B85E4"></uni-icons>
        <text class="action-text">新建名片</text>
      </view>
    </view>

    <!-- 标签栏 -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-item" 
        :class="{ active: activeTab === tab.value }"
        @click="switchTab(tab.value)"
      >
        <text class="tab-text">{{ tab.label }}</text>
        <text class="tab-count">({{ getTabCount(tab.value) }})</text>
      </view>
    </view>

    <!-- 名片列表 -->
    <scroll-view class="card-list" scroll-y>
      <template v-if="filteredCards.length > 0">
        <view v-for="(card, index) in filteredCards" :key="index" class="card-item" @click="viewCard(card)">
          <view class="card-avatar">
            <image class="avatar-img" :src="card.avatar" mode="aspectFill"></image>
          </view>
          <view class="card-info">
            <view class="card-header">
              <text class="card-name">{{ card.name }}</text>
              <view class="card-type" :class="card.type">
                <text class="type-text">{{ card.type === 'personal' ? '个人' : '企业' }}</text>
              </view>
            </view>
            <text class="card-position">{{ card.position }}</text>
            <text class="card-company" v-if="card.company">{{ card.company }}</text>
            <view class="card-meta">
              <text class="update-time">{{ formatTime(card.updateTime) }}</text>
              <view class="card-stats">
                <view class="stat-item">
                  <uni-icons type="eye" size="12" color="#999999"></uni-icons>
                  <text class="stat-text">{{ card.viewCount }}</text>
                </view>
                <view class="stat-item">
                  <uni-icons type="heart" size="12" color="#999999"></uni-icons>
                  <text class="stat-text">{{ card.likeCount }}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="card-actions">
            <view class="action-btn" @click.stop="shareCard(card)">
              <uni-icons type="redo" size="16" color="#2B85E4"></uni-icons>
            </view>
            <view class="action-btn" @click.stop="editCard(card)">
              <uni-icons type="compose" size="16" color="#2B85E4"></uni-icons>
            </view>
            <view class="action-btn" @click.stop="showCardMenu(card)">
              <uni-icons type="more-filled" size="16" color="#666666"></uni-icons>
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">{{ getEmptyText() }}</text>
          <view class="create-btn" @click="createCard">
            <text class="create-text">去创建</text>
          </view>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'personal',
      tabs: [
        { label: '个人名片', value: 'personal' },
        { label: '企业名片', value: 'company' }
      ],
      cards: [
        {
          id: 1,
          type: 'personal',
          name: '张三',
          position: '产品经理',
          company: '阿里巴巴科技有限公司',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          updateTime: '2024-01-15 14:30:00',
          viewCount: 128,
          likeCount: 23,
          phone: '13800138001',
          email: '<EMAIL>',
          templateId: 1
        },
        {
          id: 2,
          type: 'company',
          name: '阿里巴巴科技有限公司',
          position: '企业名片',
          company: '',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          updateTime: '2024-01-14 16:20:00',
          viewCount: 256,
          likeCount: 45,
          phone: '************',
          email: '<EMAIL>',
          templateId: 2
        },
        {
          id: 3,
          type: 'personal',
          name: '李四',
          position: '技术总监',
          company: '腾讯科技有限公司',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
          updateTime: '2024-01-13 10:15:00',
          viewCount: 89,
          likeCount: 12,
          phone: '13800138002',
          email: '<EMAIL>',
          templateId: 3
        }
      ],
      emptyImageUrl: 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg'
    }
  },
  computed: {
    filteredCards() {
      return this.cards.filter(card => card.type === this.activeTab)
    }
  },
  onLoad() {
    this.loadMyCards()
  },
  onShow() {
    // 页面显示时刷新数据
    this.loadMyCards()
  },
  methods: {
    loadMyCards() {
      // 调用API获取我的名片列表
      // this.$api.card.getMyCards().then(res => {
      //   this.cards = res.data
      // })
    },
    
    switchTab(tabValue) {
      this.activeTab = tabValue
    },
    
    getTabCount(tabValue) {
      return this.cards.filter(card => card.type === tabValue).length
    },
    
    getEmptyText() {
      return this.activeTab === 'personal' ? '暂无个人名片，快去创建吧！' : '暂无企业名片，快去创建吧！'
    },
    
    createCard() {
      uni.navigateTo({
        url: `/pages/card/create?type=${this.activeTab}`
      })
    },
    
    viewCard(card) {
      uni.navigateTo({
        url: `/pages/card/detail?id=${card.id}`
      })
    },
    
    editCard(card) {
      uni.navigateTo({
        url: `/pages/card/create?id=${card.id}&type=${card.type}`
      })
    },
    
    shareCard(card) {
      uni.navigateTo({
        url: `/pages/card/share?id=${card.id}`
      })
    },
    
    showCardMenu(card) {
      uni.showActionSheet({
        itemList: ['设为默认', '复制名片', '删除名片'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.setDefaultCard(card)
              break
            case 1:
              this.copyCard(card)
              break
            case 2:
              this.deleteCard(card)
              break
          }
        }
      })
    },
    
    setDefaultCard(card) {
      uni.showModal({
        title: '设为默认',
        content: `确定要将「${card.name}」设为默认名片吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用API设置默认名片
            // this.$api.card.setDefault({ cardId: card.id }).then(() => {
            //   uni.showToast({
            //     title: '设置成功',
            //     icon: 'success'
            //   })
            // })
            uni.showToast({
              title: '设置成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    copyCard(card) {
      uni.showModal({
        title: '复制名片',
        content: `确定要复制「${card.name}」名片吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用API复制名片
            // this.$api.card.copy({ cardId: card.id }).then(res => {
            //   this.cards.unshift(res.data)
            //   uni.showToast({
            //     title: '复制成功',
            //     icon: 'success'
            //   })
            // })
            const newCard = {
              ...card,
              id: Date.now(),
              name: card.name + ' 副本',
              updateTime: new Date().toISOString(),
              viewCount: 0,
              likeCount: 0
            }
            this.cards.unshift(newCard)
            uni.showToast({
              title: '复制成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    deleteCard(card) {
      uni.showModal({
        title: '删除名片',
        content: `确定要删除「${card.name}」名片吗？删除后无法恢复。`,
        success: (res) => {
          if (res.confirm) {
            // 调用API删除名片
            // this.$api.card.delete({ cardId: card.id }).then(() => {
            //   const index = this.cards.findIndex(c => c.id === card.id)
            //   if (index > -1) {
            //     this.cards.splice(index, 1)
            //   }
            //   uni.showToast({
            //     title: '删除成功',
            //     icon: 'success'
            //   })
            // })
            const index = this.cards.findIndex(c => c.id === card.id)
            if (index > -1) {
              this.cards.splice(index, 1)
            }
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    formatTime(timeStr) {
      const now = new Date()
      const updateTime = new Date(timeStr)
      const diff = now.getTime() - updateTime.getTime()
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) {
        return '刚刚更新'
      } else if (minutes < 60) {
        return `${minutes}分钟前更新`
      } else if (hours < 24) {
        return `${hours}小时前更新`
      } else if (days < 7) {
        return `${days}天前更新`
      } else {
        const date = new Date(timeStr)
        return `${date.getMonth() + 1}-${date.getDate()} 更新`
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.nav-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 14px;
  color: #2B85E4;
  margin-left: 8rpx;
}

.tab-bar {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  position: relative;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #2B85E4;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 16px;
  color: #666666;
  margin-right: 8rpx;
}

.tab-item.active .tab-text {
  color: #2B85E4;
  font-weight: 500;
}

.tab-count {
  font-size: 12px;
  color: #999999;
}

.tab-item.active .tab-count {
  color: #2B85E4;
}

.card-list {
  flex: 1;
  padding: 24rpx 32rpx;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-item:last-child {
  margin-bottom: 0;
}

.card-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 60rpx;
}

.card-info {
  flex: 1;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.card-name {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  margin-right: 16rpx;
}

.card-type {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 10px;
}

.card-type.personal {
  background-color: #E8F4FD;
  color: #2B85E4;
}

.card-type.company {
  background-color: #FFF2E8;
  color: #FF8A00;
}

.type-text {
  font-size: 10px;
}

.card-position {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8rpx;
}

.card-company {
  font-size: 12px;
  color: #999999;
  margin-bottom: 16rpx;
}

.card-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.update-time {
  font-size: 12px;
  color: #999999;
}

.card-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
}

.stat-text {
  font-size: 12px;
  color: #999999;
  margin-left: 8rpx;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 32rpx;
  border: 1rpx solid #E9ECEF;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 32rpx;
}

.create-btn {
  width: 200rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2B85E4;
  border-radius: 36rpx;
}

.create-text {
  font-size: 16px;
  color: #FFFFFF;
}
</style>