<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">名片评论</text>
      <view class="nav-right">
        <text class="comment-count">{{ totalComments }}条评论</text>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 评论列表 -->
      <view class="comments-section">
        <view class="comments-list">
          <view
            class="comment-item"
            v-for="comment in comments"
            :key="comment.id"
          >
            <!-- 评论者信息 -->
            <view class="comment-header">
              <view class="user-info">
                <image
                  class="user-avatar"
                  :src="comment.userAvatar || '/static/images/default-avatar.png'"
                  mode="aspectFill"
                ></image>
                <view class="user-details">
                  <view class="user-name">{{ comment.userName || '匿名用户' }}</view>
                  <view class="comment-time">{{ formatTime(comment.createTime) }}</view>
                </view>
              </view>

              <view class="comment-actions">
                <view class="action-btn" @click="likeComment(comment)">
                  <uni-icons
                    :type="comment.isLiked ? 'heart-filled' : 'heart'"
                    size="16"
                    :color="comment.isLiked ? '#ff6b35' : '#999999'"
                  ></uni-icons>
                  <text :class="{liked: comment.isLiked}">{{ comment.likeCount || 0 }}</text>
                </view>
                <view class="action-btn" @click="replyComment(comment)">
                  <uni-icons type="chatbubbles" size="16" color="#999999"></uni-icons>
                  <text>回复</text>
                </view>
              </view>
            </view>

            <!-- 评论内容 -->
            <view class="comment-content">
              <text>{{ comment.content }}</text>
            </view>

            <!-- 回复列表 -->
            <view class="replies-list" v-if="comment.replies && comment.replies.length > 0">
              <view
                class="reply-item"
                v-for="reply in comment.replies"
                :key="reply.id"
              >
                <view class="reply-header">
                  <image
                    class="reply-avatar"
                    :src="reply.userAvatar || '/static/images/default-avatar.png'"
                    mode="aspectFill"
                  ></image>
                  <view class="reply-info">
                    <text class="reply-user">{{ reply.userName || '匿名用户' }}</text>
                    <text class="reply-time">{{ formatTime(reply.createTime) }}</text>
                  </view>
                </view>
                <view class="reply-content">
                  <text>{{ reply.content }}</text>
                </view>
              </view>

              <!-- 查看更多回复 -->
              <view
                class="more-replies"
                v-if="comment.replyCount > comment.replies.length"
                @click="loadMoreReplies(comment)"
              >
                <text>查看更多回复 ({{ comment.replyCount - comment.replies.length }}条)</text>
                <uni-icons type="down" size="12" color="#999999"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="comments.length === 0 && !loading">
            <uni-icons type="chatbubbles" size="60" color="#cccccc"></uni-icons>
            <text class="empty-text">暂无评论</text>
            <text class="empty-desc">快来发表第一条评论吧</text>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore" @click="loadMore">
          <text>加载更多评论</text>
        </view>
      </view>
    </view>

    <!-- 底部评论输入框 -->
    <view class="comment-input-bar">
      <view class="input-container">
        <input
          class="comment-input"
          v-model="commentText"
          :placeholder="replyTarget ? `回复 ${replyTarget.userName}` : '写下你的评论...'"
          :focus="inputFocus"
          @blur="onInputBlur"
        />
        <view class="input-actions">
          <view class="emoji-btn" @click="showEmojiPanel">
            <uni-icons type="paperplane" size="20" color="#999999"></uni-icons>
          </view>
          <button
            class="send-btn"
            :class="{active: commentText.trim()}"
            @click="sendComment"
            :disabled="!commentText.trim() || sending"
          >
            发送
          </button>
        </view>
      </view>

      <!-- 回复提示 -->
      <view class="reply-tip" v-if="replyTarget">
        <text>回复 {{ replyTarget.userName }}</text>
        <uni-icons type="clear" size="16" color="#999999" @click="cancelReply"></uni-icons>
      </view>
    </view>

    <!-- 表情面板 -->
    <view class="emoji-panel" v-if="showEmoji">
      <view class="emoji-grid">
        <view
          class="emoji-item"
          v-for="emoji in emojiList"
          :key="emoji"
          @click="insertEmoji(emoji)"
        >
          {{ emoji }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/api/index.js'

export default {
  data() {
    return {
      cardId: null,
      comments: [],
      totalComments: 0,
      commentText: '',
      replyTarget: null,
      inputFocus: false,
      showEmoji: false,
      sending: false,
      loading: false,
      hasMore: true,
      page: 1,
      pageSize: 20,
      emojiList: [
        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
        '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
        '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
        '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
        '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
        '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
        '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
        '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
        '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐'
      ]
    }
  },

  onLoad(options) {
    this.cardId = options.cardId
    this.loadComments()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 加载评论列表
    async loadComments(loadMore = false) {
      try {
        this.loading = true

        if (!loadMore) {
          this.page = 1
          this.comments = []
        }

        const res = await api.get('/card/comments', {
          cardId: this.cardId,
          page: this.page,
          pageSize: this.pageSize
        })

        if (res.success) {
          const newComments = res.data.records || []
          this.comments = loadMore ? [...this.comments, ...newComments] : newComments
          this.totalComments = res.data.total || 0
          this.hasMore = newComments.length === this.pageSize
        }
      } catch (error) {
        console.error('加载评论失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载更多评论
    loadMore() {
      this.page++
      this.loadComments(true)
    },

    // 加载更多回复
    async loadMoreReplies(comment) {
      try {
        const res = await api.get('/card/comment/replies', {
          commentId: comment.id,
          page: Math.floor(comment.replies.length / 10) + 1,
          pageSize: 10
        })

        if (res.success) {
          const newReplies = res.data.records || []
          comment.replies = [...comment.replies, ...newReplies]
        }
      } catch (error) {
        console.error('加载回复失败:', error)
      }
    },

    // 点赞评论
    async likeComment(comment) {
      try {
        const res = await api.post('/card/comment/like', {
          commentId: comment.id
        })

        if (res.success) {
          comment.isLiked = !comment.isLiked
          comment.likeCount = comment.isLiked ?
            (comment.likeCount || 0) + 1 :
            Math.max((comment.likeCount || 0) - 1, 0)
        }
      } catch (error) {
        console.error('点赞失败:', error)
      }
    },

    // 回复评论
    replyComment(comment) {
      this.replyTarget = comment
      this.inputFocus = true
    },

    // 取消回复
    cancelReply() {
      this.replyTarget = null
    },

    // 发送评论
    async sendComment() {
      if (!this.commentText.trim() || this.sending) return

      try {
        this.sending = true

        const params = {
          cardId: this.cardId,
          content: this.commentText.trim()
        }

        if (this.replyTarget) {
          params.parentId = this.replyTarget.id
        }

        const res = await api.post('/card/comment', params)

        if (res.success) {
          this.commentText = ''
          this.replyTarget = null

          uni.showToast({
            title: '评论成功',
            icon: 'success'
          })

          // 重新加载评论列表
          this.loadComments()
        } else {
          uni.showToast({
            title: res.message || '评论失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('发送评论失败:', error)
        uni.showToast({
          title: '评论失败',
          icon: 'none'
        })
      } finally {
        this.sending = false
      }
    },

    // 输入框失焦
    onInputBlur() {
      this.inputFocus = false
      // 延迟隐藏表情面板，避免点击表情时面板消失
      setTimeout(() => {
        this.showEmoji = false
      }, 200)
    },

    // 显示表情面板
    showEmojiPanel() {
      this.showEmoji = !this.showEmoji
      this.inputFocus = true
    },

    // 插入表情
    insertEmoji(emoji) {
      this.commentText += emoji
    },

    // 格式化时间
    formatTime(dateTime) {
      const date = new Date(dateTime)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      const minute = 60 * 1000
      const hour = 60 * minute
      const day = 24 * hour

      if (diff < minute) {
        return '刚刚'
      } else if (diff < hour) {
        return `${Math.floor(diff / minute)}分钟前`
      } else if (diff < day) {
        return `${Math.floor(diff / hour)}小时前`
      } else if (diff < 7 * day) {
        return `${Math.floor(diff / day)}天前`
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 20rpx;
  flex: 1;
}

.comment-count {
  font-size: 26rpx;
  color: #999999;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

/* 评论列表 */
.comments-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.comment-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999999;
}

.comment-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
}

.action-btn text {
  margin-left: 8rpx;
}

.action-btn text.liked {
  color: #ff6b35;
}

.comment-content {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 回复列表 */
.replies-list {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.reply-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eeeeee;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.reply-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  margin-right: 12rpx;
}

.reply-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.reply-user {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.reply-time {
  font-size: 20rpx;
  color: #999999;
}

.reply-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #333333;
}

.more-replies {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999999;
  border-top: 1rpx solid #eeeeee;
  margin-top: 20rpx;
}

.more-replies text {
  margin-right: 8rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999999;
  margin: 20rpx 0 8rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #cccccc;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

/* 底部输入框 */
.comment-input-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
  z-index: 100;
}

.input-container {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
}

.comment-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background-color: transparent;
  border: none;
  outline: none;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.emoji-btn {
  padding: 8rpx;
}

.send-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  background-color: #cccccc;
  color: #ffffff;
  border: none;
}

.send-btn.active {
  background-color: #ff6b35;
}

.reply-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 20rpx;
  background-color: #fff7f0;
  border-radius: 8rpx;
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #ff6b35;
}

/* 表情面板 */
.emoji-panel {
  position: fixed;
  bottom: 120rpx;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
  z-index: 99;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 20rpx;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 32rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
</style>
