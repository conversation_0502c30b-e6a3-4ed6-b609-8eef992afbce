<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">访客记录</text>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalVisitors }}</text>
          <text class="stats-label">总访客数</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ todayVisitors }}</text>
          <text class="stats-label">今日访客</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ weekVisitors }}</text>
          <text class="stats-label">本周访客</text>
        </view>
      </view>
    </view>

    <!-- 访客列表 -->
    <scroll-view class="visitor-list" scroll-y>
      <template v-if="visitors.length > 0">
        <view v-for="(visitor, index) in visitors" :key="index" class="visitor-item" @click="viewVisitorDetail(visitor)">
          <view class="visitor-avatar">
            <image class="avatar-img" :src="visitor.avatar" mode="aspectFill"></image>
          </view>
          <view class="visitor-info">
            <view class="visitor-name">{{ visitor.name }}</view>
            <view class="visitor-company">{{ visitor.company }}</view>
            <view class="visitor-position">{{ visitor.position }}</view>
            <view class="visitor-time">
              <uni-icons class="time-icon" type="clock" size="12" color="#999999"></uni-icons>
              <text class="time-text">{{ formatTime(visitor.visitTime) }}</text>
            </view>
          </view>
          <view class="visitor-actions">
            <view class="action-btn" @click.stop="addToContacts(visitor)">
              <uni-icons type="person-add" size="16" color="#2B85E4"></uni-icons>
            </view>
            <view class="action-btn" @click.stop="startChat(visitor)">
              <uni-icons type="chat" size="16" color="#2B85E4"></uni-icons>
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <image class="empty-image" :src="emptyImageUrl" mode="aspectFit"></image>
          <text class="empty-text">暂无访客记录</text>
          <text class="empty-desc">分享您的名片，让更多人了解您</text>
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      totalVisitors: 156,
      todayVisitors: 8,
      weekVisitors: 32,
      visitors: [
        {
          id: 1,
          name: '张三',
          company: '阿里巴巴科技有限公司',
          position: '产品经理',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          visitTime: '2024-01-15 14:30:25',
          phone: '13800138001',
          email: '<EMAIL>'
        },
        {
          id: 2,
          name: '李四',
          company: '腾讯科技有限公司',
          position: '技术总监',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          visitTime: '2024-01-15 11:20:15',
          phone: '13800138002',
          email: '<EMAIL>'
        },
        {
          id: 3,
          name: '王五',
          company: '百度在线网络技术有限公司',
          position: '架构师',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg',
          visitTime: '2024-01-14 16:45:30',
          phone: '13800138003',
          email: '<EMAIL>'
        },
        {
          id: 4,
          name: '赵六',
          company: '字节跳动科技有限公司',
          position: '运营总监',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg',
          visitTime: '2024-01-14 09:15:45',
          phone: '13800138004',
          email: '<EMAIL>'
        },
        {
          id: 5,
          name: '孙七',
          company: '美团科技有限公司',
          position: '设计总监',
          avatar: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg',
          visitTime: '2024-01-13 15:20:10',
          phone: '13800138005',
          email: '<EMAIL>'
        }
      ],
      emptyImageUrl: 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg'
    }
  },
  onLoad() {
    this.loadVisitors()
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    loadVisitors() {
      // 调用API加载访客记录
      // this.$api.visitor.getList().then(res => {
      //   this.visitors = res.data.visitors
      //   this.totalVisitors = res.data.totalVisitors
      //   this.todayVisitors = res.data.todayVisitors
      //   this.weekVisitors = res.data.weekVisitors
      // })
    },
    
    viewVisitorDetail(visitor) {
      uni.navigateTo({
        url: `/pages/visitors/detail?id=${visitor.id}`
      })
    },
    
    addToContacts(visitor) {
      uni.showModal({
        title: '添加联系人',
        content: `确定要将 ${visitor.name} 添加到通讯录吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用API添加到通讯录
            // this.$api.contact.create({
            //   name: visitor.name,
            //   company: visitor.company,
            //   position: visitor.position,
            //   phone: visitor.phone,
            //   email: visitor.email,
            //   avatar: visitor.avatar
            // }).then(() => {
            //   uni.showToast({
            //     title: '添加成功',
            //     icon: 'success'
            //   })
            // })
            uni.showToast({
              title: '添加成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    startChat(visitor) {
      uni.navigateTo({
        url: `/pages/chat/index?userId=${visitor.id}&userName=${visitor.name}`
      })
    },
    
    formatTime(timeStr) {
      const now = new Date()
      const visitTime = new Date(timeStr)
      const diff = now.getTime() - visitTime.getTime()
      
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 1) {
        return '刚刚'
      } else if (minutes < 60) {
        return `${minutes}分钟前`
      } else if (hours < 24) {
        return `${hours}小时前`
      } else if (days < 7) {
        return `${days}天前`
      } else {
        return timeStr.split(' ')[0]
      }
    }
  }
}
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 24rpx;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.stats-section {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #E9ECEF;
}

.stats-card {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 0;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #2B85E4;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 12px;
  color: #666666;
}

.stats-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: #E9ECEF;
}

.visitor-list {
  flex: 1;
  padding: 0 32rpx;
}

.visitor-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}

.visitor-item:last-child {
  margin-bottom: 0;
}

.visitor-avatar {
  width: 96rpx;
  height: 96rpx;
  margin-right: 24rpx;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 48rpx;
}

.visitor-info {
  flex: 1;
}

.visitor-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.visitor-company {
  font-size: 14px;
  color: #666666;
  margin-bottom: 4rpx;
}

.visitor-position {
  font-size: 12px;
  color: #999999;
  margin-bottom: 8rpx;
}

.visitor-time {
  display: flex;
  align-items: center;
}

.time-icon {
  margin-right: 8rpx;
}

.time-text {
  font-size: 12px;
  color: #999999;
}

.visitor-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 32rpx;
  border: 1rpx solid #E9ECEF;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
}
</style>