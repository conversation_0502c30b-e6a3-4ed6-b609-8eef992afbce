<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333" @click="goBack"></uni-icons>
      <text class="nav-title">访客详情</text>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 访客信息卡片 -->
      <view class="visitor-card">
        <view class="visitor-avatar">
          <image
            class="avatar"
            :src="visitorInfo.avatar || '/static/images/default-avatar.png'"
            mode="aspectFill"
          ></image>
          <view class="online-status" :class="{online: visitorInfo.isOnline}"></view>
        </view>

        <view class="visitor-info">
          <view class="visitor-name">{{ visitorInfo.name || '匿名访客' }}</view>
          <view class="visitor-position" v-if="visitorInfo.position">
            {{ visitorInfo.position }}
          </view>
          <view class="visitor-company" v-if="visitorInfo.company">
            {{ visitorInfo.company }}
          </view>
          <view class="visitor-location" v-if="visitorInfo.location">
            <uni-icons type="location" size="12" color="#999999"></uni-icons>
            <text>{{ visitorInfo.location }}</text>
          </view>
        </view>

        <view class="visitor-actions">
          <button class="action-btn primary" @click="addToContacts" v-if="!isContact">
            <uni-icons type="person-add" size="16" color="#ffffff"></uni-icons>
            <text>添加联系人</text>
          </button>
          <button class="action-btn" @click="sendMessage">
            <uni-icons type="chatbubbles" size="16" color="#ff6b35"></uni-icons>
            <text>发消息</text>
          </button>
        </view>
      </view>

      <!-- 访问统计 -->
      <view class="stats-section">
        <view class="section-title">访问统计</view>
        <view class="stats-grid">
          <view class="stats-item">
            <view class="stats-number">{{ visitStats.totalVisits }}</view>
            <view class="stats-label">总访问次数</view>
          </view>
          <view class="stats-item">
            <view class="stats-number">{{ visitStats.totalDuration }}</view>
            <view class="stats-label">总停留时长</view>
          </view>
          <view class="stats-item">
            <view class="stats-number">{{ visitStats.avgDuration }}</view>
            <view class="stats-label">平均停留</view>
          </view>
          <view class="stats-item">
            <view class="stats-number">{{ visitStats.lastVisitDays }}</view>
            <view class="stats-label">最近访问</view>
          </view>
        </view>
      </view>

      <!-- 访问记录 -->
      <view class="records-section">
        <view class="section-title">
          <text>访问记录</text>
          <view class="filter-tabs">
            <view
              class="filter-tab"
              :class="{active: activeFilter === 'all'}"
              @click="setFilter('all')"
            >
              全部
            </view>
            <view
              class="filter-tab"
              :class="{active: activeFilter === 'week'}"
              @click="setFilter('week')"
            >
              近7天
            </view>
            <view
              class="filter-tab"
              :class="{active: activeFilter === 'month'}"
              @click="setFilter('month')"
            >
              近30天
            </view>
          </view>
        </view>

        <view class="records-list">
          <view
            class="record-item"
            v-for="record in filteredRecords"
            :key="record.id"
          >
            <view class="record-time">
              <view class="time-main">{{ formatTime(record.createTime) }}</view>
              <view class="time-sub">{{ formatDate(record.createTime) }}</view>
            </view>

            <view class="record-content">
              <view class="record-source">
                <uni-icons
                  :type="getSourceIcon(record.source)"
                  size="16"
                  :color="getSourceColor(record.source)"
                ></uni-icons>
                <text>{{ getSourceText(record.source) }}</text>
              </view>

              <view class="record-details">
                <view class="detail-item" v-if="record.stayTime">
                  <text class="detail-label">停留时长：</text>
                  <text class="detail-value">{{ formatDuration(record.stayTime) }}</text>
                </view>
                <view class="detail-item" v-if="record.visitorDevice">
                  <text class="detail-label">设备：</text>
                  <text class="detail-value">{{ record.visitorDevice }}</text>
                </view>
                <view class="detail-item" v-if="record.visitorIp">
                  <text class="detail-label">IP：</text>
                  <text class="detail-value">{{ record.visitorIp }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="filteredRecords.length === 0">
            <uni-icons type="info" size="40" color="#cccccc"></uni-icons>
            <text class="empty-text">暂无访问记录</text>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore" @click="loadMore">
          <text>加载更多</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/api/index.js'

export default {
  data() {
    return {
      visitorId: null,
      cardId: null,
      visitorInfo: {},
      visitStats: {
        totalVisits: 0,
        totalDuration: '0分钟',
        avgDuration: '0分钟',
        lastVisitDays: '0天前'
      },
      visitRecords: [],
      activeFilter: 'all',
      isContact: false,
      hasMore: true,
      page: 1,
      pageSize: 20
    }
  },

  computed: {
    // 过滤后的访问记录
    filteredRecords() {
      const now = new Date()
      const records = this.visitRecords

      switch (this.activeFilter) {
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          return records.filter(record => new Date(record.createTime) >= weekAgo)
        case 'month':
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          return records.filter(record => new Date(record.createTime) >= monthAgo)
        default:
          return records
      }
    }
  },

  onLoad(options) {
    this.visitorId = options.visitorId
    this.cardId = options.cardId

    this.loadVisitorInfo()
    this.loadVisitStats()
    this.loadVisitRecords()
    this.checkIsContact()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 加载访客信息
    async loadVisitorInfo() {
      try {
        const res = await api.get(`/visitor/info/${this.visitorId}`)
        if (res.success) {
          this.visitorInfo = res.data
        }
      } catch (error) {
        console.error('加载访客信息失败:', error)
      }
    },

    // 加载访问统计
    async loadVisitStats() {
      try {
        const res = await api.get(`/visitor/stats`, {
          visitorId: this.visitorId,
          cardId: this.cardId
        })
        if (res.success) {
          this.visitStats = res.data
        }
      } catch (error) {
        console.error('加载访问统计失败:', error)
      }
    },

    // 加载访问记录
    async loadVisitRecords(loadMore = false) {
      try {
        if (!loadMore) {
          this.page = 1
          this.visitRecords = []
        }

        const res = await api.get('/visitor/records', {
          visitorId: this.visitorId,
          cardId: this.cardId,
          page: this.page,
          pageSize: this.pageSize
        })

        if (res.success) {
          const newRecords = res.data.records || []
          this.visitRecords = loadMore ? [...this.visitRecords, ...newRecords] : newRecords
          this.hasMore = newRecords.length === this.pageSize
        }
      } catch (error) {
        console.error('加载访问记录失败:', error)
      }
    },

    // 检查是否已是联系人
    async checkIsContact() {
      try {
        const res = await api.get('/contact/check', {
          visitorId: this.visitorId
        })
        if (res.success) {
          this.isContact = res.data.isContact
        }
      } catch (error) {
        console.error('检查联系人状态失败:', error)
      }
    },

    // 设置过滤条件
    setFilter(filter) {
      this.activeFilter = filter
    },

    // 加载更多
    loadMore() {
      this.page++
      this.loadVisitRecords(true)
    },

    // 添加到联系人
    async addToContacts() {
      try {
        uni.showLoading({ title: '添加中...' })

        const res = await api.post('/contact/add', {
          visitorId: this.visitorId,
          cardId: this.cardId
        })

        if (res.success) {
          this.isContact = true
          uni.showToast({
            title: '添加成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('添加联系人失败:', error)
        uni.showToast({
          title: '添加失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    // 发送消息
    sendMessage() {
      uni.navigateTo({
        url: `/pages/chat/chat?userId=${this.visitorId}`
      })
    },

    // 获取访问来源图标
    getSourceIcon(source) {
      const icons = {
        1: 'home',
        2: 'redo',
        3: 'scan'
      }
      return icons[source] || 'help'
    },

    // 获取访问来源颜色
    getSourceColor(source) {
      const colors = {
        1: '#52c41a',
        2: '#1890ff',
        3: '#ff6b35'
      }
      return colors[source] || '#999999'
    },

    // 获取访问来源文本
    getSourceText(source) {
      const texts = {
        1: '小程序访问',
        2: '分享链接',
        3: '扫码访问'
      }
      return texts[source] || '未知来源'
    },

    // 格式化时间
    formatTime(dateTime) {
      const date = new Date(dateTime)
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    // 格式化日期
    formatDate(dateTime) {
      const date = new Date(dateTime)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

      if (date >= today) {
        return '今天'
      } else if (date >= yesterday) {
        return '昨天'
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日`
      }
    },

    // 格式化时长
    formatDuration(seconds) {
      if (seconds < 60) {
        return `${seconds}秒`
      } else if (seconds < 3600) {
        return `${Math.floor(seconds / 60)}分钟`
      } else {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        return `${hours}小时${minutes}分钟`
      }
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.back-icon {
  padding: 10rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-left: 20rpx;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

/* 访客信息卡片 */
.visitor-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.visitor-avatar {
  position: relative;
  margin-bottom: 20rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}

.online-status {
  position: absolute;
  bottom: 8rpx;
  right: 8rpx;
  width: 24rpx;
  height: 24rpx;
  border-radius: 12rpx;
  background-color: #cccccc;
  border: 4rpx solid #ffffff;
}

.online-status.online {
  background-color: #52c41a;
}

.visitor-info {
  text-align: center;
  margin-bottom: 30rpx;
}

.visitor-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.visitor-position {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 4rpx;
}

.visitor-company {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.visitor-location {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999999;
}

.visitor-location text {
  margin-left: 8rpx;
}

.visitor-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: 1rpx solid #ff6b35;
  background-color: transparent;
  color: #ff6b35;
}

.action-btn.primary {
  background-color: #ff6b35;
  color: #ffffff;
}

.action-btn text {
  margin-left: 8rpx;
}

/* 统计区域 */
.stats-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #ff6b35;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #666666;
}

/* 记录区域 */
.records-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.filter-tabs {
  display: flex;
  gap: 20rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666666;
  background-color: #f5f5f5;
  transition: all 0.3s;
}

.filter-tab.active {
  background-color: #ff6b35;
  color: #ffffff;
}

.records-list {
  margin-top: 30rpx;
}

.record-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-time {
  width: 120rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}

.time-main {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 4rpx;
}

.time-sub {
  font-size: 22rpx;
  color: #999999;
}

.record-content {
  flex: 1;
}

.record-source {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}

.record-source text {
  margin-left: 8rpx;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  display: flex;
  font-size: 24rpx;
}

.detail-label {
  color: #999999;
  width: 120rpx;
}

.detail-value {
  color: #666666;
  flex: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #999999;
  margin-top: 20rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666666;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}
</style>
