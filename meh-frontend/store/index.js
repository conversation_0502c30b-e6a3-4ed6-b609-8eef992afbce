import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const store = new Vuex.Store({
  state: {
    // 用户信息
    userInfo: uni.getStorageSync('userInfo') || null,
    // 登录状态
    hasLogin: <PERSON><PERSON><PERSON>(uni.getStorageSync('token')),
    // 登录令牌
    token: uni.getStorageSync('token') || '',
    // 未读消息数
    unreadCount: 0,
    // 系统信息
    systemInfo: null
  },
  mutations: {
    // 登录成功
    login(state, data) {
      state.userInfo = data.user
      state.token = data.token
      state.hasLogin = true
      
      // 存储到本地
      uni.setStorageSync('userInfo', data.user)
      uni.setStorageSync('token', data.token)
    },
    // 退出登录
    logout(state) {
      state.userInfo = null
      state.token = ''
      state.hasLogin = false
      
      // 清除本地存储
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    },
    // 更新用户信息
    updateUserInfo(state, userInfo) {
      state.userInfo = userInfo
      uni.setStorageSync('userInfo', userInfo)
    },
    // 更新未读消息数
    updateUnreadCount(state, count) {
      state.unreadCount = count
    },
    // 设置系统信息
    setSystemInfo(state, info) {
      state.systemInfo = info
    }
  },
  actions: {
    // 获取系统信息
    getSystemInfo({ commit }) {
      return new Promise((resolve) => {
        uni.getSystemInfo({
          success: (res) => {
            commit('setSystemInfo', res)
            resolve(res)
          }
        })
      })
    }
  }
})

export default store
