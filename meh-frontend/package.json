{"name": "meh-business-card", "version": "1.0.0", "description": "MEH电子名片微信小程序", "main": "main.js", "dependencies": {"@dcloudio/uni-app": "^2.0.0", "@dcloudio/uni-app-plus": "^2.0.0", "@dcloudio/uni-h5": "^2.0.0", "@dcloudio/uni-mp-alipay": "^2.0.0", "@dcloudio/uni-mp-baidu": "^2.0.0", "@dcloudio/uni-mp-qq": "^2.0.0", "@dcloudio/uni-mp-toutiao": "^2.0.0", "@dcloudio/uni-mp-weixin": "^2.0.0", "@dcloudio/uni-stat": "^2.0.0", "uview-ui": "^2.0.31"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.0", "@dcloudio/uni-migration": "^2.0.0", "@dcloudio/uni-template-compiler": "^2.0.0", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0", "@dcloudio/vue-cli-plugin-uni": "^2.0.0", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0", "@dcloudio/webpack-uni-mp-loader": "^2.0.0", "@dcloudio/webpack-uni-pages-loader": "^2.0.0", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-service": "^4.5.0", "babel-plugin-import": "^1.11.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "vue-template-compiler": "^2.6.11"}, "scripts": {"serve": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}