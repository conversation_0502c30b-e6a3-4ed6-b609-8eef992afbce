/**
 * API请求封装
 */
import config from '../config'

// 请求拦截器
const requestInterceptor = (options) => {
  // 获取token
  const token = uni.getStorageSync('token')
  
  // 添加token到请求头
  if (token) {
    options.header = {
      ...options.header,
      'Authorization': 'Bearer ' + token
    }
  }
  
  // 添加内容类型
  if (options.method === 'POST' || options.method === 'PUT') {
    options.header = {
      ...options.header,
      'Content-Type': 'application/json'
    }
  }
  
  return options
}

// 响应拦截器
const responseInterceptor = (response, resolve, reject) => {
  // 请求成功
  if (response.statusCode === 200) {
    // 业务成功
    if (response.data.code === 200) {
      resolve(response.data.data)
    } else {
      // 业务失败
      if (response.data.code === 401) {
        // 未登录或token过期
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
        
        // 跳转到登录页
        uni.navigateTo({
          url: '/pages/login/login'
        })
      }
      
      // 显示错误信息
      uni.showToast({
        title: response.data.message || '请求失败',
        icon: 'none'
      })
      
      reject(response.data)
    }
  } else {
    // 请求失败
    uni.showToast({
      title: '网络错误，请稍后重试',
      icon: 'none'
    })
    
    reject(response)
  }
}

// 请求方法
const request = (options) => {
  // 请求拦截
  options = requestInterceptor(options)
  
  // 完整URL
  const url = options.url.startsWith('http') ? options.url : config.baseUrl + options.url
  
  return new Promise((resolve, reject) => {
    uni.request({
      url,
      data: options.data,
      method: options.method || 'GET',
      header: options.header || {},
      success: (res) => {
        responseInterceptor(res, resolve, reject)
      },
      fail: (err) => {
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// 封装常用请求方法
const api = {
  // GET请求
  get(url, data = {}, options = {}) {
    return request({
      url,
      data,
      method: 'GET',
      ...options
    })
  },
  
  // POST请求
  post(url, data = {}, options = {}) {
    return request({
      url,
      data,
      method: 'POST',
      ...options
    })
  },
  
  // PUT请求
  put(url, data = {}, options = {}) {
    return request({
      url,
      data,
      method: 'PUT',
      ...options
    })
  },
  
  // DELETE请求
  delete(url, data = {}, options = {}) {
    return request({
      url,
      data,
      method: 'DELETE',
      ...options
    })
  },
  
  // 上传文件
  upload(url, filePath, name = 'file', formData = {}) {
    const token = uni.getStorageSync('token')
    
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: config.baseUrl + url,
        filePath,
        name,
        formData,
        header: token ? { 'Authorization': 'Bearer ' + token } : {},
        success: (res) => {
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data)
            if (data.code === 200) {
              resolve(data.data)
            } else {
              uni.showToast({
                title: data.message || '上传失败',
                icon: 'none'
              })
              reject(data)
            }
          } else {
            uni.showToast({
              title: '网络错误，请稍后重试',
              icon: 'none'
            })
            reject(res)
          }
        },
        fail: (err) => {
          uni.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          })
          reject(err)
        }
      })
    })
  }
}

export default api
