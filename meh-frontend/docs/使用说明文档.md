# 电子名片微信小程序项目使用说明文档

## 1. 项目概述

MEH电子名片是一款基于微信小程序的电子名片管理应用，旨在为用户提供便捷的电子名片创建、管理、分享和社交功能。本项目采用前后端分离架构，前端使用UniApp开发，后端使用Spring Boot开发，支持Docker容器化部署。

## 2. 系统架构

### 2.1 技术栈

- **前端**：UniApp + Vue.js
- **后端**：Spring Boot + MyBatis-Plus
- **数据库**：MySQL
- **缓存**：Redis
- **部署**：Docker + Docker Compose

### 2.2 系统模块

- 用户管理
- 名片管理
- 通讯录管理
- 社交互动
- 积分系统
- 营销推广
- 系统管理

## 3. 安装与部署

### 3.1 环境要求

- Docker 19.03+
- Docker Compose 1.25+
- 微信开发者工具（前端开发与调试）

### 3.2 后端部署

1. 克隆项目代码
```bash
git clone https://github.com/your-repo/meh-business-card.git
cd meh-business-card
```

2. 使用Docker Compose部署
```bash
cd backend
docker-compose up -d
```

3. 验证部署
```bash
curl http://localhost:8080/actuator/health
```

### 3.3 前端部署

1. 安装依赖
```bash
cd frontend
npm install
```

2. 开发模式
```bash
npm run dev:mp-weixin
```

3. 生产构建
```bash
npm run build:mp-weixin
```

4. 在微信开发者工具中导入项目（dist/dev/mp-weixin目录）

## 4. 功能使用说明

### 4.1 用户注册与登录

1. 打开小程序，进入登录页面
2. 可选择手机号密码登录或微信一键登录
3. 首次使用需注册账号，填写基本信息

### 4.2 名片管理

#### 4.2.1 创建名片

1. 在首页点击"我的名片"，进入名片列表页
2. 点击"新建名片"按钮
3. 填写名片信息，包括基本信息、联系方式、个人介绍、资源与项目等
4. 选择名片模板
5. 点击"保存"按钮完成创建

#### 4.2.2 编辑名片

1. 在名片列表页，点击名片项的"编辑"按钮
2. 修改名片信息
3. 点击"保存"按钮完成编辑

#### 4.2.3 分享名片

1. 在名片详情页，点击"分享"按钮
2. 选择分享方式（微信好友、朋友圈、生成二维码等）
3. 按照提示完成分享操作

### 4.3 通讯录管理

#### 4.3.1 添加联系人

1. 扫描他人名片二维码
2. 在他人名片详情页点击"保存"按钮
3. 确认保存到通讯录

#### 4.3.2 管理联系人

1. 在底部导航栏点击"通讯录"，进入通讯录列表页
2. 可查看、编辑、删除联系人
3. 可创建分组，对联系人进行分类管理

### 4.4 积分系统

#### 4.4.1 积分获取

用户可通过以下方式获取积分：
- 每日登录：5积分/次
- 连续登录奖励：连续3天+2分，7天+5分，15天+10分，30天+15分
- 完善个人信息：10积分
- 社交互动：点赞1分/次，评论2分/次，分享3分/次
- 被点赞：2分/次，被评论：3分/次
- 添加联系人：2分/人
- 邀请好友：50分/人

#### 4.4.2 积分兑换

1. 在底部导航栏点击"积分商城"，进入积分商城页面
2. 浏览可兑换的商品
3. 选择商品，点击"立即兑换"
4. 确认兑换信息，完成兑换

### 4.5 社交互动

#### 4.5.1 点赞与评论

1. 在他人名片详情页，点击"点赞"或"留言"按钮
2. 输入评论内容，点击发送

#### 4.5.2 在线咨询

1. 在他人名片详情页，点击"咨询"按钮
2. 进入聊天页面，可发送文字、图片等消息

### 4.6 营销推广

#### 4.6.1 裂变营销

1. 在个人中心页面，点击"名片裂变营销"
2. 查看活动规则和奖励机制
3. 点击"邀请好友"，分享小程序给好友
4. 好友注册后，双方均可获得积分奖励

## 5. 管理员功能

### 5.1 用户管理

1. 登录管理后台
2. 在左侧菜单选择"用户管理"
3. 可查看用户列表、详情，进行禁用/启用操作

### 5.2 积分管理

1. 在用户详情页，点击"积分管理"
2. 可手动增加/减少用户积分
3. 填写操作原因，点击确认

### 5.3 系统配置

1. 在左侧菜单选择"系统配置"
2. 可配置积分规则、活动参数等系统参数

## 6. 常见问题

### 6.1 登录问题

**Q: 微信登录失败怎么办？**
A: 请确保微信账号已绑定手机号，或尝试使用手机号密码登录方式。

**Q: 忘记密码怎么处理？**
A: 在登录页点击"忘记密码"，通过手机验证码重置密码。

### 6.2 名片问题

**Q: 为什么我的名片二维码无法扫描？**
A: 请确保网络环境良好，二维码清晰完整。如持续问题，可尝试重新生成二维码。

**Q: 如何修改名片模板？**
A: 编辑名片时，滑动到底部"名片模板"区域，选择新的模板即可。

### 6.3 积分问题

**Q: 积分有效期是多久？**
A: 积分自获取之日起有效期为一年。

**Q: 为什么我完成任务没有获得积分？**
A: 部分积分任务有每日上限，请查看积分规则或次日再尝试。

## 7. 联系与支持

如有任何问题或建议，请通过以下方式联系我们：

- 客服电话：400-123-4567
- 客服邮箱：<EMAIL>
- 在线咨询：小程序内"个人中心"-"在线客服"

## 8. 版本历史

- v1.0.0 (2025-06-03)：首次发布，包含基础名片管理、通讯录、积分系统等核心功能
