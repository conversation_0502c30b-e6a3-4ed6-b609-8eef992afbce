package com.meh.businesscard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 用户注册参数
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@ApiModel(value = "用户注册参数")
public class UserRegisterDTO {

    @ApiModelProperty(value = "用户名", required = true, example = "zhangsan123")
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,20}$", message = "用户名必须为4-20位字母、数字或下划线")
    private String username;

    @ApiModelProperty(value = "密码", required = true, example = "Password@123")
    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*#?&]{8,20}$",
            message = "密码必须为8-20位，且包含大小写字母和数字")
    private String password;

    @ApiModelProperty(value = "确认密码", required = true, example = "Password@123")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @ApiModelProperty(value = "手机号", required = true, example = "13800138000")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty(value = "验证码", example = "123456")
    @Length(min = 4, max = 6, message = "验证码长度必须为4-6位")
    private String verifyCode;

    @ApiModelProperty(value = "邀请码", example = "ABC123")
    private String inviteCode;

    @ApiModelProperty(value = "真实姓名", example = "张三")
    @Length(max = 50, message = "姓名长度不能超过50个字符")
    private String realName;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;
}
