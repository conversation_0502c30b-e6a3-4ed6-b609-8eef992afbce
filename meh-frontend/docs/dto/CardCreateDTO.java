package com.meh.businesscard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 名片创建参数
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@ApiModel(value = "名片创建参数")
public class CardCreateDTO {
    
    @ApiModelProperty(value = "名片类型：1-个人名片，2-企业名片", required = true)
    @NotNull(message = "名片类型不能为空")
    private Integer type;
    
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String name;
    
    @ApiModelProperty(value = "职位")
    private String position;
    
    @ApiModelProperty(value = "公司")
    private String company;
    
    @ApiModelProperty(value = "电话")
    private String phone;
    
    @ApiModelProperty(value = "邮箱")
    private String email;
    
    @ApiModelProperty(value = "地址")
    private String address;
    
    @ApiModelProperty(value = "个人简介")
    private String introduction;
    
    @ApiModelProperty(value = "专业技能，JSON格式存储")
    private String skills;
    
    @ApiModelProperty(value = "个人资源，JSON格式存储")
    private String resources;
    
    @ApiModelProperty(value = "项目经历，JSON格式存储")
    private String projects;
    
    @ApiModelProperty(value = "模板ID", required = true)
    @NotNull(message = "模板ID不能为空")
    private Long templateId;
}
