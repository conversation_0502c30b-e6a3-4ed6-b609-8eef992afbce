
<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <uni-icons type="arrowleft" size="24px" color="#333" @click="handleBack"></uni-icons>
      <text class="header-title">裂变活动</text>
    </view>

    <!-- 活动主图Banner -->
    <view class="banner-container">
      <image 
        class="banner-image"
        mode="widthFix"
        src="https://ai-public.mastergo.com/ai/img_res/dd9300fcec84b26c472a22bed613063e.jpg"
      ></image>
    </view>

    <!-- 活动简介 -->
    <view class="intro-card">
      <text class="intro-title">邀请好友 赢取好礼</text>
      <text class="intro-desc">邀请1位好友可得50积分\n邀请5位可兑换"MEH荣誉徽章"</text>
    </view>

    <!-- 当前进度 -->
    <view class="progress-card">
      <text class="progress-title">当前进度</text>
      <view class="progress-bar">
        <view 
          v-for="i in 5" 
          :key="i" 
          :class="['progress-bubble', i <= progress.current ? 'active' : '']"
        ></view>
      </view>
      <text class="progress-text">已邀请：{{progress.current}}/{{progress.total}}位好友</text>
    </view>

    <!-- 奖励明细 -->
    <view class="reward-card">
      <text class="reward-title">奖励明细</text>
      <view class="reward-list">
        <view class="reward-item" v-for="(item, index) in rewards" :key="index">
          <uni-icons :type="item.icon" size="24px" :color="item.color"></uni-icons>
          <text class="reward-text">{{item.text}}</text>
        </view>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view class="share-buttons">
      <view class="share-button" @click="handleShare('wechat')">
        <uni-icons type="weixin" size="24px" color="#07C160"></uni-icons>
        <text>分享名片</text>
      </view>
      <view class="share-button" @click="handleShare('friend')">
        <uni-icons type="personadd" size="24px" color="#FF9500"></uni-icons>
        <text>邀请好友</text>
      </view>
      <view class="share-button" @click="handleShare('qrcode')">
        <uni-icons type="scan" size="24px" color="#10AEFF"></uni-icons>
        <text>生成二维码</text>
      </view>
    </view>

    <!-- 活动规则 -->
    <view class="rule-card">
      <view class="rule-header" @click="toggleRule">
        <text>活动规则</text>
        <uni-icons :type="showRule ? 'arrowup' : 'arrowdown'" size="16px" color="#999"></uni-icons>
      </view>
      <view class="rule-content" v-if="showRule">
        <text>活动时间：6月1日 - 6月30日</text>
        <text>说明：请勿使用刷量行为，系统将自动识别作弊账号并清除奖励</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const progress = ref({
  current: 3,
  total: 5
});

const rewards = ref([
  { icon: 'star-filled', color: '#FFCC00', text: '奖励1：50积分' },
  { icon: 'medal-filled', color: '#FF9500', text: '奖励2：专属徽章' },
  { icon: 'flag-filled', color: '#10AEFF', text: '奖励3：优先参加下次活动资格' }
]);

const showRule = ref(false);

const handleBack = () => {
  uni.navigateBack();
};

const handleShare = (type: string) => {
  if (type === 'qrcode') {
    uni.showToast({ title: '生成二维码成功', icon: 'success' });
  } else {
    uni.showToast({ title: `已分享到${type === 'wechat' ? '微信' : '好友'}`, icon: 'success' });
  }
};

const toggleRule = () => {
  showRule.value = !showRule.value;
};
</script>

<style>
page {
  height: 100%;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(to bottom, #F5F9FF, #FFFFFF);
  padding: 0 24rpx;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 24rpx;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  margin-left: 24rpx;
}

.banner-container {
  margin: 24rpx 0;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: auto;
}

.intro-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.intro-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.intro-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  white-space: pre-line;
}

.progress-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.progress-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.progress-bubble {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #E5E5E5;
}

.progress-bubble.active {
  background-color: #10AEFF;
}

.progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
  display: block;
}

.reward-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.reward-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.reward-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.reward-item {
  display: flex;
  align-items: center;
}

.reward-text {
  font-size: 14px;
  color: #666;
  margin-left: 16rpx;
}

.share-buttons {
  display: flex;
  justify-content: space-between;
  margin: 24rpx 0;
}

.share-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 30%;
  height: 120rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.share-button text {
  font-size: 12px;
  color: #666;
  margin-top: 8rpx;
}

.rule-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-header text {
  font-size: 16px;
  color: #333;
}

.rule-content {
  margin-top: 24rpx;
}

.rule-content text {
  font-size: 12px;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}
</style>

