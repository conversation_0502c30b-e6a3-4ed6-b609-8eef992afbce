
<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333"></uni-icons>
      <text class="nav-title">兑换详情</text>
    </view>

    <!-- 滚动区域 -->
    <scroll-view class="scroll-container" scroll-y>
      <!-- 商品轮播图 -->
      <swiper class="swiper" :indicator-dots="true" :autoplay="true" interval="3000" duration="500">
        <swiper-item v-for="(img, index) in productImages" :key="index">
          <image :src="img" mode="aspectFill" class="swiper-img" />
        </swiper-item>
      </swiper>

      <!-- 商品信息区 -->
      <view class="product-info">
        <text class="product-title">限量版纪念徽章 - MEH会员专属</text>
        <view class="points-row">
          <text class="points-label">积分</text>
          <text class="points-value">180</text>
        </view>
        <view class="stock-info">
          <text class="stock-text">剩余库存：12件</text>
          <text class="limit-text">每人限兑：1次</text>
        </view>
      </view>

      <!-- 商品详情区 -->
      <view class="product-detail">
        <view class="detail-section">
          <text class="section-title">商品介绍</text>
          <view class="detail-content">
            <text class="detail-text">• 此徽章为定制限量款</text>
            <text class="detail-text">• 材质为合金 + 钛层</text>
            <text class="detail-text">• 可用于 MEH 会员佩戴场景等</text>
          </view>
        </view>

        <view class="detail-section">
          <text class="section-title">兑换须知</text>
          <view class="detail-content">
            <text class="detail-text">• 积分兑换后不可退换</text>
            <text class="detail-text">• 实物商品将在7日内邮寄</text>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="current-points">
        <text class="points-text">当前积分：</text>
        <text class="points-num">320</text>
      </view>
      <button class="exchange-btn" type="primary">立即兑换</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const productImages = ref([
  'https://ai-public.mastergo.com/ai/img_res/3743d439e77f1918946bfef95071e961.jpg',
  'https://ai-public.mastergo.com/ai/img_res/25e7e69620a2c75e6ab5fd48703ec7bf.jpg',
  'https://ai-public.mastergo.com/ai/img_res/18f305f03146aa3f0ea53fba6df59c65.jpg'
]);
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.nav-bar {
  height: 88rpx;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  padding: 0 30rpx;
  flex-shrink: 0;
}

.back-icon {
  width: 20px;
  height: 20px;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  margin-right: 20px;
}

.scroll-container {
  flex: 1;
  overflow: auto;
}

.swiper {
  width: 750rpx;
  height: 500rpx;
}

.swiper-img {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.points-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}

.points-label {
  font-size: 14px;
  color: #666666;
  margin-right: 10rpx;
}

.points-value {
  font-size: 20px;
  color: #ff6b00;
  font-weight: bold;
}

.stock-info {
  display: flex;
  gap: 30rpx;
}

.stock-text,
.limit-text {
  font-size: 12px;
  color: #999999;
}

.product-detail {
  padding: 30rpx;
  background-color: #ffffff;
}

.detail-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-text {
  font-size: 14px;
  color: #666666;
  line-height: 1.5;
}

.bottom-bar {
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #eeeeee;
  flex-shrink: 0;
}

.current-points {
  display: flex;
  align-items: baseline;
}

.points-text {
  font-size: 14px;
  color: #666666;
}

.points-num {
  font-size: 20px;
  color: #ff6b00;
  font-weight: bold;
}

.exchange-btn {
  width: 240rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 14px;
  margin: 0;
}
</style>

