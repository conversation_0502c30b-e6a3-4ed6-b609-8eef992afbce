
<template>
  <view class="page">
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#333333"/>
      <text class="title">积分商城</text>
      <uni-icons class="search-icon" type="search" size="24" color="#333333"/>
    </view>

    <scroll-view class="content" scroll-y>
      <view class="points-section">
        <view class="points-wrapper">
          <text class="points-label">当前积分</text>
          <text class="points-value">320</text>
          <view class="sign-btn">
            <uni-icons class="sign-icon" type="gift" size="16" color="#FFFFFF"/>
            <text class="sign-text">签到赚积分</text>
          </view>
        </view>
      </view>

      <scroll-view class="category-section" scroll-x>
        <view class="category-wrapper">
          <view 
            v-for="(item, index) in categories" 
            :key="index"
            class="category-item"
            :class="{'active': currentCategory === index}"
            @tap="selectCategory(index)"
          >
            <text>{{ item }}</text>
          </view>
        </view>
      </scroll-view>

      <view class="goods-section">
        <view class="goods-list">
          <view class="goods-item" v-for="(item, index) in goodsList" :key="index">
            <image class="goods-image" :src="item.image" mode="aspectFill"/>
            <view class="goods-info">
              <text class="goods-title">{{ item.title }}</text>
              <view class="points-info">
                <text class="points-num">{{ item.points }}积分</text>
                <text class="exchange-count">{{ item.exchangeCount }}人已兑换</text>
              </view>
              <button class="exchange-btn" :class="{'disabled': item.points > 320}">
                立即兑换
              </button>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const currentCategory = ref(0);
const categories = ['全部', '实物', '虚拟', '热门'];

const goodsList = [
  {
    title: '定制徽章',
    points: 120,
    exchangeCount: 328,
    image: 'https://ai-public.mastergo.com/ai/img_res/a3daca754a81096e2e0d0ccecf19ce40.jpg'
  },
  {
    title: '专属头像框',
    points: 80,
    exchangeCount: 562,
    image: 'https://ai-public.mastergo.com/ai/img_res/1a9524a9d5c4933542aabe5cc4bab29c.jpg'
  },
  {
    title: '限量手机壳',
    points: 450,
    exchangeCount: 89,
    image: 'https://ai-public.mastergo.com/ai/img_res/de779a8a8fabb9adbdbedaf3a879bfa3.jpg'
  },
  {
    title: '精美钥匙扣',
    points: 160,
    exchangeCount: 235,
    image: 'https://ai-public.mastergo.com/ai/img_res/86ba72838f713ae3af3f2a5fe3d20377.jpg'
  }
];

const selectCategory = (index: number) => {
  currentCategory.value = index;
};
</script>

<style>
page {
  height: 100%;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F5F6F7;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #FFFFFF;
  padding: 0 30rpx;
  position: relative;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.search-icon {
  width: 48rpx;
  height: 48rpx;
  margin-left: auto;
}

.content {
  flex: 1;
  overflow: auto;
}

.points-section {
  background-color: #FFFFFF;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}

.points-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.points-label {
  font-size: 14px;
  color: #666666;
  margin-bottom: 20rpx;
}

.points-value {
  font-size: 40px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
}

.sign-btn {
  display: flex;
  align-items: center;
  background-color: #2B7EF4;
  padding: 16rpx 30rpx;
  border-radius: 30rpx;
}

.sign-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.sign-text {
  font-size: 14px;
  color: #FFFFFF;
}

.category-section {
  background-color: #FFFFFF;
  padding: 20rpx 0;
  white-space: nowrap;
}

.category-wrapper {
  display: inline-flex;
  padding: 0 30rpx;
}

.category-item {
  padding: 12rpx 30rpx;
  margin-right: 20rpx;
  border-radius: 30rpx;
  background-color: #F5F6F7;
}

.category-item text {
  font-size: 14px;
  color: #666666;
}

.category-item.active {
  background-color: #2B7EF4;
}

.category-item.active text {
  color: #FFFFFF;
}

.goods-section {
  padding: 20rpx 30rpx;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.goods-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  overflow: hidden;
}

.goods-image {
  width: 100%;
  height: 320rpx;
}

.goods-info {
  padding: 20rpx;
}

.goods-title {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.points-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.points-num {
  font-size: 16px;
  color: #FF6B35;
  font-weight: 500;
}

.exchange-count {
  font-size: 12px;
  color: #999999;
}

.exchange-btn {
  width: 100%;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background-color: #2B7EF4;
  color: #FFFFFF;
  font-size: 14px;
  border-radius: 32rpx;
  padding: 0;
}

.exchange-btn.disabled {
  background-color: #CCCCCC;
}
</style>

