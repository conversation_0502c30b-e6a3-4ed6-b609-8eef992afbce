
<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#fff" @click="goBack"/>
      <text class="nav-title">分享名片</text>
    </view>

    <!-- 二维码展示区 -->
    <view class="qr-container">
      <view class="qr-card">
        <image class="qr-image" :src="qrCodeUrl" mode="aspectFit"/>
      </view>
      <text class="qr-tip">扫一扫，快速添加名片</text>
    </view>

    <!-- 操作按钮区 -->
    <view class="action-container">
      <uni-button class="save-btn" type="primary" @click="saveQrCode">
        <uni-icons class="btn-icon" type="download" size="20" color="#fff"/>
        <text>保存二维码到相册</text>
      </uni-button>

      <view class="share-group">
        <uni-button class="share-btn" @click="shareToWechat">
          <uni-icons class="btn-icon" type="weixin" size="20" color="#07C160"/>
          <text>微信好友</text>
        </uni-button>
        <uni-button class="share-btn" @click="shareToTimeline">
          <uni-icons class="btn-icon" type="pyq" size="20" color="#07C160"/>
          <text>朋友圈</text>
        </uni-button>
      </view>

      <uni-button class="link-btn" @click="generateLink">
        <uni-icons class="btn-icon" type="link" size="20" color="#666"/>
        <text>生成名片外链</text>
      </uni-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

// 二维码图片URL
const qrCodeUrl = ref('https://ai-public.mastergo.com/ai/img_res/cd80956f861f8756fd726d3bbbc690b2.jpg');

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 保存二维码
const saveQrCode = () => {
  uni.showLoading({ title: '保存中...' });
  uni.saveImageToPhotosAlbum({
    filePath: qrCodeUrl.value,
    success: () => {
      uni.showToast({ title: '保存成功', icon: 'success' });
    },
    fail: () => {
      uni.showToast({ title: '保存失败', icon: 'error' });
    },
    complete: () => {
      uni.hideLoading();
    }
  });
};

// 分享到微信
const shareToWechat = () => {
  uni.showToast({ title: '分享到微信', icon: 'none' });
};

// 分享到朋友圈
const shareToTimeline = () => {
  uni.showToast({ title: '分享到朋友圈', icon: 'none' });
};

// 生成外链
const generateLink = () => {
  uni.setClipboardData({
    data: 'https://example.com/card/share',
    success: () => {
      uni.showToast({ title: '链接已复制', icon: 'success' });
    }
  });
};
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F8F8F8;
}

.nav-bar {
  position: relative;
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #2B85E4;
  padding: 0 30rpx;
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}

.qr-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.qr-card {
  background: #fff;
  padding: 40rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.qr-image {
  width: 480rpx;
  height: 480rpx;
}

.qr-tip {
  margin-top: 30rpx;
  color: #666;
  font-size: 14px;
}

.action-container {
  padding: 40rpx;
}

.save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2B85E4 !important;
  border-color: #2B85E4 !important;
}

.share-group {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
  gap: 30rpx;
}

.share-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff !important;
  color: #333 !important;
}

.link-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff !important;
  color: #666 !important;
}

.btn-icon {
  margin-right: 8rpx;
  width: 40rpx;
  height: 40rpx;
}
</style>

