
<template>
  <view class="page">
    <view class="nav-bar">
      <uni-icons type="left" size="24" class="back-icon" @click="goBack"/>
      <text class="title">名片详情</text>
      <uni-icons type="share" size="24" class="share-icon" @click="share"/>
    </view>

    <scroll-view class="content" scroll-y>
      <view class="main-info">
        <image class="avatar" :src="avatarUrl" mode="aspectFill"/>
        <text class="name">张三</text>
        <text class="position">产品经理</text>
        <text class="company">科技创新有限公司</text>
      </view>

      <view class="contact-info">
        <view class="contact-item" @click="makePhoneCall">
          <uni-icons type="phone" size="24" color="#2B85E4"/>
          <text class="contact-text">13800138000</text>
        </view>
        <view class="contact-item" @click="sendEmail">
          <uni-icons type="email" size="24" color="#2B85E4"/>
          <text class="contact-text"><EMAIL></text>
        </view>
        <view class="contact-item" @click="openMap">
          <uni-icons type="location" size="24" color="#2B85E4"/>
          <text class="contact-text">北京市朝阳区科技园区88号</text>
        </view>
      </view>

      <view class="qrcode-section">
        <image class="qrcode" :src="qrcodeUrl" mode="aspectFit"/>
        <text class="qrcode-tip">扫描二维码保存名片</text>
      </view>

      <view class="interaction-section">
        <view class="interaction-btn" @click="handleLike">
          <uni-icons :type="isLiked ? 'heart-filled' : 'heart'" size="24" :color="isLiked ? '#FF6B6B' : '#666'"/>
          <text class="btn-text">点赞</text>
        </view>
        <view class="interaction-btn" @click="handleComment">
          <uni-icons type="chat" size="24" color="#666"/>
          <text class="btn-text">留言</text>
        </view>
        <view class="interaction-btn" @click="handleConsult">
          <uni-icons type="headphones" size="24" color="#666"/>
          <text class="btn-text">在线咨询</text>
        </view>
      </view>

      <view class="visitor-info" @click="checkVisitors">
        <text class="visitor-text">共有 128 人查看了此名片</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const isLiked = ref(false);

const avatarUrl = 'https://ai-public.mastergo.com/ai/img_res/186f9ebcb14c3ef8a0fdd002f42de4a8.jpg';

const qrcodeUrl = 'https://ai-public.mastergo.com/ai/img_res/aeea505e32c8420e062495541fd643f2.jpg';

const goBack = () => {
  uni.navigateBack();
};

const share = () => {
  uni.showShareMenu({
    withShareTicket: true
  });
};

const makePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: '13800138000'
  });
};

const sendEmail = () => {
  // 在真实环境中需要调用相应的邮件发送功能
  uni.showToast({
    title: '正在打开邮件应用',
    icon: 'none'
  });
};

const openMap = () => {
  uni.showToast({
    title: '正在打开地图应用',
    icon: 'none'
  });
};

const handleLike = () => {
  isLiked.value = !isLiked.value;
  uni.showToast({
    title: isLiked.value ? '已点赞' : '已取消点赞',
    icon: 'none'
  });
};

const handleComment = () => {
  uni.showToast({
    title: '正在打开留言',
    icon: 'none'
  });
};

const handleConsult = () => {
  uni.showToast({
    title: '正在连接客服',
    icon: 'none'
  });
};

const checkVisitors = () => {
  uni.showToast({
    title: '查看访客记录',
    icon: 'none'
  });
};
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  background-color: #F8F8F8;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  height: 88rpx;
  background-color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  flex-shrink: 0;
}

.title {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
}

.back-icon, .share-icon {
  width: 24px;
  height: 24px;
}

.content {
  flex: 1;
  overflow: auto;
}

.main-info {
  margin: 32rpx;
  padding: 48rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  margin-bottom: 24rpx;
}

.name {
  font-size: 24px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}

.position {
  font-size: 16px;
  color: #666666;
  margin-bottom: 8rpx;
}

.company {
  font-size: 16px;
  color: #666666;
}

.contact-info {
  margin: 32rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
}

.contact-item:not(:last-child) {
  border-bottom: 1px solid #EEEEEE;
}

.contact-text {
  margin-left: 24rpx;
  font-size: 14px;
  color: #333333;
}

.qrcode-section {
  margin: 32rpx;
  padding: 48rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 16rpx;
}

.qrcode-tip {
  font-size: 14px;
  color: #999999;
}

.interaction-section {
  margin: 32rpx;
  padding: 32rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-around;
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-text {
  margin-top: 8rpx;
  font-size: 14px;
  color: #666666;
}

.visitor-info {
  margin: 32rpx;
  display: flex;
  justify-content: center;
}

.visitor-text {
  font-size: 12px;
  color: #999999;
}
</style>

