
<template>
  <view class="chat-page">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#333333"></uni-icons>
      <text class="title">张伟 - 云科技有限公司</text>
    </view>

    <!-- 聊天内容区 -->
    <scroll-view class="chat-content" scroll-y scroll-into-view="msg-bottom">
      <!-- 系统消息 -->
      <view class="system-message">
        <text>2023年12月20日 14:30</text>
      </view>

      <!-- 欢迎消息 -->
      <view class="message receive">
        <image class="avatar" src="https://ai-public.mastergo.com/ai/img_res/d40a6da2e848499c7c5f1a48a2958355.jpg" />
        <view class="message-content">
          <view class="bubble">你好，请问有什么可以帮助您的？</view>
          <text class="time">14:30</text>
        </view>
      </view>

      <!-- 发送的消息 -->
      <view class="message send">
        <view class="message-content">
          <view class="bubble">您好，我想了解贵公司的产品服务</view>
          <text class="time">14:31</text>
        </view>
        <image class="avatar" src="https://ai-public.mastergo.com/ai/img_res/bbee57a9174b5db738aa28efb818b1be.jpg" />
      </view>

      <!-- 接收的消息 -->
      <view class="message receive">
        <image class="avatar" src="https://ai-public.mastergo.com/ai/img_res/700f57435656c68bfb3de46e77857bb6.jpg" />
        <view class="message-content">
          <view class="bubble">好的，我们公司主要提供企业数字化转型解决方案，包括云服务、大数据分析和人工智能应用等服务。您感兴趣的是哪个方面呢？</view>
          <text class="time">14:32</text>
        </view>
      </view>

      <!-- 底部占位 -->
      <view id="msg-bottom" style="height: 20rpx;"></view>
    </scroll-view>

    <!-- 底部输入区 -->
    <view class="input-area">
      <view class="toolbar">
        <uni-icons class="tool-icon" type="emotion" size="28" color="#666666"></uni-icons>
        <uni-icons class="tool-icon" type="sound" size="28" color="#666666"></uni-icons>
        <uni-icons class="tool-icon" type="image" size="28" color="#666666"></uni-icons>
      </view>
      <view class="input-box">
        <input type="text" v-model="inputMessage" placeholder="请输入消息..." />
        <uni-icons 
          class="send-btn" 
          :type="inputMessage ? 'paperplane-filled' : 'paperplane'" 
          size="28" 
          :color="inputMessage ? '#007AFF' : '#999999'"
        ></uni-icons>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const inputMessage = ref('');
</script>

<style>
page {
  height: 100%;
}

.chat-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F7F7F7;
}

.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
  flex-shrink: 0;
}

.back-icon {
  margin-right: 20rpx;
}

.title {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}

.chat-content {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow: auto;
}

.system-message {
  text-align: center;
  margin: 20rpx 0;
}

.system-message text {
  font-size: 12px;
  color: #999999;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
}

.message {
  display: flex;
  margin-bottom: 30rpx;
}

.message.send {
  flex-direction: row-reverse;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin: 0 20rpx;
  flex-shrink: 0;
}

.message-content {
  max-width: 480rpx;
}

.bubble {
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8rpx;
}

.receive .bubble {
  background-color: #FFFFFF;
  color: #333333;
}

.send .bubble {
  background-color: #007AFF;
  color: #FFFFFF;
}

.time {
  font-size: 12px;
  color: #999999;
}

.send .time {
  text-align: right;
}

.input-area {
  background-color: #F8F8F8;
  border-top: 1px solid #EEEEEE;
  padding: 20rpx;
  flex-shrink: 0;
}

.toolbar {
  display: flex;
  padding: 10rpx 0;
  margin-bottom: 10rpx;
}

.tool-icon {
  margin-right: 40rpx;
}

.input-box {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border-radius: 36rpx;
  padding: 0 20rpx;
}

.input-box input {
  flex: 1;
  height: 72rpx;
  font-size: 14px;
  color: #333333;
}

.send-btn {
  padding: 10rpx;
}
</style>

