
<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="left" size="20" color="#333333"></uni-icons>
      <text class="nav-title">通讯录</text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-box">
        <uni-icons class="search-icon" type="search" size="16" color="#999999"></uni-icons>
        <input class="search-input" type="text" placeholder="搜索联系人" v-model="searchText" />
      </view>
    </view>

    <!-- 联系人列表 -->
    <scroll-view class="contact-list" scroll-y>
      <template v-if="contacts.length > 0">
        <view v-for="(contact, index) in contacts" :key="index" class="contact-item">
          <view class="avatar">
            <image class="avatar-img" :src="contact.avatar" mode="aspectFill"></image>
          </view>
          <view class="contact-info">
            <view class="contact-name">{{ contact.name }}</view>
            <view class="contact-company">{{ contact.company }}</view>
          </view>
          <view class="contact-action">
            <uni-icons class="phone-icon" type="phone" size="20" color="#3370ff"></uni-icons>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-state">
          <text class="empty-text">通讯录暂无联系人</text>
        </view>
      </template>
    </scroll-view>

    <!-- 字母导航 -->
    <view class="letter-nav">
      <text v-for="letter in letters" :key="letter" class="letter-item">{{ letter }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const searchText = ref('');
const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

const contacts = ref([
  {
    name: '张三',
    company: '阿里巴巴科技有限公司',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/771b7cc582ea864cff7c99f23f676c4c.jpg'
  },
  {
    name: '李四',
    company: '腾讯科技有限公司',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/f581ddee214b10233825fd026a29c1ba.jpg'
  },
  {
    name: '王五',
    company: '百度在线网络技术有限公司',
    avatar: 'https://ai-public.mastergo.com/ai/img_res/c7fef1428291774749412e0b2b2db1d4.jpg'
  }
]);
</script>

<style>
page {
  height: 100%;
}

.container {
  height: 100%;
  background-color: #f5f6f7;
  position: relative;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.nav-title {
  margin-left: 24rpx;
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.search-bar {
  padding: 24rpx 32rpx;
  background-color: #ffffff;
}

.search-input-box {
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 24rpx;
  background-color: #f5f6f7;
  border-radius: 36rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 14px;
  color: #333333;
}

.contact-list {
  flex: 1;
  overflow: auto;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.contact-info {
  flex: 1;
  margin-left: 24rpx;
}

.contact-name {
  font-size: 16px;
  color: #333333;
  margin-bottom: 8rpx;
}

.contact-company {
  font-size: 14px;
  color: #999999;
}

.contact-action {
  margin-left: 24rpx;
}

.phone-icon {
  width: 40rpx;
  height: 40rpx;
}

.letter-nav {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.letter-item {
  padding: 8rpx;
  font-size: 12px;
  color: #3370ff;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}
</style>

