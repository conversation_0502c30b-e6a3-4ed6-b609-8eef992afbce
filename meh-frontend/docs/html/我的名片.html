
<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="nav-bar">
      <text class="nav-title">我的名片</text>
      <uni-button type="primary" size="mini" class="add-btn">
        <uni-icons type="plus" size="16" color="#fff"></uni-icons>
        新建名片
      </uni-button>
    </view>

    <!-- 分类切换 -->
    <view class="tab-bar">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        <text>{{ tab }}</text>
      </view>
    </view>

    <!-- 名片列表 -->
    <scroll-view 
      scroll-y 
      class="card-list"
      v-if="cardList.length > 0"
    >
      <view 
        class="card-item"
        v-for="(card, index) in cardList"
        :key="index"
      >
        <image class="avatar" :src="card.avatar" mode="aspectFill"></image>
        <view class="card-content">
          <text class="name">{{ card.name }}</text>
          <text class="position">{{ card.position }}</text>
          <text class="time">{{ card.time }}</text>
        </view>
        <view class="more-btn">
          <uni-icons type="more-filled" size="20" color="#666"></uni-icons>
        </view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <image class="empty-image" :src="emptyImage" mode="aspectFit"></image>
      <text class="empty-text">暂无名片，快去创建吧！</text>
      <uni-button type="primary" size="mini" class="create-btn">去创建</uni-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const tabs = ['个人名片', '企业名片'];
const currentTab = ref(0);

const cardList = ref([
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/b08bef701c391d298394af3b20ddb57e.jpg',
    name: '张三',
    position: '产品经理 | 科技有限公司',
    time: '2024-01-20 更新'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/db29e244601f09dbd170fb1b60977d81.jpg',
    name: '李四',
    position: '销售总监 | 互联网科技公司',
    time: '2024-01-19 更新'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/42d111479ef58c324a5871646027acfb.jpg',
    name: '王五',
    position: '技术主管 | 软件科技公司',
    time: '2024-01-18 更新'
  }
]);

const emptyImage = 'https://ai-public.mastergo.com/ai/img_res/cd2228515008b4b829b4c9ce9739860a.jpg';

const switchTab = (index: number) => {
  currentTab.value = index;
};
</script>

<style>
page {
  height: 100%;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  margin: 0;
}

.tab-bar {
  display: flex;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  position: relative;
}

.tab-item text {
  font-size: 16px;
  color: #666;
}

.tab-item.active text {
  color: #2979ff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #2979ff;
}

.card-list {
  flex: 1;
  padding: 30rpx;
  overflow: auto;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.card-content {
  flex: 1;
}

.name {
  font-size: 16px;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.position {
  font-size: 14px;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.time {
  font-size: 12px;
  color: #999;
  display: block;
}

.more-btn {
  padding: 20rpx;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 30rpx;
}

.create-btn {
  margin: 0;
}
</style>

