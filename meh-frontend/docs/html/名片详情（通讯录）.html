
<template>
  <view class="page">
    <view class="nav-bar">
      <uni-icons type="left" size="20" color="#333333" @click="goBack" />
      <text class="nav-title">联系人名片</text>
    </view>

    <scroll-view class="content" scroll-y>
      <view class="basic-info">
        <image class="avatar" :src="avatarUrl" mode="aspectFill" />
        <text class="name">张小明</text>
        <text class="position">产品经理</text>
        <text class="company">科技创新有限公司</text>
      </view>

      <view class="contact-info">
        <view class="contact-item" @click="handleCall">
          <view class="icon-wrapper">
            <uni-icons type="phone" size="24" color="#007AFF" />
          </view>
          <text class="contact-text">13800138000</text>
          <uni-icons type="right" size="16" color="#CCCCCC" />
        </view>

        <view class="contact-item" @click="handleEmail">
          <view class="icon-wrapper">
            <uni-icons type="email" size="24" color="#007AFF" />
          </view>
          <text class="contact-text"><EMAIL></text>
          <uni-icons type="right" size="16" color="#CCCCCC" />
        </view>

        <view class="contact-item" @click="handleLocation">
          <view class="icon-wrapper">
            <uni-icons type="location" size="24" color="#007AFF" />
          </view>
          <text class="contact-text">北京市朝阳区科技园区88号</text>
          <uni-icons type="right" size="16" color="#CCCCCC" />
        </view>
      </view>

      <view class="company-info">
        <text class="section-title">公司简介</text>
        <text class="company-desc">科技创新有限公司成立于2015年，是一家专注于人工智能和大数据领域的高新技术企业。公司致力于为企业提供智能化解决方案，推动产业数字化转型。</text>
        <view class="tags">
          <text class="tag">AI</text>
          <text class="tag">大数据</text>
          <text class="tag">云计算</text>
        </view>
      </view>
    </scroll-view>

    <view class="action-buttons">
      <button class="action-btn call" @click="handleCall">
        <uni-icons type="phone" size="20" color="#FFFFFF" />
        <text>拨打电话</text>
      </button>
      <button class="action-btn message" @click="handleMessage">
        <uni-icons type="chat" size="20" color="#FFFFFF" />
        <text>发消息</text>
      </button>
      <button class="action-btn copy" @click="handleCopy">
        <uni-icons type="paperclip" size="20" color="#FFFFFF" />
        <text>复制名片</text>
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const avatarUrl = 'https://ai-public.mastergo.com/ai/img_res/53f0098d4ed9e5960e5c80984f5d5756.jpg';

const goBack = () => {
  uni.navigateBack();
};

const handleCall = () => {
  uni.makePhoneCall({
    phoneNumber: '13800138000'
  });
};

const handleEmail = () => {
  uni.setClipboard({
    data: '<EMAIL>',
    success: () => {
      uni.showToast({
        title: '邮箱已复制',
        icon: 'none'
      });
    }
  });
};

const handleLocation = () => {
  uni.showToast({
    title: '正在打开地图...',
    icon: 'none'
  });
};

const handleMessage = () => {
  uni.showToast({
    title: '正在打开消息...',
    icon: 'none'
  });
};

const handleCopy = () => {
  const cardInfo = `张小明\n产品经理\n科技创新有限公司\n电话：13800138000\n邮箱：<EMAIL>\n地址：北京市朝阳区科技园区88号`;
  uni.setClipboard({
    data: cardInfo,
    success: () => {
      uni.showToast({
        title: '名片已复制',
        icon: 'none'
      });
    }
  });
};
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F5F5F5;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #FFFFFF;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  margin-right: 44rpx;
}

.content {
  flex: 1;
  overflow: auto;
}

.basic-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #FFFFFF;
  margin-top: 20rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  margin-bottom: 20rpx;
}

.name {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
}

.position {
  font-size: 14px;
  color: #666666;
  margin-bottom: 8rpx;
}

.company {
  font-size: 14px;
  color: #666666;
}

.contact-info {
  margin-top: 20rpx;
  background-color: #FFFFFF;
  padding: 0 30rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1px solid #EEEEEE;
}

.contact-item:last-child {
  border-bottom: none;
}

.icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.contact-text {
  flex: 1;
  font-size: 14px;
  color: #333333;
}

.company-info {
  margin-top: 20rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.company-desc {
  font-size: 14px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  padding: 8rpx 20rpx;
  background-color: #F0F7FF;
  color: #007AFF;
  border-radius: 4px;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
  background-color: #FFFFFF;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #FFFFFF;
}

.action-btn uni-icons {
  margin-right: 8rpx;
}

.call {
  background-color: #4CD964;
}

.message {
  background-color: #FF9500;
}

.copy {
  background-color: #007AFF;
}
</style>

