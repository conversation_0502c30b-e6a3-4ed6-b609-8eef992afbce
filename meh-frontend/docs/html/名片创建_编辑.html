
<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons @click="goBack" type="left" size="20" color="#333333" class="back-icon"/>
      <text class="nav-title">创建名片</text>
    </view>

    <!-- 滚动区域 -->
    <scroll-view scroll-y class="content">
      <!-- 头像上传区 -->
      <view class="avatar-section">
        <view class="avatar-upload" @click="uploadAvatar">
          <image v-if="avatarUrl" :src="avatarUrl" class="avatar-image"/>
          <view v-else class="avatar-placeholder">
            <uni-icons type="camera-filled" size="32" color="#CCCCCC"/>
            <text class="upload-text">点击上传头像</text>
          </view>
        </view>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-item">
          <view class="label-row">
            <text class="required">*</text>
            <text class="label">姓名</text>
          </view>
          <input type="text" v-model="formData.name" placeholder="请输入姓名" class="input"/>
        </view>

        <!-- 职位 -->
        <view class="form-item">
          <view class="label-row">
            <text class="required">*</text>
            <text class="label">职位</text>
          </view>
          <input type="text" v-model="formData.position" placeholder="请输入职位" class="input"/>
        </view>

        <!-- 公司 -->
        <view class="form-item">
          <view class="label-row">
            <text class="required">*</text>
            <text class="label">公司</text>
          </view>
          <input type="text" v-model="formData.company" placeholder="请输入公司名称" class="input"/>
        </view>

        <!-- 电话 -->
        <view class="form-item">
          <view class="label-row">
            <text class="required">*</text>
            <text class="label">联系电话</text>
          </view>
          <input type="number" v-model="formData.phone" placeholder="请输入联系电话" class="input"/>
        </view>

        <!-- 邮箱 -->
        <view class="form-item">
          <view class="label-row">
            <text class="label">邮箱地址</text>
          </view>
          <input type="text" v-model="formData.email" placeholder="请输入邮箱地址" class="input"/>
        </view>

        <!-- 地址 -->
        <view class="form-item">
          <view class="label-row">
            <text class="label">公司地址</text>
          </view>
          <input type="text" v-model="formData.address" placeholder="请输入公司地址" class="input"/>
        </view>

        <!-- 简介 -->
        <view class="form-item">
          <view class="label-row">
            <text class="label">个人简介</text>
          </view>
          <textarea v-model="formData.introduction" placeholder="请输入个人简介" class="textarea"/>
        </view>
      </view>

      <!-- 模板选择 -->
      <view class="template-section" @click="selectTemplate">
        <view class="template-button">
          <view class="template-left">
            <uni-icons type="paperplane" size="20" color="#2979ff"/>
            <text class="template-text">选择名片模板</text>
          </view>
          <uni-icons type="right" size="16" color="#999999"/>
        </view>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"/>
    </scroll-view>

    <!-- 底部保存按钮 -->
    <view class="bottom-button">
      <button class="save-button" @click="saveCard" :disabled="!isFormValid">
        保存名片
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';

const avatarUrl = ref('');
const formData = ref({
  name: '',
  position: '',
  company: '',
  phone: '',
  email: '',
  address: '',
  introduction: ''
});

const isFormValid = computed(() => {
  return formData.value.name && 
         formData.value.position && 
         formData.value.company && 
         formData.value.phone;
});

const uploadAvatar = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      avatarUrl.value = res.tempFilePaths[0];
    }
  });
};

const goBack = () => {
  uni.navigateBack();
};

const selectTemplate = () => {
  uni.navigateTo({
    url: '/pages/template/index'
  });
};

const saveCard = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请填写必填项',
      icon: 'none'
    });
    return;
  }
  
  uni.showLoading({
    title: '保存中'
  });

  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    });
  }, 1500);
};
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;
  flex-shrink: 0;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: 600;
  color: #333333;
}

.content {
  flex: 1;
  overflow: auto;
}

.avatar-section {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.avatar-upload {
  width: 160rpx;
  height: 160rpx;
  border-radius: 80rpx;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-text {
  font-size: 12px;
  color: #999999;
  margin-top: 10rpx;
}

.form-section {
  background-color: #ffffff;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.form-item {
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label-row {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.required {
  color: #ff4d4f;
  margin-right: 8rpx;
  font-size: 14px;
}

.label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.input {
  width: 100%;
  height: 72rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 14px;
  color: #333333;
}

.textarea {
  width: 100%;
  height: 200rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 14px;
  color: #333333;
}

.template-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.template-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-left {
  display: flex;
  align-items: center;
}

.template-text {
  font-size: 14px;
  color: #333333;
  margin-left: 16rpx;
}

.bottom-placeholder {
  height: 120rpx;
}

.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.save-button {
  width: 100%;
  height: 80rpx;
  background-color: #2979ff;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-button[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}
</style>

