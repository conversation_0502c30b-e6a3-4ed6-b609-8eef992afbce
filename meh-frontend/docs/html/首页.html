
<template>
  <view class="page">
    <view class="container">
      <!-- 顶部欢迎栏 -->
      <view class="welcome-bar">
        <text class="greeting">上午好，海水先生</text>
        <text class="points">当前积分：320</text>
      </view>

      <!-- 主功能快捷入口 -->
      <view class="quick-access">
        <view class="quick-item">
          <uni-icons type="person" size="28" color="#3478f6"></uni-icons>
          <text class="quick-text">我的名片</text>
        </view>
        <view class="quick-item">
          <uni-icons type="scan" size="28" color="#3478f6"></uni-icons>
          <text class="quick-text">扫码添加</text>
        </view>
        <view class="quick-item">
          <uni-icons type="contact" size="28" color="#3478f6"></uni-icons>
          <text class="quick-text">通讯录</text>
        </view>
        <view class="quick-item">
          <uni-icons type="eye" size="28" color="#3478f6"></uni-icons>
          <text class="quick-text">访客记录</text>
        </view>
      </view>

      <!-- 热门推荐区 -->
      <view class="recommend-section">
        <view class="section-title">热门推荐</view>
        <scroll-view class="recommend-scroll" scroll-x>
          <view class="recommend-list">
            <view v-for="(item, index) in recommendList" :key="index" class="recommend-card">
              <image class="card-avatar" :src="item.avatar" mode="aspectFill"></image>
              <text class="card-name">{{item.name}}</text>
              <text class="card-company">{{item.company}}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 营销活动Banner -->
      <swiper class="banner-swiper" circular autoplay interval="3000">
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <image :src="banner.image" mode="aspectFill" class="banner-image"></image>
        </swiper-item>
      </swiper>

      <!-- 底部导航栏 -->
      <view class="tab-bar">
        <view class="tab-item active">
          <uni-icons type="home" size="24" color="#3478f6"></uni-icons>
          <text class="tab-text">首页</text>
        </view>
        <view class="tab-item">
          <uni-icons type="contact" size="24" color="#999"></uni-icons>
          <text class="tab-text">通讯录</text>
        </view>
        <view class="tab-item">
          <uni-icons type="gift" size="24" color="#999"></uni-icons>
          <text class="tab-text">积分商城</text>
        </view>
        <view class="tab-item">
          <uni-icons type="person" size="24" color="#999"></uni-icons>
          <text class="tab-text">个人中心</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const recommendList = ref([
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/77dd34bcaa64d6c1468d2e9d1937fba5.jpg',
    name: '张经理',
    company: '科技有限公司'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/62b59c7fbe3857dccdf9d87c0229dfaf.jpg',
    name: '李总监',
    company: '创新科技'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/970c0949318636dd6df4a90872a4d8f6.jpg',
    name: '王董事',
    company: '投资集团'
  }
])

const banners = ref([
  {
    image: 'https://ai-public.mastergo.com/ai/img_res/06ed08ac10e7e15145b02da8b4927157.jpg'
  },
  {
    image: 'https://ai-public.mastergo.com/ai/img_res/ebced0a80b454a52eb46886ad954b425.jpg'
  }
])
</script>

<style>
page {
  height: 100%;
}

.page {
  height: 100%;
  background-color: #f5f5f5;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
}

.welcome-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 0;
}

.greeting {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.points {
  font-size: 14px;
  color: #666;
}

.quick-access {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin: 20rpx 0;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.quick-text {
  margin-top: 16rpx;
  font-size: 14px;
  color: #333;
}

.recommend-section {
  margin: 30rpx 0;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.recommend-scroll {
  overflow: auto;
}

.recommend-list {
  display: flex;
  padding: 10rpx 0;
}

.recommend-card {
  flex-shrink: 0;
  width: 200rpx;
  margin-right: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.card-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-bottom: 16rpx;
}

.card-name {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-company {
  display: block;
  font-size: 12px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.banner-swiper {
  width: 100%;
  height: 200rpx;
  margin: 30rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 100rpx;
  background: #fff;
  margin: auto 0 0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-text {
  margin-top: 8rpx;
  font-size: 12px;
  color: #999;
}

.tab-item.active .tab-text {
  color: #3478f6;
}
</style>

