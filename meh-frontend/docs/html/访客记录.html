
<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" />
      <text class="title">访客记录</text>
    </view>

    <!-- 访客统计 -->
    <view class="visitor-stats">
      <text class="stats-text">最近有 12 人查看了你的名片</text>
    </view>

    <!-- 访客列表 -->
    <scroll-view class="visitor-list" scroll-y>
      <block v-if="visitorList.length > 0">
        <view v-for="(item, index) in visitorList" :key="index" class="visitor-card">
          <view class="avatar">
            <image :src="item.avatar" mode="aspectFill" />
          </view>
          <view class="info">
            <view class="name-row">
              <text class="name">{{item.name}}</text>
              <text class="time">{{item.time}}</text>
            </view>
            <text class="company">{{item.company}} - {{item.position}}</text>
          </view>
        </view>
      </block>
      <view v-else class="empty-state">
        <image class="empty-image" :src="emptyImageUrl" mode="aspectFit" />
        <text class="empty-title">暂无访客记录</text>
        <text class="empty-desc">还没有人访问你的名片哦，快去分享吧~</text>
      </view>
    </scroll-view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

// 访客数据
const visitorList = ref([
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/ecaa64077fc2c7ca4c9a003fb46efe00.jpg',
    name: '张明',
    company: '科技有限公司',
    position: '产品经理',
    time: '2025-06-02 15:12'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/7efb7ceda5a88e497c9f652bc1ab1e0b.jpg',
    name: '李婷',
    company: '互联网科技',
    position: '市场总监',
    time: '2025-06-01 20:43'
  },
  {
    avatar: 'https://ai-public.mastergo.com/ai/img_res/a879a65520acef01794f24417148c945.jpg',
    name: '王强',
    company: '创新科技',
    position: '技术总监',
    time: '2025-06-01 18:30'
  }
]);

// 空状态图片
const emptyImageUrl = 'https://ai-public.mastergo.com/ai/img_res/763224e437f3d3e513daa4364fca6143.jpg';
</script>

<style>
page {
  height: 100%;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F8F8F8;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #FFFFFF;
  flex-shrink: 0;
}

.back-icon {
  color: #333333;
}

.title {
  flex: 1;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  margin-right: 24px;
}

.visitor-stats {
  padding: 30rpx;
  background-color: #FFFFFF;
  margin-top: 20rpx;
  flex-shrink: 0;
}

.stats-text {
  font-size: 16px;
  color: #333333;
}

.visitor-list {
  flex: 1;
  overflow: auto;
  padding: 20rpx 30rpx;
}

.visitor-card {
  display: flex;
  padding: 30rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.info {
  flex: 1;
}

.name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}

.time {
  font-size: 12px;
  color: #999999;
}

.company {
  font-size: 14px;
  color: #666666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 16px;
  color: #333333;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
}
</style>

