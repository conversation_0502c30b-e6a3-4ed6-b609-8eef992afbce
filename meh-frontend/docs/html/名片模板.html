
<template>
  <view class="page">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <uni-icons class="back-icon" type="back" size="24" color="#333333"/>
      <text class="title">选择名片模板</text>
    </view>
    
    <!-- 分类筛选栏 -->
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          v-for="(item, index) in categories" 
          :key="index"
          :class="['category-item', currentCategory === index ? 'active' : '']"
          @tap="selectCategory(index)"
        >
          {{ item }}
        </view>
      </view>
    </scroll-view>

    <!-- 模板展示区 -->
    <scroll-view class="template-container" scroll-y>
      <view class="template-grid">
        <view 
          v-for="(template, index) in templates" 
          :key="index"
          class="template-item"
          @tap="selectTemplate(index)"
        >
          <view class="template-image-wrapper">
            <image :src="template.image" mode="aspectFill" class="template-image"/>
            <uni-icons 
              v-if="selectedTemplate === index" 
              class="check-icon" 
              type="checkbox-filled" 
              size="24" 
              color="#2B85E4"
            />
          </view>
          <text class="template-name">{{ template.name }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <button class="confirm-button" @tap="confirmSelection">确认选择</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const categories = ref(['商务型', '时尚型', '简洁型', '创意型', '科技型']);
const currentCategory = ref(0);

const templates = ref([
  {
    name: '商务简约风格',
    image: 'https://ai-public.mastergo.com/ai/img_res/205710c1dbdee1c3f4201fc30654753b.jpg'
  },
  {
    name: '科技创新风格',
    image: 'https://ai-public.mastergo.com/ai/img_res/da7941ccd8be8378be442930486666e4.jpg'
  },
  {
    name: '艺术时尚风格',
    image: 'https://ai-public.mastergo.com/ai/img_res/771599a8e2086813d5f5217cabc2dd28.jpg'
  },
  {
    name: '简约轻奢风格',
    image: 'https://ai-public.mastergo.com/ai/img_res/dd773cce6cdfa7123ae8ddea8427d730.jpg'
  }
]);

const selectedTemplate = ref(-1);

const selectCategory = (index: number) => {
  currentCategory.value = index;
};

const selectTemplate = (index: number) => {
  selectedTemplate.value = index;
};

const confirmSelection = () => {
  if (selectedTemplate.value === -1) {
    uni.showToast({
      title: '请选择模板',
      icon: 'none'
    });
    return;
  }
  uni.showToast({
    title: '已选择模板',
    icon: 'success'
  });
};
</script>

<style>
page {
  height: 100%;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F8F8F8;
}

.nav-bar {
  position: relative;
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #FFFFFF;
  padding: 0 30rpx;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-icon {
  width: 48rpx;
  height: 48rpx;
}

.title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.category-scroll {
  background-color: #FFFFFF;
  padding: 20rpx 0;
  flex-shrink: 0;
}

.category-list {
  display: flex;
  padding: 0 30rpx;
}

.category-item {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  font-size: 14px;
  color: #666666;
  background-color: #F5F5F5;
  border-radius: 32rpx;
  white-space: nowrap;
}

.category-item.active {
  color: #FFFFFF;
  background-color: #2B85E4;
}

.template-container {
  flex: 1;
  overflow: auto;
}

.template-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 30rpx;
}

.template-item {
  width: calc(50% - 15rpx);
  margin-bottom: 30rpx;
}

.template-item:nth-child(2n) {
  margin-left: 30rpx;
}

.template-image-wrapper {
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  background-color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.template-image {
  width: 100%;
  height: 400rpx;
}

.check-icon {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
}

.template-name {
  display: block;
  margin-top: 16rpx;
  font-size: 14px;
  color: #333333;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bottom-button {
  padding: 30rpx;
  background-color: #FFFFFF;
  flex-shrink: 0;
}

.confirm-button {
  background-color: #2B85E4;
  color: #FFFFFF;
  font-size: 16px;
  border-radius: 44rpx;
}

.confirm-button:active {
  opacity: 0.8;
}
</style>

