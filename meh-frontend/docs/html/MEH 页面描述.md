---

## 🚀 第 1 个页面：启动页（Splash Screen）

### 一、统一风格描述（适用于所有页面）

* **整体色调：**

  * 主色调：采用蓝色系（科技、专业感），辅色为白色与灰色。
  * 辅助色：少量使用品牌红色或橙色点缀，以突出重点功能或按钮。

* **字体规范：**

  * 主标题字体：微软雅黑 Bold，加粗，大小20\~24pt。
  * 次级标题字体：微软雅黑 Regular，16\~18pt。
  * 正文字体：微软雅黑 Regular，14\~16pt。
  * 小标签与提示字体：微软雅黑 Regular，12pt，浅灰色。

* **图标风格：**

  * 采用扁平化图标风格，简约明了，易于识别。
  * 图标颜色统一采用蓝色与灰色调。

* **布局风格：**

  * 统一采用简洁留白设计，突出重点信息。
  * 组件圆角风格（圆角矩形按钮），圆角弧度统一为8\~12px。
  * 页面过渡动画统一为淡入淡出，流畅自然。

---

### 二、启动页元素描述

本页面元素极简，突出品牌展示：

* **LOGO**

  * 居中显示，尺寸占页面宽度的30%-40%。
  * LOGO背景透明，主体颜色以品牌蓝色为主。

* **MEH企业家俱乐部文字**

  * 品牌中文名：“码布斯企业家俱乐部”。
  * 英文名：“Mabus Entrepreneurs’ Hub”。
  * 文字采用微软雅黑加粗，主色调（蓝色或灰色）。

* **启动加载动画（可选）**

  * 使用简单加载动画（旋转或呼吸效果），位于屏幕底部。
  * 颜色为品牌辅色（灰色或蓝色）。

* **品牌标语（Slogan）**

  * 简短的一句品牌口号（例如：“赋能数字未来，连接价值人脉”）。
  * 字体较小，颜色为浅灰色，位于LOGO下方偏下位置。

---

### 三、内容描述（本页内容极简）

* LOGO（核心视觉识别元素）
* 品牌名称与英文名（清晰醒目）
* 品牌Slogan（次级描述，强化品牌印象）

---

### 四、布局描述（详细布局）

采用垂直居中布局：

```
页面顶部留白 (10%-15%)

[LOGO]
(居中，页面核心视觉)

[品牌中文名]
（微软雅黑 Bold，20~24pt，LOGO下方适当间距）

[品牌英文名]
（微软雅黑 Regular，16~18pt，中文名下方适当间距）

[品牌Slogan]
（微软雅黑 Regular，14pt，浅灰色，英文名下方稍远距离）

底部加载动画（如有）
(页面底部，淡入淡出或简单旋转动画，视觉提示加载状态)

页面底部留白 (5%-10%)
```

---

### 🎯 **页面目标：**

* 清晰展示品牌识别元素，建立良好的品牌初印象。
* 提供快速加载感受，避免用户等待的不适感。

---


## 🏠 第 2 个页面：首页（Home Page）

### 一、统一风格描述（承接启动页风格）

* **整体色调：**

  * 主色调：蓝色系（科技感和专业感），背景以浅白或浅灰色为主。
  * 辅助色：少量使用橙色或红色点缀突出关键按钮和活动入口。

* **字体规范：**

  * 主标题字体：微软雅黑 Bold，加粗，20\~24pt。
  * 次级标题字体：微软雅黑 Regular，16\~18pt。
  * 正文字体：微软雅黑 Regular，14\~16pt。
  * 标签、提示类小字体：微软雅黑 Regular，12pt，浅灰色。

* **图标风格：**

  * 统一扁平化设计，简洁易懂，颜色以品牌蓝色为主。

* **布局风格：**

  * 页面整体采用分区块设计，区块之间适当留白，避免视觉拥挤感。
  * 功能入口使用圆角卡片，圆角统一为 8\~12px。
  * 使用流畅的垂直滚动布局，内容丰富但不拥挤。

---

### 二、首页元素描述

首页主要提供导航、功能入口和快捷访问方式，元素如下：

* **顶部欢迎栏**

  * 欢迎语（例如：“上午好，海水先生”），字体 16\~18pt。
  * 当前积分显示（例如：“当前积分：320”），字体 14\~16pt，右侧靠边。

* **主功能快捷入口区（4个快捷按钮）**
  采用卡片式按钮：

  * 我的名片（带图标）
  * 扫码添加（带图标）
  * 通讯录（带图标）
  * 访客记录（带图标）

  每个按钮采用统一风格圆角矩形卡片设计，配图标 + 功能名称，色调主蓝色系。

* **热门推荐区（可选）**

  * 推荐名片展示（水平滑动列表）
  * 每个名片卡片展示头像、姓名、公司名，点击进入详情

* **营销活动 Banner 区**

  * 轮播 Banner 设计，宣传当前的积分活动或裂变营销活动
  * 图片尺寸统一，横向比例约为 3:1 或 4:1，圆角矩形风格

* **底部导航栏（Tab栏）**

  * 首页（高亮状态）
  * 通讯录
  * 积分商城
  * 个人中心
  * 统一扁平化图标风格，文字+图标展示

---

### 三、内容描述（页面主要内容模块）

| 内容区域        | 详细说明               | 展示方式        |
| ----------- | ------------------ | ----------- |
| 顶部欢迎栏       | 用户姓名、当前积分          | 横向居中对齐      |
| 主功能快捷入口区    | 我的名片、扫码添加、通讯录、访客记录 | 2x2宫格布局卡片按钮 |
| 热门推荐区       | 名片推荐展示（横向滚动卡片列表）   | 水平滑动卡片      |
| 营销活动 Banner | 营销活动信息推广           | 轮播图形式       |
| 底部导航栏       | 首页、通讯录、积分商城、个人中心   | 固定底部        |

---

### 四、布局描述（详细布局）

布局采用上下结构：

```
顶部留白（约5%）

[顶部欢迎栏] （横向布局）
欢迎语（左侧）      当前积分（右侧）

（上下留白约5%）

[主功能快捷入口区] （2x2网格布局）
[ 我的名片 ] [ 扫码添加 ]
[ 通讯录 ]   [ 访客记录 ]

（上下留白约5%）

[热门推荐区] （横向滑动列表卡片）
[名片1] [名片2] [名片3]...

（上下留白约5%）

[营销活动 Banner 区] （轮播展示）
横幅图片或活动描述卡片，自动轮播切换

页面内容结束后适当留白（约5%-10%）

固定底部：
[底部导航栏（Tab栏）]
首页  通讯录  积分商城  个人中心
```

---

### 🎯 **页面目标**

* 快速直观访问核心功能
* 提升用户活跃与互动率
* 清晰展示核心服务和营销推广活动

---


## 👤 第 3 个页面：个人中心（User Profile Page）

### 一、统一风格描述（继承首页风格）

* **整体色调：**

  * 主色调：延续蓝色系，辅助背景采用浅白或浅灰色。
  * 辅助色：橙色或红色，突出重要功能按钮。

* **字体规范：**

  * 主标题：微软雅黑 Bold，加粗，20\~24pt。
  * 次级标题：微软雅黑 Regular，16\~18pt。
  * 正文字体：微软雅黑 Regular，14\~16pt。
  * 小标签/提示文字：微软雅黑 Regular，12pt，浅灰色。

* **图标风格：**

  * 扁平化风格图标，颜色采用蓝色和灰色统一。

* **布局风格：**

  * 简洁明快的垂直列表布局。
  * 信息清晰分组展示，每个功能或信息区块清晰分割，圆角矩形卡片式设计，圆角统一8\~12px。

---

### 二、个人中心页面元素描述

个人中心页面用于集中管理用户个人信息与偏好设置，元素包括：

* **个人信息区**

  * 用户头像（圆形裁剪）
  * 用户姓名（明显突出）
  * 用户当前会员等级（如“MEH金牌会员”）

* **积分与奖励区**

  * 当前积分余额展示
  * 点击跳转积分商城的快捷按钮（如“查看积分商城”）

* **功能设置菜单**

  * 编辑个人资料（右侧小箭头表示可进入）
  * 我的名片管理（快捷入口）
  * 我的二维码（展示个人专属二维码）
  * 积分兑换记录
  * 设置与隐私（跳转设置页面）

* **退出登录按钮**

  * 底部显著位置，采用红色按钮强调提醒

---

### 三、内容描述（页面主要内容模块）

| 区域名称   | 内容明细                     | 展示方式         |
| ------ | ------------------------ | ------------ |
| 个人信息区  | 头像、姓名、会员等级               | 顶部卡片横向展示     |
| 积分与奖励区 | 当前积分、积分商城入口              | 独立卡片垂直展示     |
| 功能设置菜单 | 编辑资料、名片管理、二维码、兑换记录、设置与隐私 | 垂直列表菜单（右侧箭头） |
| 退出登录按钮 | 安全退出当前账号                 | 底部红色按钮显著突出   |

---

### 四、布局描述（详细布局）

采用垂直分组列表布局方式：

```
页面顶部留白（约5%）

[个人信息区] (横向布局)
[头像]    [用户姓名与会员等级]

（上下留白约5%）

[积分与奖励区] (垂直卡片布局)
当前积分: 320 ➡️ 积分商城（小箭头）

（上下留白约5%）

[功能设置菜单] (垂直列表)
[编辑个人资料] ➡️
[我的名片管理] ➡️
[我的二维码] ➡️
[积分兑换记录] ➡️
[设置与隐私] ➡️

页面内容与底部按钮之间留白（约10%）

[退出登录按钮]（红色按钮）
居中显示，按钮圆角矩形，高度适中，突出强调。

页面底部留白（约5%-10%）
```

---

### 🎯 **页面目标**

* 集中管理用户信息，提供便捷操作入口。
* 提升用户自我管理和信息设置的效率。
* 明确展示用户会员身份与权益，提高用户粘性。

---


## 📇 第 4 个页面：名片列表页（Card List Page）

### 一、统一风格描述（继承整体统一风格）

* **整体色调**

  * 主色调：延续蓝色系，背景以浅灰或白色为主。
  * 辅助色：橙色或红色用于按钮和重要功能提示。

* **字体规范**

  * 主标题：微软雅黑 Bold，加粗，20\~24pt。
  * 次级标题：微软雅黑 Regular，16\~18pt。
  * 正文字体：微软雅黑 Regular，14\~16pt。
  * 提示、小标签字体：微软雅黑 Regular，12pt，浅灰色。

* **图标风格**

  * 扁平化风格，图标采用品牌蓝色或灰色，统一清晰。

* **布局风格**

  * 纵向列表式布局，清晰简洁，易于快速查看。
  * 采用卡片式设计，每条信息以圆角矩形卡片呈现，圆角统一8\~12px。

---

### 二、名片列表页元素描述

用于管理个人和企业名片，元素包括：

* **顶部导航栏**

  * 标题：“我的名片”
  * 新建名片按钮（右侧显著位置，橙色突出显示）

* **名片分类切换栏（Tab栏）**

  * “个人名片” 与 “企业名片” 两个选项卡。
  * 默认高亮显示“个人名片”。

* **名片信息卡片列表**

  * 每个卡片包含：

    * 用户头像或企业LOGO（圆角或圆形）
    * 名片名称（个人姓名或企业名）
    * 所属公司/职务简短描述
    * 更新时间（或创建日期）
    * 右侧“更多操作”按钮（三个点图标）

* **列表为空提示**

  * 当名片列表为空时，显示友好提示信息（如：“暂无名片，快去创建吧！”）
  * 提供创建名片快捷入口按钮。

---

### 三、内容描述（页面主要内容模块）

| 区域名称    | 内容明细             | 展示方式          |
| ------- | ---------------- | ------------- |
| 顶部导航栏   | 页面标题、新建名片按钮      | 横向顶部居中布局，右侧按钮 |
| 名片分类切换栏 | 个人名片、企业名片        | Tab切换模式       |
| 名片列表卡片  | 名片头像、名称、职务/公司、日期 | 垂直滚动列表卡片展示    |
| 列表为空提示  | 提示文本、创建名片快捷按钮    | 中心提示区显示       |

---

### 四、布局描述（详细布局）

采用纵向卡片列表布局：

```
顶部导航栏（顶部留白约5%）
“我的名片”               [+ 新建名片]

（上下留白约3%）

名片分类切换栏（Tabs布局）
[个人名片] | [企业名片]

（上下留白约5%）

名片列表（垂直滚动布局）
-------------------
|[头像] [姓名/公司名]|
|职位/所属公司名       |
|更新时间             | [更多]
-------------------
|[头像] [姓名/公司名]|
|职位/所属公司名       |
|更新时间             | [更多]
-------------------
（持续垂直滚动列表）

（无内容情况特殊显示）
居中：“暂无名片，快去创建吧！”
[去创建] 按钮（橙色突出显示）

底部适当留白（5%-10%）
```

---

### 🎯 **页面目标**

* 快速浏览、管理和操作个人或企业名片。
* 提供清晰的创建名片入口，增强用户操作便利性。
* 统一明确的风格，提升页面操作体验和视觉愉悦感。

---


## 📝 第 5 个页面：名片创建/编辑页（Create/Edit Card Page）

### 一、统一风格描述（沿用整体设计风格）

* **整体色调**

  * 主色调：品牌蓝色，背景浅白或浅灰色。
  * 辅助色：橙色或红色用于关键按钮（保存、提交等）。

* **字体规范**

  * 主标题：微软雅黑 Bold，加粗，20\~24pt。
  * 表单标签：微软雅黑 Regular，16\~18pt。
  * 表单输入文字：微软雅黑 Regular，14\~16pt。
  * 辅助说明：微软雅黑 Regular，12pt，浅灰色。

* **图标风格**

  * 扁平化设计，统一颜色蓝色与灰色搭配。

* **布局风格**

  * 使用标准表单布局方式，纵向滚动页面。
  * 每个表单项独立卡片式呈现，圆角矩形，统一圆角为8\~12px。
  * 页面底部固定保存/提交按钮。

---

### 二、名片创建/编辑页元素描述

名片创建页主要是填写名片信息，元素包括：

* **页面标题栏**

  * 标题为“创建名片”或“编辑名片”，左侧返回按钮。

* **头像/企业LOGO上传**

  * 图片上传区域，圆形或圆角矩形裁剪预览。

* **名片信息填写表单**

  * 姓名（个人名片）/企业名称（企业名片）（必填）
  * 职务或职位名称（个人名片）（必填）
  * 公司名称（个人名片）（必填）
  * 联系电话（必填）
  * 邮箱地址（选填）
  * 公司地址（选填）
  * 个人简介或企业简介（多行文本框，可选填）

* **名片模板选择按钮**

  * 快速跳转到模板选择页面入口按钮

* **提交保存按钮**

  * 底部固定位置，按钮颜色明显突出（橙色或品牌蓝色）

---

### 三、内容描述（页面主要内容模块）

| 区域名称       | 内容明细             | 展示方式           |
| ---------- | ---------------- | -------------- |
| 页面标题栏      | 标题（创建或编辑名片）与返回按钮 | 顶部横向布局         |
| 头像/LOGO上传区 | 上传图片区域           | 图片上传框          |
| 名片信息填写表单区  | 各种表单字段           | 纵向标准表单         |
| 名片模板选择区    | 快捷跳转按钮           | 小按钮卡片          |
| 提交保存按钮     | 保存或提交表单数据        | 页面底部固定位置按钮突出显示 |

---

### 四、布局描述（详细布局）

标准纵向表单布局设计：

```
顶部导航栏（顶部留白约5%）
[返回按钮]    创建名片（或编辑名片）

（上下留白约5%）

[头像/LOGO上传区]（居中布局）
点击上传图片（圆形或圆角矩形）

（上下留白约5%）

[名片信息填写表单区]（纵向滚动表单）
- 姓名/企业名称      [输入框]
- 职务/职位名称      [输入框]
- 公司名称            [输入框]
- 联系电话            [输入框]
- 邮箱地址            [输入框]
- 公司地址            [输入框]
- 个人/企业简介     [多行文本框]

（上下留白约3%）

[名片模板选择按钮]（小按钮卡片样式）
快速选择模板 ➡️

页面底部固定（提交保存按钮）
[保存名片] 或 [提交创建]（明显颜色按钮）

页面底部留白（约5%-10%）
```

---

### 🎯 **页面目标**

* 提供清晰易用的名片创建和编辑表单。
* 明确必填选填字段，提升用户填写效率。
* 快速模板跳转选择功能，提升用户名片个性化设计的便捷性。

---

## 🎨 第 6 个页面：名片模板选择页（Card Template Selection Page）

### 一、统一风格描述（延续整体统一风格）

* **整体色调**

  * 主色调：延续蓝色系，背景浅白或浅灰色。
  * 辅助色：橙色或红色用于选择按钮和强调效果。

* **字体规范**

  * 主标题：微软雅黑 Bold，加粗，20\~24pt。
  * 模板名称字体：微软雅黑 Regular，16\~18pt。
  * 辅助说明字体：微软雅黑 Regular，12\~14pt，灰色。

* **图标风格**

  * 扁平化简洁图标，以品牌蓝色或灰色为主。

* **布局风格**

  * 网格化布局，模板清晰呈现。
  * 模板预览图以统一大小、圆角矩形呈现，圆角统一8\~12px。
  * 页面底部固定模板确定按钮。

---

### 二、名片模板选择页元素描述

名片模板选择页面，用户可选择不同风格模板以定制名片样式，元素包括：

* **页面标题栏**

  * 页面标题：“选择名片模板”，左侧返回按钮。

* **模板展示网格**

  * 展示模板效果图（横向两列或三列展示）。
  * 每个模板图下方附带模板名称或编号。
  * 选中模板带有明显的勾选标记（如右上角勾选图标）。

* **模板分类筛选栏（可选）**

  * 快速筛选不同风格模板（如商务型、时尚型、简洁型）。

* **模板预览功能**

  * 点击模板弹出全屏预览，可详细查看效果。

* **确认选择按钮**

  * 页面底部固定按钮，用于确认当前选中的模板。

---

### 三、内容描述（页面主要内容模块）

| 区域名称       | 内容明细           | 展示方式        |
| ---------- | -------------- | ----------- |
| 页面标题栏      | 标题与返回按钮        | 顶部横向布局      |
| 模板分类筛选栏    | 商务型、时尚型、简洁型等分类 | 横向按钮组切换（可选） |
| 模板展示网格     | 模板预览图、模板名称或编号  | 网格布局（两列或三列） |
| 模板全屏预览（弹窗） | 点击查看模板详细效果     | 弹出式全屏预览     |
| 确认选择按钮     | 确认选择模板         | 底部固定突出按钮    |

---

### 四、布局描述（详细布局）

网格布局方式：

```
顶部导航栏（顶部留白约5%）
[返回按钮]   选择名片模板

（上下留白约3%）

模板分类筛选栏（横向滑动或点击切换，可选）
[商务型] [时尚型] [简洁型] ...

（上下留白约5%）

模板展示网格区（2或3列网格布局）
------------------------ 
| [模板预览图1] | [模板预览图2] |
|  模板名称     |   模板名称     |
------------------------
| [模板预览图3] | [模板预览图4] |
|  模板名称     |   模板名称     |
------------------------
（持续网格向下滚动）

点击模板弹出全屏预览效果

页面底部固定（确认按钮）
[确认选择]（明显突出按钮颜色）

底部适当留白（约5%-10%）
```

---

### 🎯 **页面目标**

* 快速直观地展示不同名片风格模板，便于用户选择。
* 提供便利的模板筛选与预览功能，提升选择效率。
* 确保页面设计风格统一，操作便捷且视觉清晰。

---


## 📱 第 7 个页面：名片详情页（Card Detail Page）

### 一、统一风格描述（延续整体风格）

* **整体色调**

  * 主色调：蓝色系，背景为浅灰色或白色。
  * 辅助色：橙色、红色用于突出按钮和交互提示。

* **字体规范**

  * 名片姓名/企业名：微软雅黑 Bold，加粗，20\~24pt。
  * 职位、公司等次级信息：微软雅黑 Regular，16\~18pt。
  * 正文与联系方式字体：微软雅黑 Regular，14\~16pt。
  * 提示/说明文字：微软雅黑 Regular，12\~14pt，浅灰色。

* **图标风格**

  * 扁平化设计，品牌蓝色或灰色统一。

* **布局风格**

  * 信息分组式展示，每个区块圆角矩形卡片，圆角统一为8\~12px。
  * 页面信息清晰，重点突出，易于用户快速查看。

---

### 二、名片详情页元素描述

该页面用于详细展示个人或企业的名片信息，元素包括：

* **顶部导航栏**

  * 标题为：“名片详情”，左侧返回按钮，右侧分享按钮。

* **名片主体信息区**

  * 头像或企业LOGO明显展示（圆形或圆角矩形）。
  * 名字（个人）/公司名（企业）（明显突出）。
  * 职位或企业标语/简短介绍。

* **联系信息区**

  * 电话号码（可直接拨打）
  * 电子邮箱（可点击发送邮件）
  * 公司地址（可点击导航到地图）

* **二维码展示区**

  * 名片专属二维码，方便保存与分享。

* **互动按钮区**

  * 点赞按钮、留言按钮、在线咨询按钮。

* **访客统计区（小提示）**

  * 显示“XX人查看了此名片”小字提示（可点击跳转访客记录页面）。

---

### 三、内容描述（页面主要内容模块）

| 区域名称    | 内容明细                 | 展示方式             |
| ------- | -------------------- | ---------------- |
| 页面标题栏   | 标题、返回按钮、分享按钮         | 顶部横向布局           |
| 名片主体信息区 | 头像/LOGO、姓名/企业名、职位/介绍 | 居中布局显著突出         |
| 联系信息区   | 电话、邮箱、公司地址           | 垂直列表（可交互图标）      |
| 二维码展示区  | 名片二维码                | 居中展示             |
| 互动按钮区   | 点赞、留言、咨询按钮           | 底部横向布局按钮         |
| 访客统计区   | XX人查看了此名片            | 底部小字提示，点击可跳转访客页面 |

---

### 四、布局描述（详细布局）

详细分组卡片布局：

```
顶部导航栏（顶部留白约5%）
[返回按钮]   名片详情   [分享按钮]

（上下留白约3%）

[名片主体信息区] (居中展示)
[头像/LOGO]
[姓名/公司名]（明显突出字体）
[职位/企业简介]（次级字体）

（上下留白约5%）

[联系信息区] (卡片式布局)
☎️ 电话号码
📧 邮箱地址
📍 公司地址（地图链接）

（上下留白约5%）

[二维码展示区] (居中卡片)
[名片二维码]

（上下留白约5%）

[互动按钮区] (底部横向布局)
👍 点赞    💬 留言    💻 在线咨询

页面底部（访客统计小提示）
“共有 XX 人查看了此名片”（小字提示，点击可跳转）

底部留白（约5%-10%）
```

---

### 🎯 **页面目标**

* 清晰、详细地展示名片所有信息，便于用户快速获取和互动。
* 提供便捷交互（拨打电话、发送邮件、导航地图）。
* 增加用户互动率和参与感（点赞、留言、在线咨询）。

---

## 📲 第 8 个页面：名片二维码分享页（Card QR-Code Sharing Page）

### 一、统一风格描述（延续整体风格）

* **整体色调**

  * 主色调：品牌蓝色背景搭配浅白或浅灰。
  * 辅助色：使用橙色或红色突出重要按钮。

* **字体规范**

  * 主标题字体：微软雅黑 Bold，加粗，20\~24pt。
  * 二维码描述字体：微软雅黑 Regular，16pt。
  * 提示或辅助说明字体：微软雅黑 Regular，12\~14pt，浅灰色。

* **图标风格**

  * 统一扁平化设计，颜色蓝色或灰色。

* **布局风格**

  * 极简风格居中布局。
  * 二维码以圆角矩形背景卡片展示，圆角统一8\~12px。
  * 按钮清晰明显，便于用户快速操作。

---

### 二、名片二维码分享页元素描述

该页面展示名片的二维码，以便用户快速分享，元素包括：

* **页面标题栏**

  * 标题：“分享名片”，左侧返回按钮。

* **二维码主体展示区**

  * 名片二维码居中放大显示，便于扫描。
  * 下方附带提示文字：“扫一扫，快速添加名片”。

* **二维码保存按钮**

  * 点击保存二维码至手机相册。

* **微信好友/朋友圈分享按钮**

  * 快速分享给微信好友或朋友圈。

* **分享外链按钮（可选）**

  * 生成名片在线链接，供用户在外部平台分享。

---

### 三、内容描述（页面主要内容模块）

| 区域名称         | 内容明细        | 展示方式     |
| ------------ | ----------- | -------- |
| 页面标题栏        | 标题、返回按钮     | 顶部横向布局   |
| 二维码主体展示区     | 二维码、扫描提示    | 中心明显位置   |
| 保存二维码按钮      | 保存二维码至相册    | 底部明显按钮   |
| 微信好友/朋友圈分享按钮 | 分享到微信好友、朋友圈 | 底部横向布局按钮 |
| 分享外链按钮（可选）   | 生成并复制外链     | 底部额外按钮   |

---

### 四、布局描述（详细布局）

清晰居中展示布局：

```
顶部导航栏（顶部留白约5%）
[返回按钮]       分享名片

（上下留白约10%）

[二维码主体展示区] (中心居中)
   名片二维码（较大尺寸）
 “扫一扫，快速添加名片”（二维码下方文字提示）

（上下留白约10%）

[保存二维码按钮]（突出按钮）
保存二维码到相册 📷

（上下留白约5%）

[微信好友] | [朋友圈分享]（横向布局按钮，明显突出）

（上下留白约5%）

[分享外链按钮（可选）]
生成名片外链 🔗

底部留白（约10%）
```

---

### 🎯 **页面目标**

* 提供清晰易用的二维码分享和保存方式。
* 快捷方便地分享名片到社交平台（微信、朋友圈）。
* 提升名片传播效果，增加曝光率和互动率。

---

## 📒 第 9 个页面：通讯录列表页（Address Book List Page）

### 一、统一风格描述（延续整体风格）

* **整体色调**

  * 主色调：品牌蓝色搭配浅白或浅灰色背景。
  * 辅助色：橙色或红色突出操作按钮及交互元素。

* **字体规范**

  * 主标题字体：微软雅黑 Bold，加粗，20\~24pt。
  * 联系人姓名字体：微软雅黑 Regular，16\~18pt。
  * 职位、公司字体：微软雅黑 Regular，14\~16pt。
  * 提示文字字体：微软雅黑 Regular，12\~14pt，浅灰色。

* **图标风格**

  * 扁平化风格图标，品牌蓝色或灰色统一呈现。

* **布局风格**

  * 通讯录标准列表布局，便于快速滚动查找。
  * 每个联系人以圆角矩形卡片展示，圆角统一为8\~12px。
  * 顶部配备搜索栏，方便快速查找。

---

### 二、通讯录列表页元素描述

该页面用于管理与查看已保存的名片联系人，元素包括：

* **页面标题栏**

  * 标题：“通讯录”，左侧返回按钮。

* **顶部搜索栏**

  * 支持关键词快速查找联系人。

* **通讯录联系人列表**

  * 每个联系人信息包含：

    * 头像或企业LOGO（圆形或圆角矩形）
    * 姓名或企业名
    * 职位或所属公司（次级文字）
    * 快速拨打电话按钮（小电话图标，可选）

* **字母快速导航条（可选）**

  * 右侧边缘，快速定位到对应姓氏或企业名。

* **列表为空提示（可选）**

  * 当通讯录为空时，友好提示用户：“通讯录暂无联系人”。

---

### 三、内容描述（页面主要内容模块）

| 区域名称      | 内容明细           | 展示方式       |
| --------- | -------------- | ---------- |
| 页面标题栏     | 标题与返回按钮        | 顶部横向布局     |
| 顶部搜索栏     | 输入框及搜索按钮       | 横向居中布局     |
| 联系人列表区    | 联系人头像、姓名、职位/公司 | 垂直滚动列表卡片布局 |
| 字母导航条（可选） | 快速定位联系人        | 页面右侧边缘竖直显示 |
| 列表为空提示    | 提示文字           | 居中显示提示信息   |

---

### 四、布局描述（详细布局）

标准通讯录布局：

```
顶部导航栏（顶部留白约5%）
[返回按钮]      通讯录

（上下留白约3%）

顶部搜索栏（输入框居中）
🔍 搜索联系人

（上下留白约3%）

联系人列表（垂直滚动列表）
-------------------------
|[头像]  [联系人姓名]        |
|        职位/公司          ☎️|
-------------------------
|[头像]  [联系人姓名]        |
|        职位/公司          ☎️|
-------------------------
（持续垂直滚动列表）

页面右侧边缘（可选）：
[A-Z]字母导航条，快速定位

（无联系人时显示提示）
居中显示：“通讯录暂无联系人”

底部适当留白（约5%-10%）
```

---

### 🎯 **页面目标**

* 提供高效查找和管理通讯录联系人方式。
* 清晰展示联系人信息，方便快捷沟通（拨打电话）。
* 优化交互体验，提升用户操作便利性。

---


## 🧾 第 10 个页面：名片详情（通讯录视图）

> 用于从通讯录中查看联系人名片，提供清晰信息展示与快速沟通入口。

---

### 一、统一风格描述（承接名片详情页，但更“联系导向”）

* **整体色调**

  * 主色调：品牌蓝色，背景白色或浅灰。
  * 辅助色：绿色（拨打电话）、橙色（消息按钮）强调交互性。

* **字体规范**

  * 姓名/公司名称：微软雅黑 Bold，20\~24pt。
  * 职务/公司地址/联系方式：微软雅黑 Regular，14\~18pt。
  * 标签/辅助说明：微软雅黑 Regular，12pt，浅灰色。

* **图标风格**

  * 扁平化图标，统一蓝色或灰色主色调，交互图标带颜色强调。

* **布局风格**

  * 信息展示为分组卡片式布局，内容可滚动浏览。
  * 顶部信息居中展示，底部快捷沟通按钮横向并列。

---

### 二、名片详情元素描述（针对通讯录场景）

| 区域        | 具体内容               |
| --------- | ------------------ |
| 页面标题栏     | “联系人名片” + 返回按钮     |
| 用户信息区     | 头像 / 姓名（或企业名） / 职位 |
| 联系方式区     | 电话、邮箱、地址（可点击）      |
| 附加信息区（可选） | 公司介绍、备注标签、保存时间     |
| 快捷沟通按钮区   | 拨打电话、发消息、复制名片      |
| 下拉提示      | “向下滑动查看更多信息…”      |

---

### 三、内容描述（页面模块）

| 模块       | 内容描述                 | 展示形式           |
| -------- | -------------------- | -------------- |
| 顶部信息区    | 头像 / 姓名 / 职务 / 公司    | 居中展示卡片式信息      |
| 联系信息     | 电话（☎️）、邮箱（📧）、地址（📍） | 横向 icon+文字点击操作 |
| 公司信息（可选） | 简短介绍 / 备注 / 标签       | 简洁段落文字         |
| 快捷按钮区    | 拨打电话、发微信消息、复制名片      | 底部横向布局圆角按钮组    |

---

### 四、布局描述（标准通讯录卡片查看布局）

```
顶部导航栏（顶部留白约5%）
[返回按钮]   联系人名片

（上下留白约3%）

[头像 + 姓名 + 职位]（居中卡片）
[头像]
姓名（加粗）  
职位（次级字体）
公司名称

（上下留白约5%）

[联系方式区域]
☎️ 电话（点击拨打）
📧 邮箱（点击复制或发邮件）
📍 地址（跳转地图）

（上下留白约3%）

[公司介绍/备注]（可选卡片）
简要介绍、附加备注、标签

（上下留白约5%）

[快捷操作按钮区]
[拨打电话]  [发消息]  [复制名片]（横向排列，统一样式）

底部适当留白（约10%）
```

---

### 🎯 页面目标

* 快速查看并理解联系人信息。
* 提供直接沟通手段：一键拨打、一键发消息。
* 支持进一步挖掘背景信息（如备注、公司介绍）。

---


## 👀 第 11 个页面：访客记录页面（Visitor Log Page）

> 该页面用于展示谁查看了用户的名片，提升用户的参与感、数据反馈与交互主动性。

---

### 一、统一风格描述（数据列表 + 时间线感）

* **整体色调**

  * 主色调：品牌蓝色，背景为白色或浅灰色。
  * 强调互动或重要记录条目时可使用淡橙或灰蓝色卡片高亮。

* **字体规范**

  * 访客姓名：微软雅黑 Bold，18\~20pt。
  * 次级信息（公司、职位）：微软雅黑 Regular，14\~16pt。
  * 时间戳：微软雅黑 Regular，12pt，浅灰色。

* **图标风格**

  * 访客头像：圆形头像。
  * 图标统一蓝色/灰色，关键操作按钮可用橙色突出。

* **布局风格**

  * 信息以卡片列表展示，每个访客一个卡片。
  * 支持垂直滚动，必要时可以加入时间线锚点感。

---

### 二、页面主要元素描述

| 区域     | 内容说明                     |
| ------ | ------------------------ |
| 页面标题栏  | 标题：“访客记录”，左侧返回按钮         |
| 总访客统计区 | 简要文字提示：“最近有 XX 人查看了你的名片” |
| 访客记录列表 | 每条记录包含头像、姓名、公司、职位、查看时间   |
| 空状态提示  | “还没有人访问你的名片哦，快去分享吧\~”    |

---

### 三、内容描述（模块明细）

| 模块名称     | 内容             | 展示形式         |
| -------- | -------------- | ------------ |
| 页面标题栏    | 标题、返回按钮        | 顶部横向栏        |
| 访客总览区    | 文字描述统计访客数量     | 居中横条文字区      |
| 访客记录卡片列表 | 头像、姓名、公司、职位、时间 | 垂直滚动卡片列表     |
| 空记录提示区   | 无访客时提示文字与图标    | 居中展示，温暖人性化提示 |

---

### 四、布局描述（时间感列表布局）

```
顶部导航栏（顶部留白约5%）
[返回按钮]      访客记录

（上下留白约3%）

访客总览区
“最近有 12 人查看了你的名片”

（上下留白约3%）

访客记录列表（垂直滚动卡片布局）
-------------------------------
| [头像]  姓名（加粗）        |
|        公司 - 职位         |
|        🕒 2025-06-02 15:12 |
-------------------------------
| [头像]  姓名（加粗）        |
|        公司 - 职位         |
|        🕒 2025-06-01 20:43 |
-------------------------------
...

无记录时展示提示：
📭 暂无访客记录
“还没有人访问你的名片哦，快去分享吧~”

底部留白（约10%）
```

---

### 五、扩展功能建议（可选）

* 搜索 / 筛选功能（按时间段、企业名、是否重复访客等）
* 点击访客头像跳转其名片详情
* 区分“首次访问”和“回访用户”的标识标签

---

### 🎯 页面目标

* 提供访客记录的可视化反馈，让用户了解谁在关注自己。
* 鼓励用户进一步分享名片，形成社交裂变。
* 引导后续沟通行为（例如主动联系访客）。

---


## 💬 第 12 个页面：即时聊天 / 在线咨询页（Chat / Consultation Page）

> 提供用户与名片持有者之间的实时沟通功能，可用于发起业务咨询或建立初步联系。

---

### 一、统一风格描述（简洁、流畅、高响应）

* **整体色调**

  * 聊天背景浅灰或白色。
  * 气泡颜色区分：发送方使用品牌蓝色，接收方使用浅灰或白色气泡，文字为深色。
  * 输入区域高亮边框，按钮采用蓝色或橙色强调。

* **字体规范**

  * 消息正文：微软雅黑 Regular，14\~16pt。
  * 时间戳：微软雅黑 Regular，12pt，浅灰色。
  * 输入框提示：微软雅黑 Regular，14pt，灰色。

* **图标风格**

  * 发送按钮：飞机图标样式。
  * 附件、语音、表情图标：简洁扁平风格，蓝色/灰色。

* **布局风格**

  * 类似微信聊天界面风格，消息气泡上下排列。
  * 底部固定输入区域，带发送按钮及功能图标。

---

### 二、页面主要元素描述

| 区域          | 内容说明                          |
| ----------- | ----------------------------- |
| 页面标题栏       | 聊天对象名称（如“张伟 - XX公司”）+ 返回按钮    |
| 聊天记录区       | 按时间顺序展示聊天内容，包括文字、时间戳、头像等      |
| 消息输入区       | 文本输入框 + 表情按钮 + 附件/语音图标 + 发送按钮 |
| 空状态提示（首次聊天） | “你好，请问有什么可以帮助您的？”             |

---

### 三、内容描述（模块明细）

| 模块名称   | 内容              | 展示形式        |
| ------ | --------------- | ----------- |
| 页面标题栏  | 名片持有者姓名、公司      | 顶部固定横向布局    |
| 聊天内容列表 | 对话气泡 + 时间戳 + 头像 | 垂直滚动列表      |
| 消息输入区域 | 输入框、发送按钮、表情图标等  | 底部固定        |
| 初始状态提示 | 引导用户开始交流        | 第一条系统提示居中显示 |

---

### 四、布局描述（类 IM 聊天界面标准布局）

```
顶部导航栏（顶部留白约5%）
[返回按钮]      张伟 - XX公司

（上下留白约3%）

聊天内容区域（可滚动）
---时间戳（居中小字）---
[头像] 🟦 “你好，请问您需要什么服务？”
                         🟩 “我想了解贵公司的合作模式。”
---时间戳（居中小字）---
...

（持续滚动）

页面底部（输入区域，固定悬浮）
--------------------------
😃 [输入消息…__________] 📎 🎤  ✈️
（左：表情/附件/语音，右：发送按钮）

底部留白约 10%（避免遮挡）
```

---

### 五、功能交互说明（建议支持）

* 支持：文字消息（必需）
* 建议支持：

  * 表情发送
  * 附件/图片发送
  * 语音消息
  * 系统自动欢迎语（首次对话时）

---

### 🎯 页面目标

* 提供即时沟通渠道，降低联系门槛。
* 模拟微信式对话体验，用户熟悉易上手。
* 有利于促成后续商务合作与交流。

---


## 🎁 第 13 个页面：积分商城首页（Points Mall Home Page）

> 展示当前可用积分与可兑换商品，鼓励用户参与裂变、互动、分享等行为赚取积分并使用积分兑换奖励。

---

### 一、统一风格描述（图文并茂 + 商城氛围）

* **整体色调**

  * 主色调：蓝色为主（表示系统可信、专业），商城商品区可使用橙色或红色点缀以增强“促销感”。
  * 背景：白色或浅灰，分隔内容区。

* **字体规范**

  * 用户积分显示：微软雅黑 Bold，24pt，加粗。
  * 商品标题：微软雅黑 Bold，16\~18pt。
  * 商品描述与价格：微软雅黑 Regular，14pt。
  * 小提示文字：微软雅黑 Regular，12pt，浅灰色。

* **图标风格**

  * 商品图为主，配合统一风格图标（例如兑换按钮、积分标识等）。
  * 图标颜色与按钮统一蓝色主调。

* **布局风格**

  * 上半部分展示“我的积分”
  * 下半部分为商品列表（网格/卡片式）
  * 分类筛选与搜索区域清晰明确

---

### 二、页面主要元素描述

| 区域      | 内容说明                        |
| ------- | --------------------------- |
| 页面标题栏   | 标题：“积分商城”，左侧返回按钮            |
| 我的积分区域  | 当前可用积分数值（显著居中显示）            |
| 筛选与分类栏  | 商品分类标签、搜索框（如“全部”、“实物”、“虚拟”） |
| 商品展示区   | 商品图片、标题、所需积分、兑换按钮           |
| 商品卡片说明区 | 限时标签、剩余库存提示、已兑换人数等（可选）      |

---

### 三、内容描述（模块明细）

| 模块名称    | 内容                     | 展示形式           |
| ------- | ---------------------- | -------------- |
| 我的积分展示区 | “当前积分：320”             | 大字体，居中醒目展示     |
| 分类与筛选   | 标签组：“全部”“实物”“虚拟”+ 搜索图标 | 横向滑动标签组或搜索框    |
| 商品卡片区域  | 商品图、商品名、所需积分、兑换按钮      | 网格布局（2列或1列卡片式） |
| 商品附加说明  | 如“限量”“剩余5份”“兑换人数100+”  | 小字标注或标签展示      |

---

### 四、布局描述（商城视觉型结构）

```
顶部导航栏（顶部留白约5%）
[返回按钮]      积分商城

（上下留白约3%）

我的积分展示区（大字居中）
🎉 当前积分：320 分

（上下留白约3%）

分类筛选区（横向）
[全部] [实物] [虚拟] [热门]  🔍（搜索图标）

（上下留白约5%）

商品列表（网格卡片式布局）
----------------------------------
| 🛍️ 商品图片           |
| 商品名称：定制徽章     |
| 积分：120分           |
| [立即兑换按钮]        |
----------------------------------
| 🛍️ 商品图片           |
| 商品名称：专属头像框   |
| 积分：80分            |
| [立即兑换按钮]        |
----------------------------------
...

底部留白（约10%）
```

---

### 五、功能交互建议（支持/可扩展）

* 商品点击跳转至详情页
* 积分不足时按钮变灰
* 支持限时、限量、积分折扣活动标签
* 商品类型筛选
* 用户兑换历史入口（可放在顶部或个人中心）

---

### 🎯 页面目标

* 展示积分使用价值，激励用户参与平台活动赚取积分。
* 提供清晰、愉悦的购物体验，提升活跃度与粘性。
* 带有商城视觉感但保持信息清晰、便于操作。

---

## 🎟️ 第 14 个页面：积分兑换详情页（Points Redemption Detail Page）

> 展示商品的详细信息，包括图片、描述、所需积分、库存、兑换规则等，并提供兑换操作入口。

---

### 一、统一风格描述（承接积分商城，图文并茂 + 操作明确）

* **整体色调**

  * 主色调：蓝色（积分系统主色），背景白色。
  * 操作按钮与库存提示等使用橙色或红色强调。

* **字体规范**

  * 商品标题：微软雅黑 Bold，18\~20pt。
  * 商品价格（积分）：微软雅黑 Bold，16pt。
  * 商品描述、规则说明：微软雅黑 Regular，14pt。
  * 库存/限制信息：微软雅黑 Regular，12pt，灰色或红色。

* **图标风格**

  * 商品图片主导，按钮图标与提示图标使用统一扁平化风格。

* **布局风格**

  * 商品图片置顶、下方为详细信息
  * 页面底部为固定操作区域，展示“立即兑换”按钮与当前积分信息

---

### 二、页面主要元素描述

| 区域      | 内容说明                          |
| ------- | ----------------------------- |
| 页面标题栏   | 标题：“兑换详情”，左侧返回按钮              |
| 商品图片区域  | 大图展示（支持左右滑动多图），比例 1.5:1 或 1:1 |
| 商品基本信息区 | 商品标题、所需积分、库存、限购信息等            |
| 商品详细描述区 | 图文说明、使用方式、兑换须知                |
| 底部兑换操作区 | 当前积分显示 + 兑换按钮（不足积分时按钮变灰并禁用）   |

---

### 三、内容描述（模块明细）

| 模块名称    | 内容              | 展示形式                |
| ------- | --------------- | ------------------- |
| 商品图展示区  | 商品主图、轮播图（2\~3张） | 顶部大图，可滑动切换          |
| 商品标题区   | 商品名称、积分价格       | 加粗显示，字体大            |
| 商品状态说明区 | 库存数量、限购规则、已兑换数量 | 标签或红色文字小字提示         |
| 商品详细描述区 | 图文详情：介绍、功能、注意事项 | 多段文字 + 图片组合内容       |
| 兑换操作区   | 当前用户积分数 + 兑换按钮  | 底部固定栏，按钮颜色强调，积分字体醒目 |

---

### 四、布局描述（图文 + 操作一体式结构）

```
顶部导航栏（顶部留白约5%）
[返回按钮]        兑换详情

（上下留白约3%）

[商品图片区域]
（支持轮播图，可左右滑动）

（上下留白约5%）

[商品标题 + 所需积分]
商品名称（加粗）     积分：180分

[商品库存 / 限制]
剩余库存：12件   每人限兑：1次

（上下留白约3%）

[商品详细描述区]
—— 商品介绍 ——
- 此徽章为定制限量款
- 材质为合金 + 钛层
- 可用于 MEH 会员佩戴场景等

—— 兑换须知 ——
- 积分兑换后不可退换
- 实物商品将在7日内邮寄

（上下留白约10%）

底部兑换操作栏（固定）
当前积分：320分     [立即兑换]（蓝色按钮）

底部留白约5%
```

---

### 五、功能交互建议（建议支持）

* 图片轮播放大预览
* 点击库存提示弹窗说明（如：剩余库存、限兑规则）
* 若用户积分不足，禁用按钮并提示“积分不足，去赚积分”

---

### 🎯 页面目标

* 提供完整、清晰的商品兑换信息，降低决策成本。
* 加强用户兑换行为的引导（积分足够立即行动，积分不足引流到任务页）。
* 页面结构要兼顾美观、实用与易操作性。

---


## 🚀 第 15 个页面：名片裂变营销活动页（Referral & Fission Campaign Page）

> 用于激励用户通过分享名片参与裂变传播活动，从而获得积分奖励或解锁特殊权益。

---

### 一、统一风格描述（具有活动视觉风格 + 强调参与感）

* **整体色调**

  * 主色调：品牌蓝色 + 强调色橙红（用于按钮、进度、奖励）。
  * 背景建议使用活动氛围图或淡渐变背景（如蓝紫、橙红渐变）。

* **字体规范**

  * 活动标题：微软雅黑 Bold，22\~26pt，突出视觉冲击。
  * 奖励描述/提示：微软雅黑 Regular，14\~18pt。
  * 条件说明/进度：微软雅黑 Regular，12\~14pt，灰色或红色。

* **图标风格**

  * 活动主图（横幅或插画）、分享图标、任务图标等应图文结合，具有节日氛围感或卡通营销风格。

* **布局风格**

  * “顶部图 + 活动说明 + 裂变路径 + 奖励说明 + 操作按钮”结构。
  * 页面底部操作按钮固定。

---

### 二、页面主要元素描述

| 区域         | 内容说明                                 |
| ---------- | ------------------------------------ |
| 活动主图Banner | 活动视觉插画（例如“邀请好友赢积分”）                  |
| 活动标题与简介    | 简短介绍当前裂变活动内容（例如“成功邀请1人奖励50积分”）       |
| 裂变任务状态区    | 展示用户当前邀请数量 / 进度条（如已邀请 3/5 人），图标或卡片形式 |
| 奖励明细区      | 展示可获得的积分、等级徽章、兑换券等，每个奖励配图说明          |
| 分享按钮区域     | 提供“微信好友”“朋友圈”按钮，支持生成专属分享名片链接 + 二维码   |
| 活动规则说明     | 展示活动时间、限制、作弊处理等规则信息（可折叠）             |

---

### 三、内容描述（模块明细）

| 模块名称      | 内容                              | 展示形式            |
| --------- | ------------------------------- | --------------- |
| 活动Banner区 | 活动大图：“邀请好友 赢好礼”                 | 顶部大图横幅或插画       |
| 活动简介      | 文字说明：活动持续时间、奖励方式                | 横向卡片或段落说明       |
| 当前进度区     | 已邀请 3/5 人，带进度条或气泡图              | 卡片式任务进度卡，图文并列   |
| 奖励明细展示区   | 奖励：邀请1人得50积分，满5人得专属勋章等          | 卡片列表或图标标签展示     |
| 分享操作区域    | 按钮：“分享给好友” / “分享到朋友圈” / “生成二维码” | 底部按钮，图文 + 文案    |
| 活动规则      | 折叠展开内容，如：活动时间、不可作弊说明等           | 底部段落，默认折叠，点展开查看 |

---

### 四、布局描述（激励驱动营销风格结构）

```
顶部导航栏（顶部留白约5%）
[返回按钮]    裂变活动

（上下留白约3%）

[活动主图 Banner]
🎉 图示：「邀请好友 赢积分」

（上下留白约5%）

[活动简介]
邀请1位好友可得50积分
邀请5位可兑换“MEH荣誉徽章”

[当前进度]
🎯 已邀请：3/5 位好友
🔵🔵🔵⚪⚪（进度气泡图）

[奖励明细区]
🥇 奖励1：50积分
🥈 奖励2：专属徽章
🥉 奖励3：优先参加下次活动资格

[分享按钮区]
[🔗 分享名片]  [🧑‍🤝‍🧑 邀请好友]  [📷 生成专属二维码]

（上下留白约5%）

[活动规则说明（可折叠）]
活动时间：6月1日 - 6月30日  
说明：请勿使用刷量行为，系统将自动识别作弊账号并清除奖励

底部留白约10%
```

---

### 五、功能交互建议（建议支持）

* 点击“生成二维码”弹出用户专属分享名片码
* 自动追踪邀请成功记录（基于小程序 unionId 或 phone 绑定）
* 分享后可跳转积分商城 / 成就页引导用户使用积分

---

### 🎯 页面目标

* 激发用户分享名片、形成社交裂变增长。
* 引导用户行为与奖励挂钩，建立持续传播闭环。
* 提供简单但带仪式感的裂变体验，增加参与感与荣誉感。

---


## 🛠️ 第 16 个页面：后台管理首页（Admin Dashboard Page）

> 提供给 MEH 平台运营者使用的管理控制台，用于管理用户、名片内容、活动数据、访问监控等核心功能模块。

---

### 一、统一风格描述（B 端系统风格 + 信息密度 + 结构清晰）

* **整体色调**

  * 主色调：深蓝 + 浅灰，强调专业性和数据感。
  * 辅助色：蓝绿色、橙色用于图表、统计卡片高亮。

* **字体规范**

  * 模块标题：微软雅黑 Bold，18\~20pt。
  * 表格字段与图表标签：微软雅黑 Regular，14\~16pt。
  * 小提示与二级文字：微软雅黑 Regular，12pt，灰色。

* **图标风格**

  * 使用线性扁平图标，统一蓝灰色系。

* **布局风格**

  * 典型 B 端结构：左侧菜单导航 + 顶部栏 + 主内容区（卡片 + 图表 + 表格）

---

### 二、页面主要元素描述

| 区域      | 内容说明                            |
| ------- | ------------------------------- |
| 顶部导航栏   | 平台 Logo、管理员名称、切换语言、退出登录按钮       |
| 左侧导航栏   | 菜单模块：用户管理、名片管理、积分活动、访客统计、设置中心等  |
| 主内容统计区  | 今日新增用户、名片总数、总访问量、当前在线人数（信息卡片展示） |
| 数据图表区   | 折线图 / 柱状图展示过去7日访问量、裂变人数趋势       |
| 快捷操作按钮区 | 创建活动、审核名片、发通知（操作入口按钮）           |

---

### 三、内容描述（模块明细）

| 模块名称    | 内容                     | 展示形式          |
| ------- | ---------------------- | ------------- |
| 顶部栏     | Logo、账户信息、设置、退出登录      | 固定顶部导航        |
| 左侧导航栏   | 图标 + 菜单文本，点击切换模块       | 垂直导航栏（可折叠）    |
| 统计信息卡片区 | 用户总数、名片数、访问量等          | 横向 3\~4 张卡片展示 |
| 数据趋势图表区 | 折线图：每日访问趋势 / 柱状图：新增用户  | 宽屏数据可视化区块     |
| 快捷入口操作区 | 快速进入活动配置 / 审核 / 积分配置页面 | 圆角卡片或按钮       |

---

### 四、布局描述（典型信息管理后台结构）

```
[顶部导航栏]
Logo     管理员：海水      设置 ⚙️   退出 🔒

[左侧导航栏]
▸ 用户管理
▸ 名片管理
▸ 积分活动
▸ 访客分析
▸ 设置中心

[主内容区 - 横向4张信息卡片]
------------------------------
| 👤 今日新增用户     12     |
| 🧾 累计名片数       276    |
| 👁 总访问量        4,830   |
| 👨‍💻 在线用户数     23     |
------------------------------

[图表区]
📈 7日访问趋势图（折线图）
📊 裂变用户增长（柱状图）

[快捷操作按钮区]
[ + 创建活动 ]   [ 🧾 名片审核 ]   [ 📣 发送通知 ]

页面支持滚动，顶部固定，左栏折叠

底部留白约 5%
```

---

### 五、功能交互建议（B 端必备）

* 支持图表切换（按时间维度、模块筛选）
* 支持权限管理（部分入口仅限管理员可见）
* 快捷入口跳转到对应二级模块页（活动创建页、用户审核页等）

---

### 🎯 页面目标

* 快速了解平台运行数据和动态趋势。
* 便于管理员执行常见操作（如活动管理、数据审核）。
* 架构清晰，信息密集但不杂乱，适合日常使用与运营决策。

---