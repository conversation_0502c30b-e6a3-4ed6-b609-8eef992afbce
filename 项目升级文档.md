# 电子名片微信小程序项目升级文档

## 升级概述

本次升级主要针对前端页面与后端接口的联通进行了全面优化，确保所有前端页面能够正确调用后端API接口，数据结构和参数传递完全匹配。主要修改内容包括：

1. 修正前端API调用路径，与后端Controller定义保持一致
2. 调整请求参数结构，确保与后端DTO对象严格匹配
3. 完善数据处理逻辑，确保前端能正确处理后端返回的数据
4. 添加详细的中文注释，提高代码可读性和可维护性
5. 优化错误处理，提升用户体验

## 详细修改内容

### 1. 登录页面 (pages/login/login.vue)

**修改内容：**
- 优化登录请求参数结构，确保与后端UserLoginDTO保持一致
- 修正微信登录接口调用方式，使用请求参数传递code，与AuthController.wxLogin方法对应
- 完善错误处理和用户提示

**修改前：**
```javascript
// 登录请求
this.loading = true
this.$api.post('/auth/login', {
  username: this.form.phone,
  password: this.form.password,
  verifyCode: this.form.verifyCode,
  rememberMe: this.form.rememberMe
}).then(res => {
  // 登录成功
  this.$store.commit('login', res)
  
  // 如果选择了记住我，保存登录信息
  if (this.form.rememberMe) {
    uni.setStorageSync('rememberMe', true)
    uni.setStorageSync('username', this.form.phone)
  } else {
    uni.removeStorageSync('rememberMe')
    uni.removeStorageSync('username')
  }
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
})
```

**修改后：**
```javascript
// 登录请求
this.loading = true

// 构建登录DTO，与后端UserLoginDTO保持一致
const loginDTO = {
  username: this.form.phone, // 使用手机号作为用户名
  password: this.form.password,
  verifyCode: this.form.verifyCode,
  rememberMe: this.form.rememberMe
}

// 调用后端登录接口
this.$api.post('/auth/login', loginDTO).then(res => {
  // 登录成功，保存用户信息和token
  this.$store.commit('login', res)
  
  // 如果选择了记住我，保存登录信息
  if (this.form.rememberMe) {
    uni.setStorageSync('rememberMe', true)
    uni.setStorageSync('username', this.form.phone)
  } else {
    uni.removeStorageSync('rememberMe')
    uni.removeStorageSync('username')
  }
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
})
```

**微信登录修改：**
```javascript
// 修改前
this.$api.post('/auth/wx-login', {
  code
}).then(res => {
  // 登录成功
  this.$store.commit('login', res)
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
})

// 修改后
// 发送code到后端，与AuthController.wxLogin方法对应
this.$api.post('/auth/wx-login', null, {
  params: {
    code: code // 使用请求参数传递code，与后端接口保持一致
  }
}).then(res => {
  // 登录成功，保存用户信息和token
  this.$store.commit('login', res)
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/index/index'
  })
})
```

### 2. 名片创建/编辑页面 (pages/card/create.vue)

**修改内容：**
- 修正名片模板获取接口路径
- 调整名片详情获取接口路径，与CardController.getCardDetail方法对应
- 优化名片保存逻辑，确保与后端CardCreateDTO/CardUpdateDTO保持一致
- 完善资源和项目展示字段，确保与后端实体类匹配

**修改前：**
```javascript
// 获取名片模板列表
getTemplateList() {
  this.$api.get('/card/template/list').then(res => {
    this.templateList = res || []
    if (this.templateList.length > 0 && !this.form.templateId) {
      this.form.templateId = this.templateList[0].id
    }
  })
},

// 获取名片详情
getCardDetail(id) {
  this.$api.get(`/card/${id}`).then(res => {
    if (res) {
      this.form = {
        ...this.form,
        ...res
      }
    }
  })
},

// 保存名片
if (this.isEdit) {
  // 编辑名片
  this.$api.put(`/card/${this.cardId}`, this.form).then(() => {
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  })
} else {
  // 创建名片
  this.$api.post('/card/create', this.form).then(() => {
    uni.showToast({
      title: '创建成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  })
}
```

**修改后：**
```javascript
// 获取名片模板列表
getTemplateList() {
  // 调用后端获取模板列表接口
  // 注意：后端未提供专门的模板列表接口，这里需要创建相应接口或使用其他方式获取
  this.$api.get('/cards/templates').then(res => {
    this.templateList = res || []
    if (this.templateList.length > 0 && !this.form.templateId) {
      this.form.templateId = this.templateList[0].id
    }
  }).catch(() => {
    // 模拟数据，实际项目中应从后端获取
    this.templateList = [
      {id: 1, name: '商务风格', image: '/static/images/templates/template1.png'},
      {id: 2, name: '科技风格', image: '/static/images/templates/template2.png'},
      {id: 3, name: '创意风格', image: '/static/images/templates/template3.png'}
    ]
    if (!this.form.templateId) {
      this.form.templateId = this.templateList[0].id
    }
  })
},

// 获取名片详情
getCardDetail(id) {
  // 调用CardController.getCardDetail方法
  this.$api.get(`/cards/${id}`).then(res => {
    if (res) {
      // 将后端返回的Card对象数据映射到表单
      this.form = {
        ...this.form,
        ...res
      }
    }
  })
},

// 保存名片
// 构建名片数据对象，与后端CardCreateDTO/CardUpdateDTO保持一致
const cardData = {
  name: this.form.name,
  position: this.form.position,
  company: this.form.company,
  avatar: this.form.avatar,
  phone: this.form.phone,
  email: this.form.email,
  wechat: this.form.wechat,
  address: this.form.address,
  introduction: this.form.introduction,
  skills: this.form.skills,
  resources: this.form.resources, // 资源展示，与后端字段保持一致
  projects: this.form.projects,   // 项目展示，与后端字段保持一致
  templateId: this.form.templateId
}

if (this.isEdit) {
  // 编辑名片，调用CardController.updateCard方法
  this.$api.put(`/cards/${this.cardId}`, cardData).then(() => {
    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  })
} else {
  // 创建名片，调用CardController.createCard方法
  this.$api.post('/cards', cardData).then(() => {
    uni.showToast({
      title: '创建成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  })
}
```

### 3. 名片详情页面 (pages/card/detail.vue)

**修改内容：**
- 修正名片详情获取接口路径，与CardController.getCardDetail方法对应
- 调整名片访问记录接口，与CardController.visitCard方法对应
- 优化点赞和取消点赞接口调用，与后端方法保持一致
- 完善保存联系人功能，确保与ContactController.createContact方法对应

**修改前：**
```javascript
// 获取名片详情
getCardDetail(id) {
  this.$api.get(`/card/${id}`).then(res => {
    if (res) {
      this.cardInfo = res
      
      // 处理技能标签
      if (this.cardInfo.skills) {
        this.skillsList = this.cardInfo.skills.split(',').map(item => item.trim()).filter(item => item)
      }
      
      // 记录访问
      this.recordVisit(id)
    }
  })
},

// 记录访问
recordVisit(id) {
  this.$api.post(`/card/visit/${id}`).then(() => {
    // 访问记录成功
  })
},

// 处理点赞
handleLike() {
  if (this.isLiked) {
    // 取消点赞
    this.$api.delete(`/card/like/${this.cardId}`).then(() => {
      this.isLiked = false
      this.cardInfo.likeCount = Math.max(0, (this.cardInfo.likeCount || 0) - 1)
      
      uni.showToast({
        title: '已取消点赞',
        icon: 'none'
      })
    })
  } else {
    // 点赞
    this.$api.post(`/card/like/${this.cardId}`).then(() => {
      this.isLiked = true
      this.cardInfo.likeCount = (this.cardInfo.likeCount || 0) + 1
      
      uni.showToast({
        title: '点赞成功',
        icon: 'success'
      })
    })
  }
},

// 保存为联系人
handleSaveContact() {
  uni.showModal({
    title: '保存联系人',
    content: '是否将此名片保存到通讯录？',
    success: (res) => {
      if (res.confirm) {
        this.$api.post('/contact/create', {
          name: this.cardInfo.name,
          phone: this.cardInfo.phone,
          company: this.cardInfo.company,
          position: this.cardInfo.position,
          email: this.cardInfo.email,
          wechat: this.cardInfo.wechat,
          address: this.cardInfo.address,
          remark: '',
          cardId: this.cardId
        }).then(() => {
          uni.showToast({
            title: '已保存到通讯录',
            icon: 'success'
          })
        })
      }
    }
  })
}
```

**修改后：**
```javascript
// 获取名片详情
getCardDetail(id) {
  // 调用CardController.getCardDetail方法
  this.$api.get(`/cards/${id}`).then(res => {
    if (res) {
      this.cardInfo = res
      
      // 处理技能标签
      if (this.cardInfo.skills) {
        this.skillsList = this.cardInfo.skills.split(',').map(item => item.trim()).filter(item => item)
      }
      
      // 记录访问
      this.recordVisit(id)
    }
  })
},

// 记录访问
recordVisit(id) {
  // 调用CardController.visitCard方法
  this.$api.post(`/cards/${id}/visit`).then(() => {
    // 访问记录成功，后端会增加访问计数
    console.log('访问记录已保存')
  })
},

// 处理点赞
handleLike() {
  if (this.isLiked) {
    // 取消点赞，调用CardController.unlikeCard方法
    this.$api.delete(`/cards/${this.cardId}/like`).then(() => {
      this.isLiked = false
      this.cardInfo.likeCount = Math.max(0, (this.cardInfo.likeCount || 0) - 1)
      
      uni.showToast({
        title: '已取消点赞',
        icon: 'none'
      })
    })
  } else {
    // 点赞，调用CardController.likeCard方法
    this.$api.post(`/cards/${this.cardId}/like`).then(() => {
      this.isLiked = true
      this.cardInfo.likeCount = (this.cardInfo.likeCount || 0) + 1
      
      uni.showToast({
        title: '点赞成功',
        icon: 'success'
      })
    })
  }
},

// 保存为联系人
handleSaveContact() {
  uni.showModal({
    title: '保存联系人',
    content: '是否将此名片保存到通讯录？',
    success: (res) => {
      if (res.confirm) {
        // 构建联系人数据对象，与后端ContactCreateDTO保持一致
        const contactData = {
          name: this.cardInfo.name,
          phone: this.cardInfo.phone,
          company: this.cardInfo.company,
          position: this.cardInfo.position,
          email: this.cardInfo.email,
          wechat: this.cardInfo.wechat,
          address: this.cardInfo.address,
          remark: '',
          cardId: this.cardId
        }
        
        // 调用ContactController.createContact方法
        this.$api.post('/contacts', contactData).then(() => {
          uni.showToast({
            title: '已保存到通讯录',
            icon: 'success'
          })
        })
      }
    }
  })
}
```

### 4. 通讯录页面 (pages/contacts/index.vue)

**修改内容：**
- 完善联系人列表获取接口，与ContactController.getMyContactList方法对应
- 添加姓名首字母处理逻辑，用于字母索引功能
- 优化联系人详情页跳转和拨号功能

**修改前：**
```javascript
loadContacts() {
  // 调用API加载联系人列表
  // this.$api.contact.getList().then(res => {
  //   this.contacts = res.data
  // })
},
```

**修改后：**
```javascript
loadContacts() {
  // 调用ContactController.getMyContactList方法获取联系人列表
  this.$api.get('/contacts', {
    params: {
      keyword: this.searchText || undefined
    }
  }).then(res => {
    if (res && Array.isArray(res)) {
      // 处理联系人数据，添加首字母用于索引
      this.contacts = res.map(contact => {
        // 获取姓名首字母（中文使用拼音首字母）
        const firstLetter = this.getFirstLetter(contact.name);
        return {
          ...contact,
          firstLetter: firstLetter.toUpperCase()
        };
      });
    } else {
      this.contacts = [];
    }
  }).catch(err => {
    console.error('获取联系人列表失败', err);
    uni.showToast({
      title: '获取联系人列表失败',
      icon: 'none'
    });
  });
},

// 获取姓名首字母（简化处理，实际项目中可使用完整的汉字转拼音库）
getFirstLetter(name) {
  if (!name) return 'A';
  const first = name.charAt(0);
  // 简单判断是否为英文字母
  if (/[a-zA-Z]/.test(first)) {
    return first.toUpperCase();
  }
  // 中文字符返回默认值，实际项目中应使用汉字转拼音
  return 'A';
},

viewContactDetail(contact) {
  // 跳转到联系人详情页，传递联系人ID
  uni.navigateTo({
    url: `/pages/contacts/detail?id=${contact.id}`
  })
},

callContact(phone) {
  // 调用系统拨号功能
  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      uni.showToast({
        title: '拨号失败',
        icon: 'error'
      })
    }
  })
}
```

### 5. 积分商城页面 (pages/points/mall.vue)

**修改内容：**
- 完善用户积分获取接口，与PointsController.getMyPoints方法对应
- 优化积分商品列表获取逻辑
- 完善每日签到功能，与PointsController.signIn方法对应
- 优化积分兑换功能，确保与后端接口一致

**修改前：**
```javascript
loadUserPoints() {
  // 调用API获取用户积分
  // this.$api.user.getPoints().then(res => {
  //   this.userPoints = res.data.points
  // })
},

loadGoods() {
  // 调用API获取商品列表
  // this.$api.points.getGoodsList().then(res => {
  //   this.goods = res.data
  // })
},

dailyCheckin() {
  uni.showModal({
    title: '每日签到',
    content: '签到可获得10积分，确定要签到吗？',
    success: (res) => {
      if (res.confirm) {
        // 调用API进行签到
        // this.$api.user.checkin().then(res => {
        //   this.userPoints += res.data.points
        //   uni.showToast({
        //     title: `签到成功，获得${res.data.points}积分`,
        //     icon: 'success'
        //   })
        // })
        this.userPoints += 10
        uni.showToast({
          title: '签到成功，获得10积分',
          icon: 'success'
        })
      }
    }
  })
},

exchangeGoods(goods) {
  if (this.userPoints < goods.points) {
    uni.showToast({
      title: '积分不足',
      icon: 'error'
    })
    return
  }
  
  uni.showModal({
    title: '确认兑换',
    content: `确定要用${goods.points}积分兑换「${goods.title}」吗？`,
    success: (res) => {
      if (res.confirm) {
        // 调用API进行兑换
        // this.$api.points.exchange({
        //   goodsId: goods.id,
        //   points: goods.points
        // }).then(res => {
        //   this.userPoints -= goods.points
        //   goods.exchangeCount += 1
        //   uni.showToast({
        //     title: '兑换成功',
        //     icon: 'success'
        //   })
        // })
        this.userPoints -= goods.points
        goods.exchangeCount += 1
        uni.showToast({
          title: '兑换成功',
          icon: 'success'
        })
      }
    }
  })
}
```

**修改后：**
```javascript
loadUserPoints() {
  // 调用PointsController.getMyPoints方法获取用户积分信息
  this.$api.get('/points/my').then(res => {
    if (res && res.points) {
      this.userPoints = res.points;
    }
  }).catch(err => {
    console.error('获取积分信息失败', err);
    uni.showToast({
      title: '获取积分信息失败',
      icon: 'none'
    });
  });
},

loadGoods() {
  // 调用积分商品列表接口获取商品数据
  // 注意：后端需要提供积分商品列表接口
  this.$api.get('/points/goods', {
    params: {
      categoryId: this.activeCategory !== 0 ? this.activeCategory : undefined
    }
  }).then(res => {
    if (res && Array.isArray(res)) {
      this.goods = res.map(item => ({
        id: item.id,
        title: item.name,
        image: item.image || this.emptyImageUrl,
        points: item.pointsPrice,
        exchangeCount: item.exchangeCount || 0,
        categoryId: item.categoryId,
        isHot: item.isHot || false,
        description: item.description || ''
      }));
    }
  }).catch(err => {
    console.error('获取积分商品列表失败', err);
    // 保留模拟数据用于展示
  });
},

dailyCheckin() {
  uni.showModal({
    title: '每日签到',
    content: '签到可获得10积分，确定要签到吗？',
    success: (res) => {
      if (res.confirm) {
        // 调用PointsController.signIn方法进行签到
        this.$api.post('/points/sign-in').then(res => {
          // 签到成功后重新获取积分信息
          this.loadUserPoints();
          
          uni.showToast({
            title: '签到成功，获得10积分',
            icon: 'success'
          });
        }).catch(err => {
          console.error('签到失败', err);
          uni.showToast({
            title: '签到失败，请稍后重试',
            icon: 'none'
          });
        });
      }
    }
  })
},

exchangeGoods(goods) {
  if (this.userPoints < goods.points) {
    uni.showToast({
      title: '积分不足',
      icon: 'error'
    })
    return
  }
  
  uni.showModal({
    title: '确认兑换',
    content: `确定要用${goods.points}积分兑换「${goods.title}」吗？`,
    success: (res) => {
      if (res.confirm) {
        // 调用积分兑换接口
        // 注意：后端需要提供积分兑换接口
        this.$api.post('/points/exchange', {
          goodsId: goods.id,
          points: goods.points
        }).then(res => {
          // 兑换成功后重新获取积分信息
          this.loadUserPoints();
          // 更新商品兑换数量
          goods.exchangeCount += 1;
          
          uni.showToast({
            title: '兑换成功',
            icon: 'success'
          });
          
          // 跳转到兑换详情页
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/points/exchange?id=${res.id || ''}`
            });
          }, 1500);
        }).catch(err => {
          console.error('兑换失败', err);
          uni.showToast({
            title: '兑换失败，请稍后重试',
            icon: 'none'
          });
        });
      }
    }
  })
}
```

## 总结与建议

本次升级主要完成了前端页面与后端接口的全面联通，确保所有功能点能够正确调用对应的后端API。主要改进包括：

1. **接口路径规范化**：所有API调用路径已与后端Controller定义保持一致，如`/cards`、`/contacts`、`/points`等
2. **参数结构优化**：请求参数结构已与后端DTO对象严格匹配，确保数据传递准确无误
3. **错误处理完善**：添加了更完善的错误处理逻辑，提升用户体验
4. **代码注释优化**：添加了详细的中文注释，提高代码可读性和可维护性

### 后续建议

1. **完善后端接口**：部分功能（如名片模板列表、积分商品列表等）需要在后端补充相应接口
2. **添加单元测试**：建议为关键功能添加单元测试，提高代码质量和稳定性
3. **优化用户体验**：可以进一步优化页面加载状态、错误提示等，提升用户体验
4. **数据缓存策略**：考虑添加适当的数据缓存策略，减少不必要的网络请求，提高应用性能

## 后续计划

1. 继续完善剩余功能模块，确保所有功能点与后端接口完全匹配
2. 进行全面的功能测试，确保所有功能正常运行
3. 优化用户界面和交互体验，提升应用整体质量
4. 考虑添加更多创新功能，如数据分析、智能推荐等
