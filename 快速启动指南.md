# MEH名片小程序 - 快速启动指南

## 项目概述

MEH名片小程序是一个功能完整的电子名片管理系统，包含用户管理、名片创建、积分系统、活动管理等核心功能。

## 技术栈

### 后端技术栈
- **框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis-Plus
- **认证**: JWT + Spring Security
- **文档**: Swagger/OpenAPI
- **构建工具**: Maven

### 前端技术栈
- **框架**: uni-app
- **平台**: 微信小程序
- **UI组件**: uni-ui
- **状态管理**: Vuex
- **构建工具**: HBuilderX

## 环境要求

### 开发环境
- **JDK**: 1.8+
- **Node.js**: 14.0+
- **MySQL**: 8.0+
- **Maven**: 3.6+
- **HBuilderX**: 最新版本
- **微信开发者工具**: 最新版本

## 快速启动

### 1. 克隆项目
```bash
git clone https://github.com/yanhaishui/Meh-BusinessCard.git
cd Meh-BusinessCard
```

### 2. 后端启动

#### 2.1 数据库配置
```sql
-- 创建数据库
CREATE DATABASE meh_businesscard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据库结构
USE meh_businesscard;
SOURCE meh-backend/sql/init.sql;
```

#### 2.2 配置文件
编辑 `meh-backend/src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

jwt:
  secret: your_jwt_secret_key
  expiration: 86400

file:
  upload:
    path: /tmp/uploads
    domain: http://localhost:8080
```

#### 2.3 启动后端服务
```bash
cd meh-backend
mvn clean install
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

#### 2.4 访问API文档
启动后访问：`http://localhost:8080/swagger-ui.html`

### 3. 前端启动

#### 3.1 安装依赖
```bash
cd meh-frontend
npm install
```

#### 3.2 配置API地址
编辑 `meh-frontend/config/index.js`：

```javascript
export const config = {
  baseUrl: 'http://localhost:8080',
  version: '1.0.0',
  defaultAvatar: 'https://example.com/default-avatar.jpg'
}
```

#### 3.3 启动开发服务
使用HBuilderX打开 `meh-frontend` 目录，然后：
1. 点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
2. 或者使用命令行：
```bash
npm run dev:mp-weixin
```

## 核心功能测试

### 1. 用户注册登录
- 访问小程序首页
- 点击"登录/注册"
- 使用手机号注册新账户
- 完善个人信息

### 2. 创建名片
- 登录后进入"名片管理"
- 点击"创建名片"
- 选择模板并填写信息
- 保存并分享名片

### 3. 积分系统
- 进入"积分商城"
- 每日签到获取积分
- 浏览积分商品
- 使用积分兑换商品

### 4. 活动参与
- 访问"活动中心"
- 查看当前活动
- 参与活动获取奖励
- 邀请好友参与

## 管理后台

### 访问管理功能
管理员账户可以访问以下功能：
- 用户管理：`GET /admin/users`
- 系统统计：`GET /admin/stats`
- 积分操作：`POST /admin/users/{userId}/points`

### 创建管理员账户
```sql
-- 在数据库中创建管理员账户
INSERT INTO user (phone, password, nickname, role, status, create_time, update_time) 
VALUES ('13800138000', '$2a$10$encrypted_password', '管理员', 'ADMIN', 1, NOW(), NOW());
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接配置正确
- 检查防火墙设置

### 2. 前端无法连接后端
- 确认后端服务已启动
- 检查API地址配置
- 查看浏览器控制台错误信息

### 3. 微信小程序授权问题
- 确认小程序AppID配置正确
- 检查域名白名单设置
- 确认SSL证书配置

### 4. 文件上传失败
- 检查文件上传路径权限
- 确认文件大小限制
- 查看服务器磁盘空间

## 开发建议

### 1. 代码规范
- 遵循Java编码规范
- 使用统一的注释格式
- 保持代码整洁和可读性

### 2. 测试建议
- 编写单元测试
- 进行集成测试
- 测试异常情况处理

### 3. 性能优化
- 数据库查询优化
- 接口响应时间监控
- 前端资源压缩

### 4. 安全考虑
- 输入数据验证
- SQL注入防护
- XSS攻击防护

## 部署上线

### 1. 生产环境配置
```yaml
spring:
  profiles:
    active: prod
  datasource:
    url: ***********************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}

logging:
  level:
    com.meh.businesscard: INFO
  file:
    name: /var/log/meh-businesscard.log
```

### 2. 构建部署包
```bash
# 后端构建
cd meh-backend
mvn clean package -Pprod

# 前端构建
cd meh-frontend
npm run build:mp-weixin
```

### 3. 服务器部署
```bash
# 上传jar包到服务器
scp target/meh-businesscard-1.0.0.jar user@server:/opt/app/

# 启动服务
java -jar -Dspring.profiles.active=prod meh-businesscard-1.0.0.jar
```

### 4. 微信小程序发布
1. 使用微信开发者工具打开构建后的代码
2. 点击"上传"提交代码审核
3. 在微信公众平台提交审核
4. 审核通过后发布上线

## 技术支持

如有问题，请参考：
- 项目文档：`README.md`
- 功能总结：`新增功能总结.md`
- 开发文档：`项目开发总结.md`

或联系开发团队获取技术支持。
