# 微信小程序登录集成指南

## 功能概述

MEH名片小程序已经集成了完整的微信登录功能，支持：
- ✅ 微信授权登录（获取openid）
- ✅ 自动用户注册
- ✅ 新用户积分奖励
- ✅ 手机号授权绑定（框架已搭建）
- ✅ 用户信息自动同步

## 当前实现状态

### ✅ 已完成功能

#### 1. 后端实现
- **微信登录接口**：`POST /api/auth/wx-login`
  - 通过code获取openid和session_key
  - 自动注册新用户
  - 返回JWT token和用户信息
  - 新用户奖励100积分

- **手机号绑定接口**：`POST /api/auth/wx-bind-phone`
  - 接收加密数据进行解密（框架已搭建）
  - 绑定手机号到用户账户

#### 2. 前端实现
- **微信登录流程**：完整的登录交互
- **新用户引导**：自动提示获取手机号
- **错误处理**：完善的异常处理机制

### 🔧 需要配置的部分

#### 1. 微信小程序配置
在 `meh-backend/src/main/resources/application.yml` 中配置：

```yaml
# 微信小程序配置
wechat:
  appid: your_wechat_appid
  secret: your_wechat_secret
```

#### 2. 手机号解密实现
当前手机号绑定接口提供了框架，需要实现真正的解密逻辑：

```java
// 在 UserServiceImpl.bindWxPhone 方法中
// TODO: 实现微信手机号解密逻辑
// 参考微信官方文档：https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/signature.html
```

## 完整登录流程

### 1. 用户点击微信登录

```javascript
// 前端：pages/login/login.vue
handleWxLogin() {
  // 1. 调用微信登录API获取code
  uni.login({
    provider: 'weixin',
    success: (loginRes) => {
      const code = loginRes.code
      
      // 2. 发送code到后端
      this.$api.post('/auth/wx-login', null, {
        params: { code }
      }).then(res => {
        // 3. 保存登录信息
        this.$store.commit('login', res)
        
        // 4. 新用户引导
        if (res.isNewUser && !res.user.phone) {
          this.showPhoneAuthModal(res.sessionKey)
        } else {
          uni.switchTab({ url: '/pages/index/index' })
        }
      })
    }
  })
}
```

### 2. 后端处理登录

```java
// 后端：UserServiceImpl.wxLogin
public Result<?> wxLogin(String code) {
  // 1. 调用微信API获取openid
  String url = "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code";
  String resp = HttpUtil.get(url);
  JSONObject json = JSONUtil.parseObj(resp);
  String openid = json.getStr("openid");
  
  // 2. 查询或创建用户
  User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
    .eq(User::getOpenid, openid));
    
  if (user == null) {
    // 3. 自动注册新用户
    user = new User();
    user.setOpenid(openid);
    user.setNickname("微信用户");
    userMapper.insert(user);
    
    // 4. 初始化积分（100积分奖励）
    initUserPoints(user.getId());
  }
  
  // 5. 生成JWT token
  String token = generateToken(user);
  return Result.success(Map.of("token", token, "user", user, "isNewUser", isNewUser));
}
```

### 3. 手机号授权（可选）

```javascript
// 前端：手机号授权按钮
<button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">
  授权手机号
</button>

// 处理授权回调
onGetPhoneNumber(e) {
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const { encryptedData, iv } = e.detail
    
    // 发送到后端解密
    this.$api.post('/auth/wx-bind-phone', {
      encryptedData,
      iv,
      sessionKey: this.sessionKey
    }).then(res => {
      // 绑定成功
      uni.showToast({ title: '手机号绑定成功' })
    })
  }
}
```

## 数据库设计

### User表字段
```sql
CREATE TABLE `t_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_phone` (`phone`)
);
```

## 安全考虑

### 1. Token安全
- JWT token有效期设置合理（默认24小时）
- 支持token刷新机制
- 敏感操作需要重新验证

### 2. 数据加密
- 微信手机号数据采用AES加密
- session_key需要安全存储和传输
- 用户隐私数据保护

### 3. 接口安全
- 防止重复注册
- 限制登录频率
- 异常情况处理

## 测试指南

### 1. 开发环境测试
```bash
# 1. 启动后端服务
cd meh-backend
mvn spring-boot:run

# 2. 配置微信开发者工具
# - 设置AppID
# - 配置服务器域名
# - 开启调试模式

# 3. 测试登录流程
# - 点击微信登录
# - 查看控制台日志
# - 验证token生成
```

### 2. 接口测试
```bash
# 测试微信登录接口
curl -X POST "http://localhost:8080/api/auth/wx-login?code=test_code"

# 测试手机号绑定接口
curl -X POST "http://localhost:8080/api/auth/wx-bind-phone" \
  -H "Content-Type: application/json" \
  -d '{
    "encryptedData": "test_encrypted_data",
    "iv": "test_iv",
    "sessionKey": "test_session_key"
  }'
```

## 部署配置

### 1. 生产环境配置
```yaml
# application-prod.yml
wechat:
  appid: ${WECHAT_APPID}
  secret: ${WECHAT_SECRET}

# 环境变量
export WECHAT_APPID=your_production_appid
export WECHAT_SECRET=your_production_secret
```

### 2. 微信小程序后台配置
- 配置服务器域名白名单
- 设置业务域名
- 配置支付域名（如需要）

## 常见问题

### 1. code无效或过期
- 检查微信AppID和Secret配置
- 确认code只能使用一次
- 验证网络连接状态

### 2. 手机号解密失败
- 检查session_key是否正确
- 确认加密数据格式
- 验证解密算法实现

### 3. 用户重复注册
- 检查openid唯一性约束
- 确认数据库索引配置
- 验证查询逻辑

## 后续优化建议

### 1. 功能增强
- 实现真正的手机号解密
- 添加用户头像获取
- 支持微信昵称同步

### 2. 性能优化
- 缓存session_key
- 优化数据库查询
- 减少API调用次数

### 3. 用户体验
- 优化登录流程
- 添加加载动画
- 完善错误提示

## 总结

当前微信登录功能已经基本完成，主要特点：

✅ **完整的登录流程**：从前端授权到后端验证
✅ **自动用户管理**：新用户自动注册和积分奖励  
✅ **安全的认证机制**：JWT token和权限控制
✅ **扩展性设计**：支持手机号绑定和用户信息完善

只需要配置微信AppID和Secret，以及实现手机号解密逻辑，即可投入生产使用。
