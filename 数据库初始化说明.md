# MEH名片小程序数据库初始化说明

## 概述

本文档说明了MEH名片小程序数据库的完整初始化过程，包括表结构创建、基础数据插入和测试数据准备。

## 数据库信息

- **数据库名称**: `meh_businesscard`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **引擎**: `InnoDB`

## 表结构概览

### 核心业务表（15张）

| 表名 | 说明 | 记录数 |
|------|------|--------|
| `t_user` | 用户表 | 5条测试数据 |
| `t_user_points` | 用户积分表 | 5条测试数据 |
| `t_user_points_log` | 积分变动日志表 | 8条测试数据 |
| `t_card` | 名片表 | 5条测试数据 |
| `t_card_template` | 名片模板表 | 5条基础数据 |
| `t_card_visit_log` | 名片访问记录表 | 7条测试数据 |
| `t_card_like` | 名片点赞表 | 12条测试数据 |
| `t_card_comment` | 名片评论表 | 8条测试数据 |
| `t_contact` | 联系人表 | 4条测试数据 |
| `t_contact_group` | 联系人分组表 | 5条测试数据 |
| `t_activity` | 活动表 | 3条测试数据 |
| `t_invite` | 邀请记录表 | 5条测试数据 |
| `t_chat` | 聊天会话表 | 5条测试数据 |
| `t_chat_message` | 聊天消息表 | 10条测试数据 |
| `t_points_goods` | 积分商品表 | 5条基础数据 |

### 系统管理表（10张）

| 表名 | 说明 | 记录数 |
|------|------|--------|
| `t_admin` | 管理员表 | 1条基础数据 |
| `t_role` | 角色表 | 2条基础数据 |
| `t_permission` | 权限表 | 8条基础数据 |
| `t_role_permission` | 角色权限关联表 | 8条基础数据 |
| `t_config` | 系统配置表 | 9条基础数据 |
| `t_banner` | Banner表 | 3条基础数据 |
| `t_notice` | 公告表 | 3条基础数据 |
| `t_points_goods_category` | 积分商品分类表 | 4条基础数据 |
| `t_points_exchange` | 积分兑换记录表 | 3条测试数据 |
| `t_sys_operation_log` | 系统操作日志表 | 10条测试数据 |

## 初始化步骤

### 1. 执行初始化脚本

```bash
# 方式一：使用MySQL命令行
mysql -u root -p < meh-backend/sql/init.sql

# 方式二：登录MySQL后执行
mysql -u root -p
source /path/to/meh-backend/sql/init.sql;
```

### 2. 验证初始化结果

```sql
-- 检查数据库是否创建成功
SHOW DATABASES LIKE 'meh_businesscard';

-- 检查表是否创建成功
USE meh_businesscard;
SHOW TABLES;

-- 检查数据是否插入成功
SELECT 
    '数据库初始化完成' as status,
    (SELECT COUNT(*) FROM t_user) as user_count,
    (SELECT COUNT(*) FROM t_card) as card_count,
    (SELECT COUNT(*) FROM t_activity) as activity_count,
    (SELECT COUNT(*) FROM t_points_goods) as goods_count,
    (SELECT COUNT(*) FROM t_card_template) as template_count;
```

## 默认账户信息

### 管理员账户
- **用户名**: `admin`
- **密码**: `123456`
- **角色**: 超级管理员
- **权限**: 所有系统权限

### 测试用户账户
| 用户ID | 昵称 | 手机号 | 微信OpenID | 积分 | 等级 |
|--------|------|--------|------------|------|------|
| 1 | 张三 | *********** | wx_test_001 | 850 | 普通用户 |
| 2 | 李四 | *********** | wx_test_002 | 1200 | VIP用户 |
| 3 | 王五 | *********** | wx_test_003 | 650 | 普通用户 |
| 4 | 赵六 | *********** | wx_test_004 | 2100 | 企业用户 |
| 5 | 钱七 | *********** | wx_test_005 | 450 | 普通用户 |

## 核心功能数据

### 1. 名片模板
- 简约商务模板
- 科技创新模板  
- 时尚艺术模板
- 企业专业模板
- 个人创意模板

### 2. 积分商品
- 精美笔记本（500积分）
- 定制水杯（800积分）
- VIP会员月卡（1000积分）
- 名片设计服务（1500积分）
- 咖啡优惠券（300积分）

### 3. 活动配置
- 新春邀请有礼（进行中）
- 每日签到送积分（进行中）
- 名片设计大赛（未开始）

### 4. 系统配置
- 微信小程序配置
- 文件上传配置
- 积分规则配置
- 系统基础配置

## 数据关联关系

### 用户相关
```
t_user (用户) 
├── t_user_points (积分)
├── t_user_points_log (积分日志)
├── t_card (名片)
├── t_contact (联系人)
├── t_contact_group (联系人分组)
├── t_invite (邀请记录)
├── t_chat (聊天会话)
└── t_points_exchange (积分兑换)
```

### 名片相关
```
t_card (名片)
├── t_card_template (模板)
├── t_card_visit_log (访问记录)
├── t_card_like (点赞记录)
└── t_card_comment (评论记录)
```

### 积分相关
```
t_points_goods (商品)
├── t_points_goods_category (分类)
└── t_points_exchange (兑换记录)
```

## 索引优化

### 主要索引
- **唯一索引**: openid, phone, invite_code等
- **普通索引**: user_id, card_id, create_time等
- **复合索引**: (card_id, user_id), (from_user_id, to_user_id)等

### 性能优化建议
1. **查询优化**: 合理使用索引，避免全表扫描
2. **分页查询**: 使用LIMIT进行分页，避免大量数据查询
3. **定期维护**: 定期分析表结构，优化慢查询

## 数据备份建议

### 1. 定期备份
```bash
# 每日备份
mysqldump -u root -p meh_businesscard > backup_$(date +%Y%m%d).sql

# 压缩备份
mysqldump -u root -p meh_businesscard | gzip > backup_$(date +%Y%m%d).sql.gz
```

### 2. 增量备份
```bash
# 开启二进制日志
# 在my.cnf中添加：
# log-bin=mysql-bin
# server-id=1
```

## 环境配置

### 开发环境
- 包含完整测试数据
- 开启详细日志记录
- 支持数据重置

### 生产环境
- 仅包含基础配置数据
- 关闭测试数据插入
- 加强安全配置

## 常见问题

### 1. 字符编码问题
确保MySQL配置使用utf8mb4字符集：
```sql
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;
```

### 2. 权限问题
确保MySQL用户有足够权限：
```sql
GRANT ALL PRIVILEGES ON meh_businesscard.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 外键约束
当前设计未使用外键约束，通过应用层保证数据一致性。

## 更新日志

### v2.0 (2025-01-20)
- ✅ 优化表结构，添加缺失字段
- ✅ 完善索引设计，提升查询性能
- ✅ 增加完整测试数据
- ✅ 优化数据关联关系
- ✅ 添加系统配置数据

### v1.0 (2025-01-15)
- ✅ 创建基础表结构
- ✅ 添加基础数据
- ✅ 实现核心功能支持

## 总结

本数据库初始化脚本提供了MEH名片小程序的完整数据基础，包括：

- **25张数据表**：覆盖所有业务功能
- **完整测试数据**：支持功能测试和演示
- **优化的索引设计**：保证查询性能
- **规范的数据结构**：便于维护和扩展

执行初始化脚本后，系统即可正常运行，支持用户注册、名片管理、积分系统、活动管理等全部功能。
