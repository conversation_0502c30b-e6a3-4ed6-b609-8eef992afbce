2025-06-11 21:00:44.158  INFO 65800 --- [main] c.m.b.BusinessCardApplication            : Starting BusinessCardApplication using Java 17.0.15 on Mac with PID 65800 (/Users/<USER>/IdeaProjects/jhipster/MehBusinessCard/meh-backend/target/classes started by ya<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/jhipster/MehBusinessCard/meh-backend)
2025-06-11 21:00:44.161 DEBUG 65800 --- [main] c.m.b.BusinessCardApplication            : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-11 21:00:44.162  INFO 65800 --- [main] c.m.b.BusinessCardApplication            : No active profile set, falling back to 1 default profile: "default"
2025-06-11 21:00:44.862  INFO 65800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 21:00:44.864  INFO 65800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 21:00:44.875  INFO 65800 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-06-11 21:00:45.349  INFO 65800 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-06-11 21:00:45.353  INFO 65800 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-11 21:00:45.353  INFO 65800 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-11 21:00:45.394  INFO 65800 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-11 21:00:45.394  INFO 65800 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1213 ms
2025-06-11 21:00:45.599 DEBUG 65800 --- [main] c.m.b.c.s.JwtAuthenticationFilter        : Filter 'jwtAuthenticationFilter' configured for use
2025-06-11 21:00:46.057  INFO 65800 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@13662609, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27a0e6ce, org.springframework.security.web.context.SecurityContextPersistenceFilter@1b465fa9, org.springframework.security.web.header.HeaderWriterFilter@3eb0fd88, org.springframework.security.web.authentication.logout.LogoutFilter@5d9d8e46, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@611a2d82, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6774f264, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2b80497f, org.springframework.security.web.session.SessionManagementFilter@7538cfe6, org.springframework.security.web.access.ExceptionTranslationFilter@6ab50d1c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@71eff6a3]
2025-06-11 21:00:46.320  INFO 65800 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path '/api'
2025-06-11 21:00:46.424  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.429  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.449  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.495  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: file, dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.495  WARN 65800 --- [main] s.d.b.FormParameterSpecificationProvider : Parameter should either be a simple or a content type
2025-06-11 21:00:46.496  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: file, dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.496  WARN 65800 --- [main] s.d.b.FormParameterSpecificationProvider : Parameter should either be a simple or a content type
2025-06-11 21:00:46.497  WARN 65800 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: file, dataTypeClass: class java.lang.Void
2025-06-11 21:00:46.497  WARN 65800 --- [main] s.d.b.FormParameterSpecificationProvider : Parameter should either be a simple or a content type
2025-06-11 21:00:46.590  INFO 65800 --- [main] c.m.b.BusinessCardApplication            : Started BusinessCardApplication in 2.634 seconds (JVM running for 2.844)
