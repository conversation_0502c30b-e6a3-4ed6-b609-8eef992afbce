2025-06-04 20:48:33.158  INFO 6570 --- [main] c.m.b.BusinessCardApplication            : Starting BusinessCardApplication using Java 17.0.15 on Mac with PID 6570 (/Users/<USER>/IdeaProjects/jhipster/MehBusinessCard/meh-backend/target/classes started by ya<PERSON><PERSON><PERSON> in /Users/<USER>/IdeaProjects/jhipster/MehBusinessCard/meh-backend)
2025-06-04 20:48:33.161 DEBUG 6570 --- [main] c.m.b.BusinessCardApplication            : Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-04 20:48:33.161  INFO 6570 --- [main] c.m.b.BusinessCardApplication            : No active profile set, falling back to 1 default profile: "default"
2025-06-04 20:48:33.498  INFO 6570 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 20:48:33.499  INFO 6570 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 20:48:33.510  INFO 6570 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-06-04 20:48:33.879  INFO 6570 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-06-04 20:48:33.882  INFO 6570 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-04 20:48:33.883  INFO 6570 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-04 20:48:33.922  INFO 6570 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/api]    : Initializing Spring embedded WebApplicationContext
2025-06-04 20:48:33.922  INFO 6570 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 741 ms
2025-06-04 20:48:34.081 DEBUG 6570 --- [main] c.m.b.c.s.JwtAuthenticationFilter        : Filter 'jwtAuthenticationFilter' configured for use
2025-06-04 20:48:34.481  INFO 6570 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@41bdaa81, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@799971ac, org.springframework.security.web.context.SecurityContextPersistenceFilter@3df04fa1, org.springframework.security.web.header.HeaderWriterFilter@2ec92631, org.springframework.security.web.authentication.logout.LogoutFilter@48d31d25, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6b0ba697, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a531637, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@59c08cf1, org.springframework.security.web.session.SessionManagementFilter@45849604, org.springframework.security.web.access.ExceptionTranslationFilter@22a63740, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@49b9ba6a]
2025-06-04 20:48:34.750  INFO 6570 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path '/api'
2025-06-04 20:48:34.810  WARN 6570 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-04 20:48:34.817  WARN 6570 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-04 20:48:34.899  WARN 6570 --- [main] d.s.r.o.OperationImplicitParameterReader : Unable to interpret the implicit parameter configuration with dataType: , dataTypeClass: class java.lang.Void
2025-06-04 20:48:34.970  INFO 6570 --- [main] c.m.b.BusinessCardApplication            : Started BusinessCardApplication in 1.989 seconds (JVM running for 2.124)
