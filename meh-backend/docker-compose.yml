version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: meh-businesscard-app
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - SPRING_DATASOURCE_URL=************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=password
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - meh-network

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: meh-businesscard-mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=meh_businesscard
      - MYSQL_USER=meh
      - MYSQL_PASSWORD=password
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - meh-network

  # Redis 缓存服务
  redis:
    image: redis:6.2
    container_name: meh-businesscard-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - meh-network

# 持久化数据卷
volumes:
  mysql-data:
  redis-data:

# 网络配置
networks:
  meh-network:
    driver: bridge
