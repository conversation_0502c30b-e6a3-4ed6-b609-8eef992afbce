-- 创建数据库
CREATE DATABASE IF NOT EXISTS meh_businesscard DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE meh_businesscard;

-- 用户表
CREATE TABLE IF NOT EXISTS `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) DEFAULT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(64) DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `real_name` varchar(32) DEFAULT NULL COMMENT '真实姓名',
  `level` tinyint(1) DEFAULT 0 COMMENT '会员等级：0-普通用户，1-VIP用户，2-企业用户',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录IP',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_openid` (`openid`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户积分表
CREATE TABLE IF NOT EXISTS `t_user_points` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `points` int(11) DEFAULT 0 COMMENT '当前积分',
  `total_points` int(11) DEFAULT 0 COMMENT '累计获得积分',
  `used_points` int(11) DEFAULT 0 COMMENT '累计消费积分',
  `continuous_login_days` int(11) DEFAULT 0 COMMENT '连续登录天数',
  `last_sign_date` datetime DEFAULT NULL COMMENT '最后签到日期',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 用户积分变动日志表
CREATE TABLE IF NOT EXISTS `t_user_points_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '变动类型：1-增加，2-减少',
  `points` int(11) NOT NULL COMMENT '变动积分',
  `before_points` int(11) NOT NULL COMMENT '变动前积分',
  `after_points` int(11) NOT NULL COMMENT '变动后积分',
  `source` tinyint(2) NOT NULL COMMENT '变动来源：1-每日登录，2-签到，3-完善信息，4-社交互动，5-邀请好友，6-活动参与，7-积分兑换，8-管理员操作',
  `description` varchar(255) DEFAULT NULL COMMENT '变动描述',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID（管理员操作时记录）',
  `operator_name` varchar(64) DEFAULT NULL COMMENT '操作人姓名（管理员操作时记录）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分变动日志表';

-- 名片表
CREATE TABLE IF NOT EXISTS `t_card` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '名片ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) DEFAULT 1 COMMENT '名片类型：1-个人名片，2-企业名片',
  `name` varchar(64) NOT NULL COMMENT '姓名',
  `position` varchar(64) DEFAULT NULL COMMENT '职位',
  `company` varchar(128) DEFAULT NULL COMMENT '公司',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `introduction` text DEFAULT NULL COMMENT '个人简介',
  `skills` text DEFAULT NULL COMMENT '专业技能，JSON格式存储',
  `resources` text DEFAULT NULL COMMENT '个人资源，JSON格式存储',
  `projects` text DEFAULT NULL COMMENT '项目经历，JSON格式存储',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板ID',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '名片二维码',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认名片：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片表';

-- 名片模板表
CREATE TABLE IF NOT EXISTS `t_card_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(64) NOT NULL COMMENT '模板名称',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '模板缩略图',
  `content` text DEFAULT NULL COMMENT '模板HTML内容',
  `style` text DEFAULT NULL COMMENT '模板CSS样式',
  `type` tinyint(1) DEFAULT 3 COMMENT '模板类型：1-个人模板，2-企业模板，3-通用模板',
  `status` tinyint(1) DEFAULT 1 COMMENT '模板状态：0-禁用，1-启用',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片模板表';

-- 名片访问记录表
CREATE TABLE IF NOT EXISTS `t_card_visit_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `visitor_id` bigint(20) DEFAULT NULL COMMENT '访问者用户ID（已登录用户）',
  `visitor_ip` varchar(64) DEFAULT NULL COMMENT '访问者IP',
  `visitor_device` varchar(255) DEFAULT NULL COMMENT '访问者设备信息',
  `source` tinyint(1) DEFAULT 1 COMMENT '访问来源：1-小程序，2-分享链接，3-扫码',
  `stay_time` int(11) DEFAULT 0 COMMENT '停留时长（秒）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片访问记录表';

-- 名片点赞表
CREATE TABLE IF NOT EXISTS `t_card_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `user_id` bigint(20) NOT NULL COMMENT '点赞用户ID',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_card_user` (`card_id`, `user_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片点赞表';

-- 名片评论表
CREATE TABLE IF NOT EXISTS `t_card_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `user_id` bigint(20) NOT NULL COMMENT '评论用户ID',
  `content` varchar(500) NOT NULL COMMENT '评论内容',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID（回复评论时使用）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-待审核，1-已通过，2-已拒绝',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片评论表';

-- 联系人表
CREATE TABLE IF NOT EXISTS `t_contact` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（联系人所属用户）',
  `card_id` bigint(20) DEFAULT NULL COMMENT '关联的名片ID',
  `name` varchar(64) NOT NULL COMMENT '联系人姓名',
  `position` varchar(64) DEFAULT NULL COMMENT '联系人职位',
  `company` varchar(128) DEFAULT NULL COMMENT '联系人公司',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `email` varchar(64) DEFAULT NULL COMMENT '联系人邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '联系人地址',
  `avatar` varchar(255) DEFAULT NULL COMMENT '联系人头像',
  `remark` varchar(255) DEFAULT NULL COMMENT '联系人备注',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组ID',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签，JSON格式存储',
  `is_starred` tinyint(1) DEFAULT 0 COMMENT '是否星标联系人：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人表';

-- 联系人分组表
CREATE TABLE IF NOT EXISTS `t_contact_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `name` varchar(64) NOT NULL COMMENT '分组名称',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人分组表';

-- 积分商品表
CREATE TABLE IF NOT EXISTS `t_points_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(128) NOT NULL COMMENT '商品名称',
  `image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `description` text DEFAULT NULL COMMENT '商品描述',
  `points` int(11) NOT NULL COMMENT '所需积分',
  `type` tinyint(1) DEFAULT 1 COMMENT '商品类型：1-实物商品，2-虚拟商品',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品分类ID',
  `stock` int(11) DEFAULT 0 COMMENT '库存数量',
  `exchange_count` int(11) DEFAULT 0 COMMENT '已兑换数量',
  `limit_per_person` int(11) DEFAULT 0 COMMENT '每人限兑数量（0表示不限制）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-下架，1-上架',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品表';

-- 积分商品分类表
CREATE TABLE IF NOT EXISTS `t_points_goods_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(64) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品分类表';

-- 积分兑换记录表
CREATE TABLE IF NOT EXISTS `t_points_exchange` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(128) NOT NULL COMMENT '商品名称',
  `goods_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `quantity` int(11) DEFAULT 1 COMMENT '兑换数量',
  `points` int(11) NOT NULL COMMENT '消耗积分',
  `receiver_name` varchar(64) DEFAULT NULL COMMENT '收货人姓名（实物商品）',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人电话（实物商品）',
  `receiver_address` varchar(255) DEFAULT NULL COMMENT '收货地址（实物商品）',
  `status` tinyint(1) DEFAULT 0 COMMENT '兑换状态：0-待处理，1-已发货，2-已完成，3-已取消',
  `tracking_number` varchar(64) DEFAULT NULL COMMENT '物流单号（实物商品）',
  `express_company` varchar(64) DEFAULT NULL COMMENT '物流公司（实物商品）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分兑换记录表';

-- 活动表
CREATE TABLE IF NOT EXISTS `t_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(128) NOT NULL COMMENT '活动名称',
  `description` text DEFAULT NULL COMMENT '活动描述',
  `image` varchar(255) DEFAULT NULL COMMENT '活动图片',
  `type` tinyint(1) DEFAULT 1 COMMENT '活动类型：1-裂变营销，2-签到活动，3-其他活动',
  `rules` text DEFAULT NULL COMMENT '活动规则',
  `rewards` text DEFAULT NULL COMMENT '活动奖励，JSON格式存储',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '活动状态：0-未开始，1-进行中，2-已结束',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- 邀请记录表
CREATE TABLE IF NOT EXISTS `t_invite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人用户ID',
  `invitee_id` bigint(20) DEFAULT NULL COMMENT '被邀请人用户ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '关联活动ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '邀请状态：0-未接受，1-已接受',
  `reward_status` tinyint(1) DEFAULT 0 COMMENT '奖励状态：0-未发放，1-已发放',
  `reward_points` int(11) DEFAULT 0 COMMENT '奖励积分',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_invite_code` (`invite_code`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitee_id` (`invitee_id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请记录表';

-- 聊天会话表
CREATE TABLE IF NOT EXISTS `t_chat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '发起用户ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收用户ID',
  `last_message` varchar(500) DEFAULT NULL COMMENT '最后一条消息内容',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后一条消息时间',
  `unread_count` int(11) DEFAULT 0 COMMENT '未读消息数（接收方）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_pair` (`from_user_id`, `to_user_id`),
  KEY `idx_to_user_id` (`to_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `t_chat_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `chat_id` bigint(20) NOT NULL COMMENT '会话ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '发送者用户ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收者用户ID',
  `content` text DEFAULT NULL COMMENT '消息内容',
  `type` tinyint(1) DEFAULT 1 COMMENT '消息类型：1-文本，2-图片，3-语音，4-视频，5-文件',
  `media_url` varchar(255) DEFAULT NULL COMMENT '媒体文件URL（图片/语音/视频/文件）',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_chat_id` (`chat_id`),
  KEY `idx_from_user_id` (`from_user_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';

-- 管理员表
CREATE TABLE IF NOT EXISTS `t_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(64) NOT NULL COMMENT '用户名',
  `password` varchar(128) NOT NULL COMMENT '密码',
  `real_name` varchar(64) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录IP',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 角色表
CREATE TABLE IF NOT EXISTS `t_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(64) NOT NULL COMMENT '角色名称',
  `code` varchar(64) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `t_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(64) NOT NULL COMMENT '权限名称',
  `code` varchar(64) NOT NULL COMMENT '权限编码',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `type` tinyint(1) DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径（菜单类型）',
  `icon` varchar(64) DEFAULT NULL COMMENT '图标（菜单类型）',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `t_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `t_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `name` varchar(64) NOT NULL COMMENT '配置名称',
  `key` varchar(64) NOT NULL COMMENT '配置键',
  `value` text DEFAULT NULL COMMENT '配置值',
  `type` tinyint(1) DEFAULT 1 COMMENT '配置类型：1-系统配置，2-业务配置',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- Banner表
CREATE TABLE IF NOT EXISTS `t_banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Banner ID',
  `title` varchar(128) NOT NULL COMMENT 'Banner标题',
  `image` varchar(255) NOT NULL COMMENT 'Banner图片',
  `link` varchar(255) DEFAULT NULL COMMENT '跳转链接',
  `type` tinyint(1) DEFAULT 1 COMMENT '链接类型：1-内部页面，2-外部链接',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Banner表';

-- 公告表
CREATE TABLE IF NOT EXISTS `t_notice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(128) NOT NULL COMMENT '公告标题',
  `content` text DEFAULT NULL COMMENT '公告内容',
  `type` tinyint(1) DEFAULT 1 COMMENT '公告类型：1-系统公告，2-活动公告',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_top` tinyint(1) DEFAULT 0 COMMENT '是否置顶：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_top` (`is_top`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';


-- 系统操作日志表
CREATE TABLE IF NOT EXISTS `t_sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(64) DEFAULT NULL COMMENT '操作用户名',
  `module` varchar(64) DEFAULT NULL COMMENT '操作模块',
  `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `method` varchar(64) DEFAULT NULL COMMENT '请求方法',
  `path` varchar(255) DEFAULT NULL COMMENT '请求路径',
  `ip` varchar(64) DEFAULT NULL COMMENT '请求IP',
  `params` text DEFAULT NULL COMMENT '请求参数',
  `result` text DEFAULT NULL COMMENT '请求结果',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行耗时(ms)',
  `status` tinyint(1) DEFAULT 1 COMMENT '操作状态：0-失败，1-成功',
  `error_msg` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_create_time` (`create_time`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';

-- 初始化管理员账号
INSERT INTO `t_admin` (`username`, `password`, `real_name`, `status`) VALUES ('admin', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '系统管理员', 1);

-- 初始化角色
INSERT INTO `t_role` (`name`, `code`, `description`, `status`) VALUES ('超级管理员', 'ROLE_ADMIN', '系统超级管理员，拥有所有权限', 1);

-- 初始化名片模板
INSERT INTO `t_card_template` (`name`, `type`, `status`, `sort`) VALUES ('简约商务模板', 3, 1, 1);
INSERT INTO `t_card_template` (`name`, `type`, `status`, `sort`) VALUES ('科技创新模板', 3, 1, 2);
INSERT INTO `t_card_template` (`name`, `type`, `status`, `sort`) VALUES ('时尚艺术模板', 3, 1, 3);


-- 系统操作日志测试数据
INSERT INTO `t_sys_operation_log` (`user_id`, `username`, `module`, `operation_type`, `description`, `method`, `path`, `ip`, `params`, `result`, `execution_time`, `status`, `create_time`) VALUES
 (1, 'admin', '用户认证', '登录', '用户登录系统', 'POST', '/auth/login', '127.0.0.1', '{"username":"admin","password":"******"}', '{"code":200,"message":"操作成功","data":{"token":"eyJhbGciOiJIUzUxMiJ9..."}}', 125, 1, '2025-06-01 10:00:00'),
 (1, 'admin', '名片管理', '新增', '创建名片', 'POST', '/cards', '127.0.0.1', '{"name":"张三","company":"码布斯科技","position":"产品经理"}', '{"code":200,"message":"操作成功"}', 89, 1, '2025-06-01 10:15:30'),
 (1, 'admin', '名片管理', '修改', '更新名片', 'PUT', '/cards/1', '127.0.0.1', '{"id":1,"name":"张三","company":"码布斯科技","position":"高级产品经理"}', '{"code":200,"message":"操作成功"}', 76, 1, '2025-06-01 14:22:15'),
 (2, 'user123', '在线咨询', '新增', '创建会话', 'POST', '/chats', '*************', '{"targetUserId":1}', '{"code":200,"message":"操作成功"}', 98, 1, '2025-06-02 09:30:45')
