-- =============================================
-- MEH名片小程序数据库初始化脚本
-- 版本: 2.0
-- 创建时间: 2025-06-01
-- 作者: yanhaishui
-- 说明: 根据最新实体类优化的完整数据库结构
-- =============================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS meh_businesscard DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE meh_businesscard;

-- 用户表
CREATE TABLE IF NOT EXISTS `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(64) DEFAULT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(64) DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
  `gender` tinyint(1) DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(128) DEFAULT NULL COMMENT '密码（加密存储）',
  `real_name` varchar(32) DEFAULT NULL COMMENT '真实姓名',
  `level` tinyint(1) DEFAULT 0 COMMENT '会员等级：0-普通用户，1-VIP用户，2-企业用户',
  `status` tinyint(1) DEFAULT 1 COMMENT '用户状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录IP',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户积分表
CREATE TABLE IF NOT EXISTS `t_user_points` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `points` int(11) DEFAULT 0 COMMENT '当前积分',
  `total_points` int(11) DEFAULT 0 COMMENT '累计获得积分',
  `used_points` int(11) DEFAULT 0 COMMENT '累计消费积分',
  `continuous_login_days` int(11) DEFAULT 0 COMMENT '连续登录天数',
  `last_sign_date` datetime DEFAULT NULL COMMENT '最后签到日期',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 用户积分变动日志表
CREATE TABLE IF NOT EXISTS `t_user_points_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) NOT NULL COMMENT '变动类型：1-增加，2-减少',
  `points` int(11) NOT NULL COMMENT '变动积分',
  `before_points` int(11) NOT NULL COMMENT '变动前积分',
  `after_points` int(11) NOT NULL COMMENT '变动后积分',
  `source` tinyint(2) NOT NULL COMMENT '变动来源：1-每日登录，2-签到，3-完善信息，4-社交互动，5-邀请好友，6-活动参与，7-积分兑换，8-管理员操作',
  `description` varchar(255) DEFAULT NULL COMMENT '变动描述',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID（管理员操作时记录）',
  `operator_name` varchar(64) DEFAULT NULL COMMENT '操作人姓名（管理员操作时记录）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分变动日志表';

-- 名片表
CREATE TABLE IF NOT EXISTS `t_card` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '名片ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` tinyint(1) DEFAULT 1 COMMENT '名片类型：1-个人名片，2-企业名片',
  `name` varchar(64) NOT NULL COMMENT '姓名',
  `position` varchar(64) DEFAULT NULL COMMENT '职位',
  `company` varchar(128) DEFAULT NULL COMMENT '公司',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `introduction` text DEFAULT NULL COMMENT '个人简介',
  `skills` text DEFAULT NULL COMMENT '专业技能，JSON格式存储',
  `resources` text DEFAULT NULL COMMENT '个人资源，JSON格式存储',
  `projects` text DEFAULT NULL COMMENT '项目经历，JSON格式存储',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板ID',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '名片二维码',
  `visit_count` int(11) DEFAULT 0 COMMENT '访问次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认名片：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片表';

-- 名片模板表
CREATE TABLE IF NOT EXISTS `t_card_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(64) NOT NULL COMMENT '模板名称',
  `thumbnail` varchar(255) DEFAULT NULL COMMENT '模板缩略图',
  `content` text DEFAULT NULL COMMENT '模板HTML内容',
  `style` text DEFAULT NULL COMMENT '模板CSS样式',
  `type` tinyint(1) DEFAULT 3 COMMENT '模板类型：1-个人模板，2-企业模板，3-通用模板',
  `status` tinyint(1) DEFAULT 1 COMMENT '模板状态：0-禁用，1-启用',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片模板表';

-- 名片访问记录表
CREATE TABLE IF NOT EXISTS `t_card_visit_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `visitor_id` bigint(20) DEFAULT NULL COMMENT '访问者用户ID（已登录用户）',
  `visitor_ip` varchar(64) DEFAULT NULL COMMENT '访问者IP',
  `visitor_device` varchar(255) DEFAULT NULL COMMENT '访问者设备信息',
  `source` tinyint(1) DEFAULT 1 COMMENT '访问来源：1-小程序，2-分享链接，3-扫码',
  `stay_time` int(11) DEFAULT 0 COMMENT '停留时长（秒）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_visitor_id` (`visitor_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片访问记录表';

-- 名片点赞表
CREATE TABLE IF NOT EXISTS `t_card_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `user_id` bigint(20) NOT NULL COMMENT '点赞用户ID',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_card_user` (`card_id`, `user_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片点赞表';

-- 名片评论表
CREATE TABLE IF NOT EXISTS `t_card_comment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `card_id` bigint(20) NOT NULL COMMENT '名片ID',
  `card_user_id` bigint(20) NOT NULL COMMENT '名片所属用户ID',
  `user_id` bigint(20) NOT NULL COMMENT '评论用户ID',
  `content` varchar(500) NOT NULL COMMENT '评论内容',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID（回复评论时使用）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-待审核，1-已通过，2-已拒绝',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_user_id` (`card_user_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片评论表';

-- 联系人表
CREATE TABLE IF NOT EXISTS `t_contact` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（联系人所属用户）',
  `card_id` bigint(20) DEFAULT NULL COMMENT '关联的名片ID',
  `name` varchar(64) NOT NULL COMMENT '联系人姓名',
  `position` varchar(64) DEFAULT NULL COMMENT '联系人职位',
  `company` varchar(128) DEFAULT NULL COMMENT '联系人公司',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `email` varchar(64) DEFAULT NULL COMMENT '联系人邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '联系人地址',
  `avatar` varchar(255) DEFAULT NULL COMMENT '联系人头像',
  `remark` varchar(255) DEFAULT NULL COMMENT '联系人备注',
  `group_id` bigint(20) DEFAULT NULL COMMENT '分组ID',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签，JSON格式存储',
  `is_starred` tinyint(1) DEFAULT 0 COMMENT '是否星标联系人：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人表';

-- 联系人分组表
CREATE TABLE IF NOT EXISTS `t_contact_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `name` varchar(64) NOT NULL COMMENT '分组名称',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人分组表';

-- 积分商品表
CREATE TABLE IF NOT EXISTS `t_points_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(128) NOT NULL COMMENT '商品名称',
  `image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `description` text DEFAULT NULL COMMENT '商品描述',
  `points` int(11) NOT NULL COMMENT '所需积分',
  `type` tinyint(1) DEFAULT 1 COMMENT '商品类型：1-实物商品，2-虚拟商品',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品分类ID',
  `stock` int(11) DEFAULT 0 COMMENT '库存数量',
  `exchange_count` int(11) DEFAULT 0 COMMENT '已兑换数量',
  `limit_per_person` int(11) DEFAULT 0 COMMENT '每人限兑数量（0表示不限制）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-下架，1-上架',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品表';

-- 积分商品分类表
CREATE TABLE IF NOT EXISTS `t_points_goods_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(64) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商品分类表';

-- 积分兑换记录表
CREATE TABLE IF NOT EXISTS `t_points_exchange` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `goods_id` bigint(20) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(128) NOT NULL COMMENT '商品名称',
  `goods_image` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `quantity` int(11) DEFAULT 1 COMMENT '兑换数量',
  `points` int(11) NOT NULL COMMENT '消耗积分',
  `receiver_name` varchar(64) DEFAULT NULL COMMENT '收货人姓名（实物商品）',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人电话（实物商品）',
  `receiver_address` varchar(255) DEFAULT NULL COMMENT '收货地址（实物商品）',
  `status` tinyint(1) DEFAULT 0 COMMENT '兑换状态：0-待处理，1-已发货，2-已完成，3-已取消',
  `tracking_number` varchar(64) DEFAULT NULL COMMENT '物流单号（实物商品）',
  `express_company` varchar(64) DEFAULT NULL COMMENT '物流公司（实物商品）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分兑换记录表';

-- 活动表
CREATE TABLE IF NOT EXISTS `t_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `name` varchar(128) NOT NULL COMMENT '活动名称',
  `subtitle` varchar(255) DEFAULT NULL COMMENT '活动副标题',
  `description` text DEFAULT NULL COMMENT '活动描述',
  `content` longtext DEFAULT NULL COMMENT '活动详细内容（富文本）',
  `image` varchar(255) DEFAULT NULL COMMENT '活动图片',
  `type` tinyint(1) DEFAULT 1 COMMENT '活动类型：1-裂变营销，2-签到活动，3-其他活动',
  `rules` text DEFAULT NULL COMMENT '活动规则，JSON格式存储',
  `rewards` text DEFAULT NULL COMMENT '活动奖励，JSON格式存储',
  `location` varchar(255) DEFAULT NULL COMMENT '活动地点',
  `participant_count` int(11) DEFAULT 0 COMMENT '参与人数',
  `max_participants` int(11) DEFAULT 0 COMMENT '最大参与人数（0表示不限制）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '活动状态：0-未开始，1-进行中，2-已结束',
  `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门活动：0-否，1-是',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- 邀请记录表
CREATE TABLE IF NOT EXISTS `t_invite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `inviter_id` bigint(20) NOT NULL COMMENT '邀请人用户ID',
  `invitee_id` bigint(20) DEFAULT NULL COMMENT '被邀请人用户ID',
  `invite_code` varchar(32) NOT NULL COMMENT '邀请码',
  `activity_id` bigint(20) DEFAULT NULL COMMENT '关联活动ID',
  `status` tinyint(1) DEFAULT 0 COMMENT '邀请状态：0-未接受，1-已接受',
  `reward_status` tinyint(1) DEFAULT 0 COMMENT '奖励状态：0-未发放，1-已发放',
  `reward_points` int(11) DEFAULT 0 COMMENT '奖励积分',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_invite_code` (`invite_code`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitee_id` (`invitee_id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请记录表';

-- 聊天会话表
CREATE TABLE IF NOT EXISTS `t_chat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '发起用户ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收用户ID',
  `last_message` varchar(500) DEFAULT NULL COMMENT '最后一条消息内容',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后一条消息时间',
  `unread_count` int(11) DEFAULT 0 COMMENT '未读消息数（接收方）',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_pair` (`from_user_id`, `to_user_id`),
  KEY `idx_to_user_id` (`to_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `t_chat_message` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `chat_id` bigint(20) NOT NULL COMMENT '会话ID',
  `from_user_id` bigint(20) NOT NULL COMMENT '发送者用户ID',
  `to_user_id` bigint(20) NOT NULL COMMENT '接收者用户ID',
  `content` text DEFAULT NULL COMMENT '消息内容',
  `type` tinyint(1) DEFAULT 1 COMMENT '消息类型：1-文本，2-图片，3-语音，4-视频，5-文件',
  `media_url` varchar(255) DEFAULT NULL COMMENT '媒体文件URL（图片/语音/视频/文件）',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_chat_id` (`chat_id`),
  KEY `idx_from_user_id` (`from_user_id`),
  KEY `idx_to_user_id` (`to_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';

-- 管理员表
CREATE TABLE IF NOT EXISTS `t_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(64) NOT NULL COMMENT '用户名',
  `password` varchar(128) NOT NULL COMMENT '密码',
  `real_name` varchar(64) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) DEFAULT NULL COMMENT '邮箱',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(64) DEFAULT NULL COMMENT '最后登录IP',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 角色表
CREATE TABLE IF NOT EXISTS `t_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(64) NOT NULL COMMENT '角色名称',
  `code` varchar(64) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `t_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `name` varchar(64) NOT NULL COMMENT '权限名称',
  `code` varchar(64) NOT NULL COMMENT '权限编码',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `type` tinyint(1) DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径（菜单类型）',
  `icon` varchar(64) DEFAULT NULL COMMENT '图标（菜单类型）',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `t_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`, `permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS `t_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `name` varchar(64) NOT NULL COMMENT '配置名称',
  `key` varchar(64) NOT NULL COMMENT '配置键',
  `value` text DEFAULT NULL COMMENT '配置值',
  `type` tinyint(1) DEFAULT 1 COMMENT '配置类型：1-系统配置，2-业务配置',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_key` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- Banner表
CREATE TABLE IF NOT EXISTS `t_banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'Banner ID',
  `title` varchar(128) NOT NULL COMMENT 'Banner标题',
  `image` varchar(255) NOT NULL COMMENT 'Banner图片',
  `link` varchar(255) DEFAULT NULL COMMENT '跳转链接',
  `type` tinyint(1) DEFAULT 1 COMMENT '链接类型：1-内部页面，2-外部链接',
  `sort` int(11) DEFAULT 0 COMMENT '排序号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Banner表';

-- 公告表
CREATE TABLE IF NOT EXISTS `t_notice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(128) NOT NULL COMMENT '公告标题',
  `content` text DEFAULT NULL COMMENT '公告内容',
  `type` tinyint(1) DEFAULT 1 COMMENT '公告类型：1-系统公告，2-活动公告',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_top` tinyint(1) DEFAULT 0 COMMENT '是否置顶：0-否，1-是',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_top` (`is_top`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';


-- 系统操作日志表
CREATE TABLE IF NOT EXISTS `t_sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(64) DEFAULT NULL COMMENT '操作用户名',
  `module` varchar(64) DEFAULT NULL COMMENT '操作模块',
  `operation_type` varchar(32) DEFAULT NULL COMMENT '操作类型',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `method` varchar(64) DEFAULT NULL COMMENT '请求方法',
  `path` varchar(255) DEFAULT NULL COMMENT '请求路径',
  `ip` varchar(64) DEFAULT NULL COMMENT '请求IP',
  `params` text DEFAULT NULL COMMENT '请求参数',
  `result` text DEFAULT NULL COMMENT '请求结果',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行耗时(ms)',
  `status` tinyint(1) DEFAULT 1 COMMENT '操作状态：0-失败，1-成功',
  `error_msg` text DEFAULT NULL COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_create_time` (`create_time`)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';

-- =============================================
-- 初始化基础数据
-- =============================================

-- 初始化管理员账号（密码：123456）
INSERT INTO `t_admin` (`username`, `password`, `real_name`, `status`) VALUES
('admin', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '系统管理员', 1);

-- 初始化角色
INSERT INTO `t_role` (`name`, `code`, `description`, `status`) VALUES
('超级管理员', 'ROLE_ADMIN', '系统超级管理员，拥有所有权限', 1),
('普通管理员', 'ROLE_MANAGER', '普通管理员，拥有部分管理权限', 1);

-- 初始化权限
INSERT INTO `t_permission` (`name`, `code`, `description`, `type`, `parent_id`, `path`, `icon`, `sort`, `status`) VALUES
('系统管理', 'system', '系统管理菜单', 1, 0, '/system', 'system', 1, 1),
('用户管理', 'system:user', '用户管理功能', 1, 1, '/system/user', 'user', 1, 1),
('角色管理', 'system:role', '角色管理功能', 1, 1, '/system/role', 'role', 2, 1),
('权限管理', 'system:permission', '权限管理功能', 1, 1, '/system/permission', 'permission', 3, 1),
('内容管理', 'content', '内容管理菜单', 1, 0, '/content', 'content', 2, 1),
('名片管理', 'content:card', '名片管理功能', 1, 5, '/content/card', 'card', 1, 1),
('活动管理', 'content:activity', '活动管理功能', 1, 5, '/content/activity', 'activity', 2, 1),
('积分管理', 'content:points', '积分管理功能', 1, 5, '/content/points', 'points', 3, 1);

-- 初始化角色权限关联
INSERT INTO `t_role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8);

-- 初始化名片模板
INSERT INTO `t_card_template` (`name`, `thumbnail`, `type`, `status`, `sort`) VALUES
('简约商务模板', 'https://example.com/templates/business.jpg', 3, 1, 1),
('科技创新模板', 'https://example.com/templates/tech.jpg', 3, 1, 2),
('时尚艺术模板', 'https://example.com/templates/art.jpg', 3, 1, 3),
('企业专业模板', 'https://example.com/templates/corporate.jpg', 2, 1, 4),
('个人创意模板', 'https://example.com/templates/creative.jpg', 1, 1, 5);

-- 初始化积分商品分类
INSERT INTO `t_points_goods_category` (`name`, `icon`, `sort`, `status`) VALUES
('实物商品', 'https://example.com/icons/goods.png', 1, 1),
('虚拟商品', 'https://example.com/icons/virtual.png', 2, 1),
('优惠券', 'https://example.com/icons/coupon.png', 3, 1),
('会员特权', 'https://example.com/icons/vip.png', 4, 1);

-- 初始化积分商品
INSERT INTO `t_points_goods` (`name`, `image`, `description`, `points`, `type`, `category_id`, `stock`, `exchange_count`, `limit_per_person`, `status`, `sort`) VALUES
('精美笔记本', 'https://example.com/goods/notebook.jpg', '高品质商务笔记本，适合办公使用', 500, 1, 1, 100, 5, 2, 1, 1),
('定制水杯', 'https://example.com/goods/cup.jpg', '个性化定制保温杯，可印制专属LOGO', 800, 1, 1, 50, 3, 1, 1, 2),
('VIP会员月卡', 'https://example.com/goods/vip.jpg', '享受VIP专属特权，包含高级模板等', 1000, 2, 4, 999, 20, 1, 1, 3),
('名片设计服务', 'https://example.com/goods/design.jpg', '专业设计师一对一名片设计服务', 1500, 2, 2, 999, 8, 0, 1, 4),
('咖啡优惠券', 'https://example.com/goods/coffee.jpg', '星巴克咖啡券，可在全国门店使用', 300, 2, 3, 200, 15, 5, 1, 5);

-- 初始化系统配置
INSERT INTO `t_config` (`name`, `key`, `value`, `type`, `description`) VALUES
('系统名称', 'system.name', 'MEH名片小程序', 1, '系统显示名称'),
('系统版本', 'system.version', '1.0.0', 1, '当前系统版本号'),
('微信小程序AppID', 'wechat.appid', 'your_wechat_appid', 1, '微信小程序AppID'),
('微信小程序Secret', 'wechat.secret', 'your_wechat_secret', 1, '微信小程序Secret'),
('文件上传路径', 'file.upload.path', '/tmp/uploads', 1, '文件上传存储路径'),
('文件访问域名', 'file.upload.domain', 'http://localhost:8080', 1, '文件访问域名'),
('每日签到积分', 'points.daily.checkin', '10', 2, '每日签到获得的积分数量'),
('新用户注册积分', 'points.register.reward', '100', 2, '新用户注册奖励积分'),
('邀请好友积分', 'points.invite.reward', '50', 2, '邀请好友注册奖励积分');

-- 初始化Banner
INSERT INTO `t_banner` (`title`, `image`, `link`, `type`, `sort`, `status`) VALUES
('欢迎使用MEH名片', 'https://example.com/banners/welcome.jpg', '/pages/index/index', 1, 1, 1),
('创建专属名片', 'https://example.com/banners/create.jpg', '/pages/card/create', 1, 2, 1),
('积分商城上线', 'https://example.com/banners/mall.jpg', '/pages/points/mall', 1, 3, 1);

-- 初始化公告
INSERT INTO `t_notice` (`title`, `content`, `type`, `status`, `is_top`) VALUES
('MEH名片小程序正式上线', '欢迎使用MEH名片小程序，开启您的数字化名片之旅！', 1, 1, 1),
('积分商城功能上线通知', '积分商城正式上线，快来用积分兑换精美礼品吧！', 2, 1, 0),
('新用户注册送积分活动', '新用户注册即可获得100积分，邀请好友还有额外奖励！', 2, 1, 0);

-- =============================================
-- 测试数据（开发环境使用）
-- =============================================

-- 测试用户数据
INSERT INTO `t_user` (`openid`, `nickname`, `avatar`, `gender`, `phone`, `email`, `real_name`, `level`, `status`, `last_login_time`, `last_login_ip`, `create_time`, `update_time`) VALUES
('wx_test_001', '张三', 'https://example.com/avatars/zhangsan.jpg', 1, '13800138001', '<EMAIL>', '张三', 0, 1, '2025-01-20 10:00:00', '127.0.0.1', '2025-01-15 09:00:00', '2025-01-20 10:00:00'),
('wx_test_002', '李四', 'https://example.com/avatars/lisi.jpg', 2, '13800138002', '<EMAIL>', '李四', 1, 1, '2025-01-20 11:00:00', '127.0.0.1', '2025-01-16 10:00:00', '2025-01-20 11:00:00'),
('wx_test_003', '王五', 'https://example.com/avatars/wangwu.jpg', 1, '13800138003', '<EMAIL>', '王五', 0, 1, '2025-01-20 12:00:00', '127.0.0.1', '2025-01-17 11:00:00', '2025-01-20 12:00:00'),
('wx_test_004', '赵六', 'https://example.com/avatars/zhaoliu.jpg', 2, '13800138004', '<EMAIL>', '赵六', 2, 1, '2025-01-20 13:00:00', '127.0.0.1', '2025-01-18 12:00:00', '2025-01-20 13:00:00'),
('wx_test_005', '钱七', 'https://example.com/avatars/qianqi.jpg', 1, '13800138005', '<EMAIL>', '钱七', 0, 1, '2025-01-20 14:00:00', '127.0.0.1', '2025-01-19 13:00:00', '2025-01-20 14:00:00');

-- 测试用户积分数据
INSERT INTO `t_user_points` (`user_id`, `points`, `total_points`, `used_points`, `continuous_login_days`, `last_sign_date`, `create_time`, `update_time`) VALUES
(1, 850, 1000, 150, 5, '2025-01-20 09:00:00', '2025-01-15 09:00:00', '2025-01-20 09:00:00'),
(2, 1200, 1500, 300, 8, '2025-01-20 10:00:00', '2025-01-16 10:00:00', '2025-01-20 10:00:00'),
(3, 650, 800, 150, 3, '2025-01-20 11:00:00', '2025-01-17 11:00:00', '2025-01-20 11:00:00'),
(4, 2100, 2500, 400, 12, '2025-01-20 12:00:00', '2025-01-18 12:00:00', '2025-01-20 12:00:00'),
(5, 450, 500, 50, 2, '2025-01-20 13:00:00', '2025-01-19 13:00:00', '2025-01-20 13:00:00');

-- 测试用户积分变动日志
INSERT INTO `t_user_points_log` (`user_id`, `type`, `points`, `before_points`, `after_points`, `source`, `description`, `create_time`) VALUES
(1, 1, 100, 0, 100, 3, '新用户注册奖励', '2025-01-15 09:00:00'),
(1, 1, 10, 100, 110, 2, '每日签到奖励', '2025-01-16 09:00:00'),
(1, 1, 50, 110, 160, 5, '邀请好友奖励', '2025-01-17 09:00:00'),
(2, 1, 100, 0, 100, 3, '新用户注册奖励', '2025-01-16 10:00:00'),
(2, 1, 10, 100, 110, 2, '每日签到奖励', '2025-01-17 10:00:00'),
(3, 1, 100, 0, 100, 3, '新用户注册奖励', '2025-01-17 11:00:00'),
(4, 1, 100, 0, 100, 3, '新用户注册奖励', '2025-01-18 12:00:00'),
(5, 1, 100, 0, 100, 3, '新用户注册奖励', '2025-01-19 13:00:00');

-- 测试名片数据
INSERT INTO `t_card` (`user_id`, `type`, `name`, `position`, `company`, `phone`, `email`, `address`, `introduction`, `skills`, `template_id`, `qrcode`, `visit_count`, `like_count`, `comment_count`, `is_default`, `create_time`, `update_time`) VALUES
(1, 1, '张三', '高级产品经理', '码布斯科技有限公司', '13800138001', '<EMAIL>', '北京市朝阳区望京SOHO', '5年产品经验，专注于移动互联网产品设计与用户体验优化', '["产品设计", "用户研究", "数据分析", "项目管理"]', 1, 'https://example.com/qrcodes/card_1.png', 25, 8, 3, 1, '2025-01-15 10:00:00', '2025-01-20 10:00:00'),
(2, 2, '李四', '技术总监', '创新科技股份有限公司', '13800138002', '<EMAIL>', '上海市浦东新区陆家嘴金融中心', '10年技术管理经验，擅长团队建设和技术架构设计', '["Java", "Spring Boot", "微服务", "团队管理"]', 2, 'https://example.com/qrcodes/card_2.png', 42, 15, 7, 1, '2025-01-16 11:00:00', '2025-01-20 11:00:00'),
(3, 1, '王五', 'UI设计师', '美好设计工作室', '13800138003', '<EMAIL>', '深圳市南山区科技园', '专业UI/UX设计师，热爱创意设计和用户体验', '["UI设计", "UX设计", "Figma", "Sketch"]', 3, 'https://example.com/qrcodes/card_3.png', 18, 6, 2, 1, '2025-01-17 12:00:00', '2025-01-20 12:00:00'),
(4, 2, '赵六', '销售总监', '优质贸易有限公司', '13800138004', '<EMAIL>', '广州市天河区珠江新城', '资深销售管理专家，拥有丰富的客户资源和销售经验', '["销售管理", "客户关系", "商务谈判", "市场开拓"]', 4, 'https://example.com/qrcodes/card_4.png', 35, 12, 5, 1, '2025-01-18 13:00:00', '2025-01-20 13:00:00'),
(5, 1, '钱七', '自由摄影师', '个人工作室', '13800138005', '<EMAIL>', '杭州市西湖区文艺路', '专业摄影师，擅长人像摄影和商业摄影', '["人像摄影", "商业摄影", "后期处理", "创意策划"]', 5, 'https://example.com/qrcodes/card_5.png', 22, 9, 4, 1, '2025-01-19 14:00:00', '2025-01-20 14:00:00');

-- 测试联系人分组
INSERT INTO `t_contact_group` (`user_id`, `name`, `sort`, `create_time`, `update_time`) VALUES
(1, '同事', 1, '2025-01-15 10:00:00', '2025-01-15 10:00:00'),
(1, '客户', 2, '2025-01-15 10:00:00', '2025-01-15 10:00:00'),
(1, '朋友', 3, '2025-01-15 10:00:00', '2025-01-15 10:00:00'),
(2, '技术团队', 1, '2025-01-16 11:00:00', '2025-01-16 11:00:00'),
(2, '合作伙伴', 2, '2025-01-16 11:00:00', '2025-01-16 11:00:00');

-- 测试联系人数据
INSERT INTO `t_contact` (`user_id`, `card_id`, `name`, `position`, `company`, `phone`, `email`, `address`, `avatar`, `remark`, `group_id`, `tags`, `is_starred`, `create_time`, `update_time`) VALUES
(1, 2, '李四', '技术总监', '创新科技股份有限公司', '13800138002', '<EMAIL>', '上海市浦东新区陆家嘴金融中心', 'https://example.com/avatars/lisi.jpg', '技术大牛，合作愉快', 1, '["技术", "合作伙伴"]', 1, '2025-01-16 10:00:00', '2025-01-16 10:00:00'),
(1, 3, '王五', 'UI设计师', '美好设计工作室', '13800138003', '<EMAIL>', '深圳市南山区科技园', 'https://example.com/avatars/wangwu.jpg', '设计能力很强', 3, '["设计", "创意"]', 0, '2025-01-17 10:00:00', '2025-01-17 10:00:00'),
(2, 1, '张三', '高级产品经理', '码布斯科技有限公司', '13800138001', '<EMAIL>', '北京市朝阳区望京SOHO', 'https://example.com/avatars/zhangsan.jpg', '产品思维清晰', 2, '["产品", "合作"]', 1, '2025-01-16 11:00:00', '2025-01-16 11:00:00'),
(2, 4, '赵六', '销售总监', '优质贸易有限公司', '13800138004', '<EMAIL>', '广州市天河区珠江新城', 'https://example.com/avatars/zhaoliu.jpg', '销售经验丰富', 2, '["销售", "商务"]', 0, '2025-01-18 11:00:00', '2025-01-18 11:00:00');

-- 测试活动数据
INSERT INTO `t_activity` (`name`, `subtitle`, `description`, `content`, `image`, `type`, `rules`, `rewards`, `location`, `participant_count`, `max_participants`, `start_time`, `end_time`, `status`, `is_hot`, `sort`, `create_time`, `update_time`) VALUES
('新春邀请有礼', '邀请好友注册送积分', '邀请好友注册MEH名片小程序，双方都可获得积分奖励', '<p>活动详情：</p><ul><li>邀请1位好友注册：双方各得50积分</li><li>邀请5位好友注册：额外奖励100积分</li><li>邀请10位好友注册：额外奖励300积分</li></ul>', 'https://example.com/activities/invite.jpg', 1, '{"minInvites": 1, "maxInvites": 50, "dailyLimit": 10}', '{"invite1": 50, "invite5": 100, "invite10": 300}', '线上活动', 156, 1000, '2025-01-15 00:00:00', '2025-02-15 23:59:59', 1, 1, 1, '2025-01-10 10:00:00', '2025-01-20 10:00:00'),
('每日签到送积分', '连续签到奖励更丰厚', '每日签到可获得积分，连续签到奖励翻倍', '<p>签到规则：</p><ul><li>每日签到：10积分</li><li>连续7天：额外20积分</li><li>连续30天：额外100积分</li></ul>', 'https://example.com/activities/checkin.jpg', 2, '{"dailyPoints": 10, "week7Bonus": 20, "month30Bonus": 100}', '{"daily": 10, "weekly": 20, "monthly": 100}', '线上活动', 324, 0, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 1, 1, 2, '2025-01-01 00:00:00', '2025-01-20 10:00:00'),
('名片设计大赛', '展示你的创意名片', '设计最具创意的个人名片，赢取丰厚奖品', '<p>比赛规则：</p><ul><li>提交原创名片设计</li><li>投票选出优秀作品</li><li>专业评委评分</li></ul>', 'https://example.com/activities/design.jpg', 3, '{"submissionDeadline": "2025-02-28", "votingPeriod": "2025-03-01-2025-03-15"}', '{"first": 2000, "second": 1000, "third": 500}', '线上活动', 89, 500, '2025-02-01 00:00:00', '2025-03-15 23:59:59', 0, 0, 3, '2025-01-20 10:00:00', '2025-01-20 10:00:00');

-- 测试邀请记录
INSERT INTO `t_invite` (`inviter_id`, `invitee_id`, `invite_code`, `activity_id`, `status`, `reward_status`, `reward_points`, `create_time`, `update_time`) VALUES
(1, 2, 'INV001', 1, 1, 1, 50, '2025-01-16 09:00:00', '2025-01-16 10:00:00'),
(1, 3, 'INV002', 1, 1, 1, 50, '2025-01-17 09:00:00', '2025-01-17 11:00:00'),
(2, 4, 'INV003', 1, 1, 1, 50, '2025-01-18 10:00:00', '2025-01-18 12:00:00'),
(2, 5, 'INV004', 1, 1, 1, 50, '2025-01-19 10:00:00', '2025-01-19 13:00:00'),
(1, NULL, 'INV005', 1, 0, 0, 0, '2025-01-20 09:00:00', '2025-01-20 09:00:00');

-- 测试积分兑换记录
INSERT INTO `t_points_exchange` (`user_id`, `goods_id`, `goods_name`, `goods_image`, `quantity`, `points`, `receiver_name`, `receiver_phone`, `receiver_address`, `status`, `remark`, `create_time`, `update_time`) VALUES
(1, 1, '精美笔记本', 'https://example.com/goods/notebook.jpg', 1, 500, '张三', '13800138001', '北京市朝阳区望京SOHO T1座 1001室', 2, '已发货，请注意查收', '2025-01-18 10:00:00', '2025-01-19 15:00:00'),
(2, 3, 'VIP会员月卡', 'https://example.com/goods/vip.jpg', 1, 1000, NULL, NULL, NULL, 2, 'VIP权限已开通', '2025-01-19 11:00:00', '2025-01-19 11:30:00'),
(4, 2, '定制水杯', 'https://example.com/goods/cup.jpg', 1, 800, '赵六', '13800138004', '广州市天河区珠江新城 CBD大厦 2008室', 1, '正在处理中', '2025-01-20 09:00:00', '2025-01-20 09:00:00');

-- 测试名片访问记录
INSERT INTO `t_card_visit_log` (`card_id`, `card_user_id`, `visitor_id`, `visitor_ip`, `visitor_device`, `source`, `stay_time`, `create_time`) VALUES
(1, 1, 2, '*************', 'iPhone 13 Pro', 1, 45, '2025-01-16 14:00:00'),
(1, 1, 3, '*************', 'Xiaomi 12', 2, 32, '2025-01-17 15:00:00'),
(2, 2, 1, '*************', 'iPhone 14', 1, 58, '2025-01-17 16:00:00'),
(2, 2, 4, '*************', 'HUAWEI P50', 3, 41, '2025-01-18 17:00:00'),
(3, 3, 1, '*************', 'iPhone 13 Pro', 1, 28, '2025-01-18 18:00:00'),
(4, 4, 2, '*************', 'Samsung Galaxy S22', 2, 35, '2025-01-19 19:00:00'),
(5, 5, 3, '*************', 'OnePlus 10 Pro', 1, 52, '2025-01-20 10:00:00');

-- 测试名片点赞记录
INSERT INTO `t_card_like` (`card_id`, `card_user_id`, `user_id`, `create_time`) VALUES
(1, 1, 2, '2025-01-16 14:30:00'),
(1, 1, 3, '2025-01-17 15:30:00'),
(1, 1, 4, '2025-01-18 16:30:00'),
(2, 2, 1, '2025-01-17 16:30:00'),
(2, 2, 3, '2025-01-18 17:30:00'),
(2, 2, 5, '2025-01-19 18:30:00'),
(3, 3, 1, '2025-01-18 18:30:00'),
(3, 3, 2, '2025-01-19 19:30:00'),
(4, 4, 2, '2025-01-19 19:30:00'),
(4, 4, 3, '2025-01-20 10:30:00'),
(5, 5, 1, '2025-01-20 11:00:00'),
(5, 5, 4, '2025-01-20 11:30:00');

-- 测试名片评论
INSERT INTO `t_card_comment` (`card_id`, `card_user_id`, `user_id`, `content`, `parent_id`, `status`, `create_time`, `update_time`) VALUES
(1, 1, 2, '名片设计很棒，信息很全面！', NULL, 1, '2025-01-16 15:00:00', '2025-01-16 15:00:00'),
(1, 1, 3, '专业的产品经理，期待合作机会', NULL, 1, '2025-01-17 16:00:00', '2025-01-17 16:00:00'),
(1, 1, 1, '谢谢夸奖，欢迎交流！', 2, 1, '2025-01-17 16:30:00', '2025-01-17 16:30:00'),
(2, 2, 1, '技术实力很强，学习了！', NULL, 1, '2025-01-17 17:00:00', '2025-01-17 17:00:00'),
(2, 2, 4, '希望有机会合作', NULL, 1, '2025-01-18 18:00:00', '2025-01-18 18:00:00'),
(3, 3, 1, '设计风格很独特，很有创意', NULL, 1, '2025-01-18 19:00:00', '2025-01-18 19:00:00'),
(4, 4, 2, '销售经验丰富，值得学习', NULL, 1, '2025-01-19 20:00:00', '2025-01-19 20:00:00'),
(5, 5, 1, '摄影作品很棒，技术精湛', NULL, 1, '2025-01-20 12:00:00', '2025-01-20 12:00:00');

-- 测试聊天会话
INSERT INTO `t_chat` (`from_user_id`, `to_user_id`, `last_message`, `last_message_time`, `unread_count`, `create_time`, `update_time`) VALUES
(1, 2, '你好，看了你的名片，很专业！', '2025-01-16 20:00:00', 0, '2025-01-16 19:00:00', '2025-01-16 20:00:00'),
(2, 1, '谢谢，有机会可以合作', '2025-01-16 20:30:00', 1, '2025-01-16 19:00:00', '2025-01-16 20:30:00'),
(1, 3, '设计作品很棒！', '2025-01-17 21:00:00', 0, '2025-01-17 20:00:00', '2025-01-17 21:00:00'),
(3, 1, '谢谢认可，欢迎交流', '2025-01-17 21:30:00', 2, '2025-01-17 20:00:00', '2025-01-17 21:30:00'),
(2, 4, '销售方面想请教一下', '2025-01-18 22:00:00', 0, '2025-01-18 21:00:00', '2025-01-18 22:00:00');

-- 测试聊天消息
INSERT INTO `t_chat_message` (`chat_id`, `from_user_id`, `to_user_id`, `content`, `type`, `media_url`, `is_read`, `create_time`) VALUES
(1, 1, 2, '你好，看了你的名片，技术背景很强！', 1, NULL, 1, '2025-01-16 19:30:00'),
(1, 2, 1, '谢谢，你的产品经验也很丰富', 1, NULL, 1, '2025-01-16 19:45:00'),
(1, 1, 2, '有机会可以合作一个项目', 1, NULL, 1, '2025-01-16 20:00:00'),
(1, 2, 1, '好的，期待合作机会', 1, NULL, 0, '2025-01-16 20:30:00'),
(2, 1, 3, '你好，设计作品很有创意！', 1, NULL, 1, '2025-01-17 20:30:00'),
(2, 3, 1, '谢谢夸奖，欢迎多交流', 1, NULL, 1, '2025-01-17 21:00:00'),
(2, 1, 3, '有设计需求的话会联系你', 1, NULL, 1, '2025-01-17 21:00:00'),
(2, 3, 1, '随时欢迎！', 1, NULL, 0, '2025-01-17 21:30:00'),
(3, 2, 4, '你好，销售方面想请教一下', 1, NULL, 1, '2025-01-18 21:30:00'),
(3, 4, 2, '没问题，有什么想了解的？', 1, NULL, 1, '2025-01-18 22:00:00');

-- 测试系统操作日志
INSERT INTO `t_sys_operation_log` (`user_id`, `username`, `module`, `operation_type`, `description`, `method`, `path`, `ip`, `params`, `result`, `execution_time`, `status`, `create_time`) VALUES
(1, 'zhangsan', '用户认证', '登录', '微信小程序登录', 'POST', '/auth/wx-login', '127.0.0.1', '{"code":"wx_login_code_001"}', '{"code":200,"message":"登录成功","data":{"token":"eyJhbGciOiJIUzUxMiJ9..."}}', 125, 1, '2025-01-15 09:00:00'),
(1, 'zhangsan', '名片管理', '新增', '创建个人名片', 'POST', '/cards', '127.0.0.1', '{"name":"张三","company":"码布斯科技","position":"产品经理"}', '{"code":200,"message":"创建成功"}', 89, 1, '2025-01-15 10:00:00'),
(2, 'lisi', '用户认证', '登录', '微信小程序登录', 'POST', '/auth/wx-login', '127.0.0.1', '{"code":"wx_login_code_002"}', '{"code":200,"message":"登录成功"}', 98, 1, '2025-01-16 10:00:00'),
(2, 'lisi', '名片管理', '新增', '创建企业名片', 'POST', '/cards', '127.0.0.1', '{"name":"李四","company":"创新科技","position":"技术总监"}', '{"code":200,"message":"创建成功"}', 76, 1, '2025-01-16 11:00:00'),
(1, 'zhangsan', '积分管理', '兑换', '兑换精美笔记本', 'POST', '/points/exchange', '127.0.0.1', '{"goodsId":1,"quantity":1}', '{"code":200,"message":"兑换成功"}', 156, 1, '2025-01-18 10:00:00'),
(2, 'lisi', '积分管理', '兑换', '兑换VIP会员月卡', 'POST', '/points/exchange', '127.0.0.1', '{"goodsId":3,"quantity":1}', '{"code":200,"message":"兑换成功"}', 134, 1, '2025-01-19 11:00:00'),
(3, 'wangwu', '用户认证', '登录', '微信小程序登录', 'POST', '/auth/wx-login', '127.0.0.1', '{"code":"wx_login_code_003"}', '{"code":200,"message":"登录成功"}', 112, 1, '2025-01-17 11:00:00'),
(4, 'zhaoliu', '用户认证', '登录', '微信小程序登录', 'POST', '/auth/wx-login', '127.0.0.1', '{"code":"wx_login_code_004"}', '{"code":200,"message":"登录成功"}', 108, 1, '2025-01-18 12:00:00'),
(5, 'qianqi', '用户认证', '登录', '微信小程序登录', 'POST', '/auth/wx-login', '127.0.0.1', '{"code":"wx_login_code_005"}', '{"code":200,"message":"登录成功"}', 119, 1, '2025-01-19 13:00:00'),
(1, 'zhangsan', '联系人管理', '新增', '添加联系人', 'POST', '/contacts', '127.0.0.1', '{"name":"李四","phone":"13800138002"}', '{"code":200,"message":"添加成功"}', 67, 1, '2025-01-16 10:30:00');

-- =============================================
-- 数据库初始化完成
-- =============================================

-- 显示统计信息
SELECT
    '数据库初始化完成' as status,
    (SELECT COUNT(*) FROM t_user) as user_count,
    (SELECT COUNT(*) FROM t_card) as card_count,
    (SELECT COUNT(*) FROM t_activity) as activity_count,
    (SELECT COUNT(*) FROM t_points_goods) as goods_count,
    (SELECT COUNT(*) FROM t_card_template) as template_count;
