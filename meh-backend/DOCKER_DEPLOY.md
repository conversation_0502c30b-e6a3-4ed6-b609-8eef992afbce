# 电子名片系统 Docker 部署指南

## 概述

本文档提供了使用 Docker 和 Docker Compose 部署码布斯企业家俱乐部电子名片系统的详细步骤。通过容器化部署，可以简化安装过程，提高系统的可移植性和可扩展性。

## 前提条件

在开始部署之前，请确保您的服务器已安装以下软件：

- Docker (版本 20.10.0 或更高)
- Docker Compose (版本 2.0.0 或更高)

可以使用以下命令检查安装版本：

```bash
docker --version
docker-compose --version
```

## 部署步骤

### 1. 获取项目代码

```bash
git clone https://github.com/your-organization/meh-businesscard.git
cd meh-businesscard/backend
```

### 2. 配置环境变量

根据您的实际环境，修改 `docker-compose.yml` 文件中的环境变量：

```yaml
environment:
  - SPRING_PROFILES_ACTIVE=prod
  - SPRING_DATASOURCE_URL=************************************************************************************************************
  - SPRING_DATASOURCE_USERNAME=root
  - SPRING_DATASOURCE_PASSWORD=password
  - SPRING_REDIS_HOST=redis
  - SPRING_REDIS_PORT=6379
```

### 3. 准备数据库初始化脚本

将数据库初始化脚本放在 `sql` 目录下，这些脚本将在 MySQL 容器首次启动时自动执行：

```bash
mkdir -p sql
# 将初始化脚本复制到 sql 目录
```

### 4. 构建并启动容器

```bash
# 构建并启动所有服务
docker-compose up -d

# 仅构建应用服务
docker-compose build app

# 查看服务状态
docker-compose ps
```

### 5. 查看日志

```bash
# 查看所有服务的日志
docker-compose logs

# 查看特定服务的日志
docker-compose logs app
docker-compose logs mysql
```

### 6. 停止和重启服务

```bash
# 停止所有服务
docker-compose down

# 停止并删除所有数据卷（慎用！）
docker-compose down -v

# 重启特定服务
docker-compose restart app
```

## 服务访问

部署完成后，可以通过以下方式访问各个服务：

- 应用服务: http://your-server-ip:8080
- MySQL 数据库: your-server-ip:3306
- Redis 服务: your-server-ip:6379

## 数据备份与恢复

### 数据库备份

```bash
docker exec meh-businesscard-mysql sh -c 'exec mysqldump -uroot -p"password" meh_businesscard' > backup.sql
```

### 数据库恢复

```bash
cat backup.sql | docker exec -i meh-businesscard-mysql sh -c 'exec mysql -uroot -p"password" meh_businesscard'
```

## 常见问题排查

1. **容器无法启动**
   - 检查日志: `docker-compose logs app`
   - 确认端口是否被占用: `netstat -tulpn | grep 8080`

2. **数据库连接失败**
   - 检查数据库容器是否正常运行: `docker-compose ps mysql`
   - 检查数据库连接配置是否正确

3. **Redis连接失败**
   - 检查Redis容器是否正常运行: `docker-compose ps redis`
   - 检查Redis连接配置是否正确

## 生产环境优化建议

1. **安全性优化**
   - 修改默认密码
   - 限制容器网络访问
   - 使用 Docker Secrets 管理敏感信息

2. **性能优化**
   - 根据服务器资源调整 JVM 参数
   - 配置合适的连接池大小
   - 启用数据库索引和查询优化

3. **高可用性配置**
   - 配置数据库主从复制
   - 使用 Redis 集群
   - 考虑使用 Docker Swarm 或 Kubernetes 进行容器编排

## 更新部署

当有新版本发布时，可以按照以下步骤更新部署：

```bash
# 拉取最新代码
git pull

# 重新构建应用镜像
docker-compose build app

# 重启应用服务
docker-compose up -d app
```
