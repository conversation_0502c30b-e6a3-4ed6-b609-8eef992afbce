package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分商品实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_points_goods")
public class PointsGoods implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 商品名称
     */
    private String name;
    
    /**
     * 商品图片
     */
    private String image;
    
    /**
     * 商品描述
     */
    private String description;
    
    /**
     * 所需积分
     */
    private Integer points;
    
    /**
     * 商品类型：1-实物商品，2-虚拟商品
     */
    private Integer type;
    
    /**
     * 商品分类ID
     */
    private Long categoryId;
    
    /**
     * 库存数量
     */
    private Integer stock;
    
    /**
     * 已兑换数量
     */
    private Integer exchangeCount;
    
    /**
     * 每人限兑数量（0表示不限制）
     */
    private Integer limitPerPerson;
    
    /**
     * 状态：0-下架，1-上架
     */
    private Integer status;
    
    /**
     * 排序号
     */
    private Integer sort;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
