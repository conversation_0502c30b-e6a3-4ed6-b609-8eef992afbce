package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户积分变动日志实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_user_points_log")
public class UserPointsLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 变动类型：1-增加，2-减少
     */
    private Integer type;
    
    /**
     * 变动积分
     */
    private Integer points;
    
    /**
     * 变动前积分
     */
    private Integer beforePoints;
    
    /**
     * 变动后积分
     */
    private Integer afterPoints;
    
    /**
     * 变动来源：1-每日登录，2-签到，3-完善信息，4-社交互动，5-邀请好友，6-活动参与，7-积分兑换，8-管理员操作
     */
    private Integer source;
    
    /**
     * 变动描述
     */
    private String description;
    
    /**
     * 操作人ID（管理员操作时记录）
     */
    private Long operatorId;
    
    /**
     * 操作人姓名（管理员操作时记录）
     */
    private String operatorName;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
