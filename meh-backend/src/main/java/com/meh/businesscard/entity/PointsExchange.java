package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分兑换记录实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_points_exchange")
public class PointsExchange implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 商品ID
     */
    private Long goodsId;
    
    /**
     * 商品名称
     */
    private String goodsName;
    
    /**
     * 商品图片
     */
    private String goodsImage;
    
    /**
     * 兑换数量
     */
    private Integer quantity;
    
    /**
     * 消耗积分
     */
    private Integer points;
    
    /**
     * 收货人姓名（实物商品）
     */
    private String receiverName;
    
    /**
     * 收货人电话（实物商品）
     */
    private String receiverPhone;
    
    /**
     * 收货地址（实物商品）
     */
    private String receiverAddress;
    
    /**
     * 兑换状态：0-待处理，1-已发货，2-已完成，3-已取消
     */
    private Integer status;
    
    /**
     * 物流单号（实物商品）
     */
    private String trackingNumber;
    
    /**
     * 物流公司（实物商品）
     */
    private String expressCompany;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
