package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片访问记录实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_card_visit_log")
public class CardVisitLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 名片ID
     */
    private Long cardId;
    
    /**
     * 名片所属用户ID
     */
    private Long cardUserId;
    
    /**
     * 访问者用户ID（已登录用户）
     */
    private Long visitorId;
    
    /**
     * 访问者IP
     */
    private String visitorIp;
    
    /**
     * 访问者设备信息
     */
    private String visitorDevice;
    
    /**
     * 访问来源：1-小程序，2-分享链接，3-扫码
     */
    private Integer source;
    
    /**
     * 停留时长（秒）
     */
    private Integer stayTime;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
