package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 邀请记录实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_invite")
public class Invite implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 邀请人用户ID
     */
    private Long inviterId;
    
    /**
     * 被邀请人用户ID
     */
    private Long inviteeId;
    
    /**
     * 邀请码
     */
    private String inviteCode;
    
    /**
     * 关联活动ID
     */
    private Long activityId;
    
    /**
     * 邀请状态：0-未接受，1-已接受
     */
    private Integer status;
    
    /**
     * 奖励状态：0-未发放，1-已发放
     */
    private Integer rewardStatus;
    
    /**
     * 奖励积分
     */
    private Integer rewardPoints;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
