package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片点赞实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_card_like")
public class CardLike implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 名片ID
     */
    private Long cardId;
    
    /**
     * 名片所属用户ID
     */
    private Long cardUserId;
    
    /**
     * 点赞用户ID
     */
    private Long userId;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
