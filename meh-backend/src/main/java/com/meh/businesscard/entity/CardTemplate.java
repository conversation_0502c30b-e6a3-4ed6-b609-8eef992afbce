package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片模板实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_card_template")
public class CardTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 模板ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板缩略图
     */
    private String thumbnail;
    
    /**
     * 模板HTML内容
     */
    private String content;
    
    /**
     * 模板CSS样式
     */
    private String style;
    
    /**
     * 模板类型：1-个人模板，2-企业模板，3-通用模板
     */
    private Integer type;
    
    /**
     * 模板状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 排序号
     */
    private Integer sort;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
