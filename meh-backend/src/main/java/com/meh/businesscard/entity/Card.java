package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_card")
public class Card implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 名片ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 名片类型：1-个人名片，2-企业名片
     */
    private Integer type;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 公司
     */
    private String company;
    
    /**
     * 电话
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 个人简介
     */
    private String introduction;
    
    /**
     * 专业技能，JSON格式存储
     */
    private String skills;
    
    /**
     * 个人资源，JSON格式存储
     */
    private String resources;
    
    /**
     * 项目经历，JSON格式存储
     */
    private String projects;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 名片二维码
     */
    private String qrcode;
    
    /**
     * 访问次数
     */
    private Integer visitCount;
    
    /**
     * 点赞次数
     */
    private Integer likeCount;
    
    /**
     * 评论次数
     */
    private Integer commentCount;
    
    /**
     * 是否默认名片：0-否，1-是
     */
    private Integer isDefault;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
