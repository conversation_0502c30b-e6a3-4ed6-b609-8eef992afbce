package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_chat_message")
public class ChatMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 会话ID
     */
    private Long chatId;
    
    /**
     * 发送者用户ID
     */
    private Long fromUserId;
    
    /**
     * 接收者用户ID
     */
    private Long toUserId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型：1-文本，2-图片，3-语音，4-视频，5-文件
     */
    private Integer type;
    
    /**
     * 媒体文件URL（图片/语音/视频/文件）
     */
    private String mediaUrl;
    
    /**
     * 是否已读：0-未读，1-已读
     */
    private Integer isRead;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
