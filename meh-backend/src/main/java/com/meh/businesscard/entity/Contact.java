package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 联系人实体类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@TableName("t_contact")
public class Contact implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 联系人ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID（联系人所属用户）
     */
    private Long userId;
    
    /**
     * 关联的名片ID
     */
    private Long cardId;
    
    /**
     * 联系人姓名
     */
    private String name;
    
    /**
     * 联系人职位
     */
    private String position;
    
    /**
     * 联系人公司
     */
    private String company;
    
    /**
     * 联系人电话
     */
    private String phone;
    
    /**
     * 联系人邮箱
     */
    private String email;
    
    /**
     * 联系人地址
     */
    private String address;
    
    /**
     * 联系人头像
     */
    private String avatar;
    
    /**
     * 联系人备注
     */
    private String remark;
    
    /**
     * 分组ID
     */
    private Long groupId;
    
    /**
     * 标签，JSON格式存储
     */
    private String tags;
    
    /**
     * 是否星标联系人：0-否，1-是
     */
    private Integer isStarred;
    
    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
