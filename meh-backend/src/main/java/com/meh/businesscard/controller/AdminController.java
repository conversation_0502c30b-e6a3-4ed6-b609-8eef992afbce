package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.service.UserPointsService;
import com.meh.businesscard.service.CardService;
import com.meh.businesscard.service.ContactService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Api(tags = "管理员功能")
@RestController
@RequestMapping("/admin")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    @Autowired
    private CardService cardService;

    @Autowired
    private ContactService contactService;

    /**
     * 获取系统统计信息
     *
     * @return 统计信息
     */
    @ApiOperation(value = "获取系统统计信息", notes = "获取用户数量、名片数量等统计数据")
    @GetMapping("/stats")
    @OperationLog(module = "管理员功能", operationType = "查询", description = "获取系统统计信息")
    public Result<?> getSystemStats() {
        // TODO: 实现系统统计信息获取
        // 包括：总用户数、今日新增用户、总名片数、今日新增名片、总积分消耗等
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", 1000);
        stats.put("todayNewUsers", 50);
        stats.put("totalCards", 5000);
        stats.put("todayNewCards", 200);
        stats.put("totalPointsConsumed", 100000);
        stats.put("todayPointsConsumed", 5000);
        
        return Result.success(stats);
    }

    /**
     * 获取用户列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键词
     * @return 用户列表
     */
    @ApiOperation(value = "获取用户列表", notes = "分页获取用户列表，支持关键词搜索")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "20", paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "keyword", value = "搜索关键词", paramType = "query", dataTypeClass = String.class)
    })
    @GetMapping("/users")
    @OperationLog(module = "管理员功能", operationType = "查询", description = "获取用户列表")
    public Result<?> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {
        // TODO: 实现用户列表查询
        return Result.success();
    }

    /**
     * 禁用/启用用户
     *
     * @param userId 用户ID
     * @param status 状态：0-禁用，1-启用
     * @return 操作结果
     */
    @ApiOperation(value = "禁用/启用用户", notes = "管理员可以禁用或启用用户账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, paramType = "path", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "status", value = "状态：0-禁用，1-启用", required = true, paramType = "query", dataTypeClass = Integer.class)
    })
    @PutMapping("/users/{userId}/status")
    @OperationLog(module = "管理员功能", operationType = "修改", description = "禁用/启用用户")
    public Result<?> updateUserStatus(@PathVariable Long userId, @RequestParam Integer status) {
        User user = userService.getById(userId);
        if (user == null) {
            return Result.failed("用户不存在");
        }

        user.setStatus(status);
        boolean success = userService.updateById(user);
        if (success) {
            return Result.success();
        } else {
            return Result.failed("操作失败");
        }
    }

    /**
     * 管理员操作用户积分
     *
     * @param userId 用户ID
     * @param points 积分数量（正数增加，负数减少）
     * @param description 操作描述
     * @return 操作结果
     */
    @ApiOperation(value = "管理员操作用户积分", notes = "管理员可以手动调整用户积分")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, paramType = "path", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "points", value = "积分数量（正数增加，负数减少）", required = true, paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "description", value = "操作描述", required = true, paramType = "query", dataTypeClass = String.class)
    })
    @PostMapping("/users/{userId}/points")
    @OperationLog(module = "管理员功能", operationType = "积分操作", description = "管理员操作用户积分")
    public Result<?> operateUserPoints(
            @PathVariable Long userId,
            @RequestParam Integer points,
            @RequestParam String description) {
        
        // 获取当前管理员信息
        User currentAdmin = userService.getCurrentUser();
        
        boolean success = userPointsService.operatePoints(
                userId, 
                points, 
                currentAdmin.getId(), 
                currentAdmin.getNickname() != null ? currentAdmin.getNickname() : currentAdmin.getPhone(),
                description
        );
        
        if (success) {
            return Result.success();
        } else {
            return Result.failed("操作失败");
        }
    }

    /**
     * 获取用户积分详情
     *
     * @param userId 用户ID
     * @return 积分详情
     */
    @ApiOperation(value = "获取用户积分详情", notes = "获取指定用户的积分详细信息")
    @GetMapping("/users/{userId}/points")
    @OperationLog(module = "管理员功能", operationType = "查询", description = "获取用户积分详情")
    public Result<UserPoints> getUserPoints(@PathVariable Long userId) {
        UserPoints userPoints = userPointsService.getUserPoints(userId);
        return Result.success(userPoints);
    }

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    @ApiOperation(value = "删除用户", notes = "管理员可以删除用户账号（软删除）")
    @DeleteMapping("/users/{userId}")
    @OperationLog(module = "管理员功能", operationType = "删除", description = "删除用户")
    public Result<?> deleteUser(@PathVariable Long userId) {
        User user = userService.getById(userId);
        if (user == null) {
            return Result.failed("用户不存在");
        }

        user.setDeleted(1);
        boolean success = userService.updateById(user);
        if (success) {
            return Result.success();
        } else {
            return Result.failed("删除失败");
        }
    }

    /**
     * 获取系统配置
     *
     * @return 系统配置
     */
    @ApiOperation(value = "获取系统配置", notes = "获取系统配置信息")
    @GetMapping("/config")
    @OperationLog(module = "管理员功能", operationType = "查询", description = "获取系统配置")
    public Result<?> getSystemConfig() {
        // TODO: 实现系统配置获取
        Map<String, Object> config = new HashMap<>();
        config.put("pointsRules", "积分规则配置");
        config.put("systemSettings", "系统设置");
        
        return Result.success(config);
    }

    /**
     * 更新系统配置
     *
     * @param config 配置信息
     * @return 更新结果
     */
    @ApiOperation(value = "更新系统配置", notes = "更新系统配置信息")
    @PutMapping("/config")
    @OperationLog(module = "管理员功能", operationType = "修改", description = "更新系统配置")
    public Result<?> updateSystemConfig(@RequestBody Map<String, Object> config) {
        // TODO: 实现系统配置更新
        return Result.success();
    }
}
