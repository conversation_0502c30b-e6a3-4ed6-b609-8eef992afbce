package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.entity.UserPointsLog;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.service.UserPointsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @ApiOperation(value = "获取当前用户信息", notes = "获取当前登录用户的详细信息")
    @GetMapping("/current")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取当前用户信息")
    public Result<User> getCurrentUser() {
        User currentUser = userService.getCurrentUser();
        return Result.success(currentUser);
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
    @ApiOperation(value = "更新用户信息", notes = "更新当前用户的基本信息")
    @PutMapping("/current")
    @OperationLog(module = "用户管理", operationType = "修改", description = "更新用户信息")
    public Result<?> updateCurrentUser(@RequestBody User user) {
        User currentUser = userService.getCurrentUser();
        user.setId(currentUser.getId());
        
        // 只允许更新部分字段
        User updateUser = new User();
        updateUser.setId(currentUser.getId());
        updateUser.setNickname(user.getNickname());
        updateUser.setAvatar(user.getAvatar());
        updateUser.setGender(user.getGender());
        updateUser.setRealName(user.getRealName());
        updateUser.setEmail(user.getEmail());
        
        boolean success = userService.updateById(updateUser);
        if (success) {
            return Result.success();
        } else {
            return Result.failed("更新失败");
        }
    }

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 上传结果
     */
    @ApiOperation(value = "上传头像", notes = "上传用户头像图片")
    @PostMapping("/avatar")
    @OperationLog(module = "用户管理", operationType = "上传", description = "上传用户头像")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        // TODO: 实现文件上传逻辑
        // 这里需要集成文件存储服务（如阿里云OSS、腾讯云COS等）
        String avatarUrl = "https://example.com/avatar/" + System.currentTimeMillis() + ".jpg";
        
        // 更新用户头像
        User currentUser = userService.getCurrentUser();
        currentUser.setAvatar(avatarUrl);
        userService.updateById(currentUser);
        
        return Result.success(avatarUrl);
    }

    /**
     * 获取用户积分信息
     *
     * @return 积分信息
     */
    @ApiOperation(value = "获取用户积分信息", notes = "获取当前用户的积分详情")
    @GetMapping("/points")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户积分信息")
    public Result<UserPoints> getUserPoints() {
        User currentUser = userService.getCurrentUser();
        UserPoints userPoints = userPointsService.getUserPoints(currentUser.getId());
        return Result.success(userPoints);
    }

    /**
     * 获取积分变动记录
     *
     * @param page 页码
     * @param size 每页大小
     * @return 积分变动记录
     */
    @ApiOperation(value = "获取积分变动记录", notes = "分页获取用户积分变动历史记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", paramType = "query", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "20", paramType = "query", dataTypeClass = Integer.class)
    })
    @GetMapping("/points/logs")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取积分变动记录")
    public Result<List<UserPointsLog>> getPointsLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        User currentUser = userService.getCurrentUser();
        List<UserPointsLog> logs = userPointsService.getPointsLogs(currentUser.getId(), page, size);
        return Result.success(logs);
    }

    /**
     * 每日签到
     *
     * @return 签到结果
     */
    @ApiOperation(value = "每日签到", notes = "用户每日签到获取积分")
    @PostMapping("/checkin")
    @OperationLog(module = "用户管理", operationType = "签到", description = "用户每日签到")
    public Result<?> dailyCheckin() {
        User currentUser = userService.getCurrentUser();
        return userPointsService.dailyCheckin(currentUser.getId());
    }

    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
    @ApiOperation(value = "获取用户统计信息", notes = "获取用户的各项统计数据")
    @GetMapping("/stats")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户统计信息")
    public Result<?> getUserStats() {
        User currentUser = userService.getCurrentUser();
        // TODO: 实现用户统计信息获取
        // 包括：名片数量、联系人数量、访客数量、积分总数等
        return Result.success();
    }
}
