package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.CardCreateDTO;
import com.meh.businesscard.dto.CardUpdateDTO;
import com.meh.businesscard.entity.Card;
import com.meh.businesscard.entity.CardComment;
import com.meh.businesscard.entity.CardVisitLog;
import com.meh.businesscard.service.CardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 名片控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Api(tags = "名片管理")
@RestController
@RequestMapping("/cards")
public class CardController {

    @Autowired
    private CardService cardService;

    /**
     * 创建名片
     *
     * @param cardCreateDTO 名片创建参数
     * @return 创建结果
     */
    @ApiOperation(value = "创建名片", notes = "创建个人电子名片")
    @PostMapping
    @OperationLog(module = "名片管理", operationType = "新增", description = "创建名片")
    public Result<?> createCard(@RequestBody @Validated CardCreateDTO cardCreateDTO) {
        return cardService.createCard(cardCreateDTO);
    }

    /**
     * 更新名片
     *
     * @param id 名片ID
     * @param cardUpdateDTO 名片更新参数
     * @return 更新结果
     */
    @ApiOperation(value = "更新名片", notes = "更新指定ID的名片信息")
    @PutMapping("/{id}")
    @OperationLog(module = "名片管理", operationType = "修改", description = "更新名片")
    public Result<?> updateCard(
        @PathVariable Long id,
        @RequestBody @Validated CardUpdateDTO cardUpdateDTO) {
        cardUpdateDTO.setId(id);
        return cardService.updateCard(cardUpdateDTO);
    }

    /**
     * 删除名片
     *
     * @param id 名片ID
     * @return 删除结果
     */
    @ApiOperation(value = "删除名片", notes = "删除指定ID的名片")
    @DeleteMapping("/{id}")
    @OperationLog(module = "名片管理", operationType = "删除", description = "删除名片")
    public Result<?> deleteCard(@PathVariable Long id) {
        return cardService.deleteCard(id);
    }

    /**
     * 获取名片详情
     *
     * @param id 名片ID
     * @return 名片详情
     */
    @ApiOperation(value = "获取名片详情", notes = "根据ID获取名片详情")
    @GetMapping("/{id}")
    @OperationLog(module = "名片管理", operationType = "查询", description = "查看名片详情")
    public Result<Card> getCardDetail(@PathVariable Long id) {
        return cardService.getCardDetail(id);
    }

    /**
     * 获取我的名片列表
     *
     * @return 名片列表
     */
    @ApiOperation(value = "获取我的名片列表", notes = "获取当前用户的所有名片")
    @GetMapping
    @OperationLog(module = "名片管理", operationType = "查询", description = "获取我的名片列表")
    public Result<List<Card>> getMyCards() {
        return cardService.getMyCardList();
    }

    /**
     * 获取名片分享链接
     *
     * @param id 名片ID
     * @return 分享链接
     */
    @ApiOperation(value = "获取名片分享链接", notes = "生成名片分享链接")
    @GetMapping("/{id}/share-link")
    @OperationLog(module = "名片管理", operationType = "分享", description = "获取名片分享链接")
    public Result<String> getShareLink(@PathVariable Long id) {
        return cardService.getShareLink(id);
    }

    /**
     * 访问名片
     *
     * @param id 名片ID
     * @return 访问结果
     */
    @ApiOperation(value = "访问名片", notes = "记录名片访问记录")
    @PostMapping("/{id}/visit")
    @OperationLog(module = "名片管理", operationType = "访问", description = "访问名片")
    public Result<?> visitCard(@PathVariable Long id) {
        return cardService.visitCard(id);
    }

    /**
     * 点赞名片
     *
     * @param id 名片ID
     * @return 点赞结果
     */
    @ApiOperation(value = "点赞名片", notes = "对名片进行点赞操作")
    @PostMapping("/{id}/like")
    @OperationLog(module = "名片管理", operationType = "点赞", description = "点赞名片")
    public Result<?> likeCard(@PathVariable Long id) {
        return cardService.likeCard(id);
    }

    /**
     * 取消点赞
     *
     * @param id 名片ID
     * @return 取消结果
     */
    @ApiOperation(value = "取消点赞", notes = "取消对名片的点赞")
    @DeleteMapping("/{id}/like")
    @OperationLog(module = "名片管理", operationType = "取消点赞", description = "取消名片点赞")
    public Result<?> unlikeCard(@PathVariable Long id) {
        return cardService.unlikeCard(id);
    }

    /**
     * 评论名片
     *
     * @param id 名片ID
     * @param content 评论内容
     * @return 评论结果
     */
    @ApiOperation(value = "评论名片", notes = "对名片发表评论")
    @ApiImplicitParam(name = "content", value = "评论内容", required = true, paramType = "query")
    @PostMapping("/{id}/comments")
    @OperationLog(module = "名片管理", operationType = "评论", description = "评论名片")
    public Result<?> commentCard(@PathVariable Long id, @RequestParam String content) {
        return cardService.commentCard(id, content);
    }

    /**
     * 获取名片评论列表
     *
     * @param id 名片ID
     * @return 评论列表
     */
    @ApiOperation(value = "获取名片评论列表", notes = "获取名片的所有评论")
    @GetMapping("/{id}/comments")
    @OperationLog(module = "名片管理", operationType = "查询", description = "获取名片评论列表")
    public Result<List<CardComment>> getCardComments(@PathVariable Long id) {
        return cardService.getCardComments(id);
    }

    /**
     * 获取名片访问记录
     *
     * @param id 名片ID
     * @return 访问记录
     */
    @ApiOperation(value = "获取名片访问记录", notes = "获取名片被访问的记录")
    @GetMapping("/{id}/visit-logs")
    @OperationLog(module = "名片管理", operationType = "查询", description = "获取名片访问记录")
    public Result<?> getCardVisitLogs(@PathVariable Long id) {
        return cardService.getVisitLog(id);
    }
}
