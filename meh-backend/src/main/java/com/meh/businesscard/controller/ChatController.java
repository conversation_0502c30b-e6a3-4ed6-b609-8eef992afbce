package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.ChatMessageDTO;
import com.meh.businesscard.entity.Chat;
import com.meh.businesscard.entity.ChatMessage;
import com.meh.businesscard.service.ChatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 在线咨询控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Api(tags = "在线咨询")
@RestController
@RequestMapping("/chats")
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * 获取我的会话列表
     *
     * @return 会话列表
     */
    @ApiOperation("获取我的会话列表")
    @GetMapping
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话列表")
    public Result<?> getMyChats() {
        return chatService.getChatList();
    }

    /**
     * 获取会话详情
     *
     * @param id 会话ID
     * @return 会话详情
     */
    @ApiOperation("获取会话详情")
    @ApiImplicitParam(name = "id", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @GetMapping("/{id}")
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话详情")
    public Result<?> getChatDetail(@PathVariable Long id) {
        return chatService.getChatDetail(id);
    }

    /**
     * 创建会话
     *
     * @param targetUserId 目标用户ID
     * @return 创建结果
     */
    @ApiOperation("创建会话")
    @ApiImplicitParam(name = "targetUserId", value = "目标用户ID", required = true, paramType = "query", dataTypeClass = Long.class)
    @PostMapping
    @OperationLog(module = "在线咨询", operationType = "新增", description = "创建会话")
    public Result<?> createChat(@RequestParam Long targetUserId) {
        return chatService.createChat(targetUserId);
    }

    /**
     * 获取会话消息列表
     *
     * @param chatId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    @ApiOperation("获取会话消息列表")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "chatId", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "page", value = "页码", paramType = "query", dataTypeClass = Integer.class),
        @ApiImplicitParam(name = "size", value = "每页大小", paramType = "query", dataTypeClass = Integer.class)
    })
    @GetMapping("/{chatId}/messages")
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话消息列表")
    public Result<?> getChatMessages(
            @PathVariable Long chatId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        return chatService.getChatMessageList(chatId, page, size);
    }

    /**
     * 发送消息
     *
     * @param messageDTO 消息参数
     * @return 发送结果
     */
    @ApiOperation("发送消息")
    @PostMapping("/messages")
    @OperationLog(module = "在线咨询", operationType = "新增", description = "发送消息")
    public Result<?> sendMessage(@RequestBody @Validated ChatMessageDTO messageDTO) {
        return chatService.sendMessage(messageDTO);
    }

    /**
     * 标记消息已读
     *
     * @param chatId 会话ID
     * @return 标记结果
     */
    @ApiOperation("标记消息已读")
    @ApiImplicitParam(name = "chatId", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @PutMapping("/{chatId}/read")
    @OperationLog(module = "在线咨询", operationType = "更新", description = "标记消息已读")
    public Result<?> markAsRead(@PathVariable Long chatId) {
        return chatService.markChatRead(chatId);
    }

    /**
     * 删除会话
     *
     * @param id 会话ID
     * @return 删除结果
     */
    @ApiOperation("删除会话")
    @ApiImplicitParam(name = "id", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @DeleteMapping("/{id}")
    @OperationLog(module = "在线咨询", operationType = "删除", description = "删除会话")
    public Result<?> deleteChat(@PathVariable Long id) {
        return chatService.deleteChat(id);
    }
}
