package com.meh.businesscard.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 联系人创建参数
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
@ApiModel(value = "联系人创建参数")
public class ContactCreateDTO {

    @ApiModelProperty(value = "联系人姓名", required = true, example = "张三")
    @NotBlank(message = "联系人姓名不能为空")
    @Length(max = 50, message = "联系人姓名长度不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "联系人职位", example = "产品经理")
    @Length(max = 100, message = "职位长度不能超过100个字符")
    private String position;

    @ApiModelProperty(value = "联系人公司", example = "码布斯科技有限公司")
    @Length(max = 100, message = "公司名称长度不能超过100个字符")
    private String company;

    @ApiModelProperty(value = "联系人电话", example = "13800138000")
    @Pattern(regexp = "^(1[3-9]\\d{9}|0\\d{2,3}-?\\d{7,8})$", message = "电话号码格式不正确")
    private String phone;

    @ApiModelProperty(value = "联系人邮箱", example = "<EMAIL>")
    @Email(message = "邮箱格式不正确")
    @Length(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @ApiModelProperty(value = "联系人地址", example = "北京市朝阳区xxx大厦")
    @Length(max = 200, message = "地址长度不能超过200个字符")
    private String address;

    @ApiModelProperty(value = "联系人头像", example = "https://example.com/avatar.jpg")
    @Length(max = 255, message = "头像URL长度不能超过255个字符")
    private String avatar;

    @ApiModelProperty(value = "联系人备注", example = "在某活动上认识")
    @Length(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    @ApiModelProperty(value = "分组ID", example = "1")
    private Long groupId;

    @ApiModelProperty(value = "标签，JSON格式存储")
    private String tags;

    @ApiModelProperty(value = "关联的名片ID")
    private Long cardId;
}
