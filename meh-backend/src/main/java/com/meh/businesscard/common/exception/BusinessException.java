package com.meh.businesscard.common.exception;

import com.meh.businesscard.common.api.IErrorCode;
import com.meh.businesscard.common.api.ResultCode;

/**
 * 业务异常类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private IErrorCode errorCode;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = ResultCode.BUSINESS_ERROR;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     */
    public BusinessException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param errorCode 错误码
     */
    public BusinessException(String message, IErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public IErrorCode getErrorCode() {
        return errorCode;
    }
}
