package com.meh.businesscard.common.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志切面
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Autowired
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 定义切点，拦截带有OperationLog注解的方法
     */
    @Pointcut("@annotation(com.meh.businesscard.common.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    /**
     * 前置通知，记录操作开始
     */
    @Before("operationLogPointcut()")
    public void doBefore(JoinPoint joinPoint) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

            // 记录请求开始时间和基本信息
            long startTime = System.currentTimeMillis();
            request.setAttribute("operation-start-time", startTime);

            // 获取请求路径
            String requestURI = request.getRequestURI();
            String requestMethod = request.getMethod();

            // 获取当前操作用户
            User currentUser = null;
            try {
                currentUser = userService.getCurrentUser();
            } catch (Exception e) {
                // 用户未登录或获取失败，不影响整体操作
            }

            // 获取操作日志注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog operationLog = method.getAnnotation(OperationLog.class);

            // 组装日志信息
            Map<String, Object> logInfo = new HashMap<>();
            logInfo.put("path", requestURI);
            logInfo.put("method", requestMethod);
            logInfo.put("module", operationLog.module());
            logInfo.put("operation", operationLog.operationType());
            logInfo.put("description", operationLog.description());
            logInfo.put("time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            if (currentUser != null) {
                logInfo.put("userId", currentUser.getId());
                logInfo.put("username", currentUser.getNickname());
            }

            log.info("操作开始: {}", objectMapper.writeValueAsString(logInfo));

        } catch (Exception e) {
            log.error("记录操作日志发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 后置通知，记录操作结果
     */
    @AfterReturning(pointcut = "operationLogPointcut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

            // 获取请求开始时间
            Long startTime = (Long) request.getAttribute("operation-start-time");
            if (startTime != null) {
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;

                // 获取操作日志注解信息
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                Method method = signature.getMethod();
                OperationLog operationLog = method.getAnnotation(OperationLog.class);

                // 组装日志信息
                Map<String, Object> logInfo = new HashMap<>();
                logInfo.put("module", operationLog.module());
                logInfo.put("operation", operationLog.operationType());
                logInfo.put("description", operationLog.description());
                logInfo.put("executionTime", executionTime + "ms");
                logInfo.put("status", "成功");

                log.info("操作作完成: {}", objectMapper.writeValueAsString(logInfo));
            }
        } catch (Exception e) {
            log.error("记录操作日志发生异常: {}", e.getMessage(), e);
        }
    }
}
