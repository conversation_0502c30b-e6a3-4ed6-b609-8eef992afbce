package com.meh.businesscard.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security 配置类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * 安全过滤器链配置
     *
     * @param http HttpSecurity对象
     * @return SecurityFilterChain 安全过滤器链
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF，因为使用JWT进行认证
            .csrf().disable()
            // 基于Token，不需要Session
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            // 请求授权配置
            .authorizeRequests()
            // 允许访问的公开接口
            .antMatchers("/auth/**", "/actuator/health", "/swagger-ui/**", "/swagger-resources/**", "/v3/api-docs/**").permitAll()
            // 其他所有请求需要认证
            .anyRequest().authenticated();

        return http.build();
    }

    // 密码编码器已在 PasswordEncoderConfig 中定义
}
