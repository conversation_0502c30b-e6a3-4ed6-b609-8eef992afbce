package com.meh.businesscard.common.annotation;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作描述
     */
    String description() default "";

    /**
     * 操作类型（如：查询、新增、修改、删除等）
     */
    String operationType() default "";

    /**
     * 操作模块
     */
    String module() default "";
}
