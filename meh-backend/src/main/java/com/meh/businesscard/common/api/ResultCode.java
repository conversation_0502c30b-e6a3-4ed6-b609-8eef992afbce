package com.meh.businesscard.common.api;

/**
 * API返回码枚举类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public enum ResultCode implements IErrorCode {
    
    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),
    
    /**
     * 操作失败
     */
    FAILED(500, "操作失败"),
    
    /**
     * 参数检验失败
     */
    VALIDATE_FAILED(400, "参数检验失败"),
    
    /**
     * 暂未登录或token已经过期
     */
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    
    /**
     * 没有相关权限
     */
    FORBIDDEN(403, "没有相关权限"),
    
    /**
     * 资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    
    /**
     * 服务器内部错误
     */
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    
    /**
     * 数据库操作失败
     */
    DATABASE_ERROR(501, "数据库操作失败"),
    
    /**
     * 重复操作
     */
    DUPLICATE_OPERATION(502, "重复操作"),
    
    /**
     * 业务处理失败
     */
    BUSINESS_ERROR(503, "业务处理失败");
    
    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    @Override
    public int getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
}
