package com.meh.businesscard.service.impl;

import com.meh.businesscard.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件服务实现
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${file.upload.path:/tmp/uploads}")
    private String uploadPath;

    @Value("${file.upload.domain:http://localhost:8080}")
    private String uploadDomain;

    @Override
    public String uploadImage(MultipartFile file) throws Exception {
        return uploadFile(file, "images");
    }

    @Override
    public String uploadAvatar(MultipartFile file) throws Exception {
        return uploadFile(file, "avatars");
    }

    @Override
    public String uploadCardBackground(MultipartFile file) throws Exception {
        return uploadFile(file, "card-backgrounds");
    }

    @Override
    public boolean deleteFile(String fileUrl) throws Exception {
        try {
            // 从URL中提取文件路径
            String filePath = fileUrl.replace(uploadDomain, "");
            Path path = Paths.get(uploadPath + filePath);
            
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("文件删除成功: {}", filePath);
                return true;
            } else {
                log.warn("文件不存在: {}", filePath);
                return false;
            }
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileUrl, e);
            throw new Exception("删除文件失败", e);
        }
    }

    @Override
    public Map<String, Object> getFileInfo(String fileUrl) throws Exception {
        try {
            // 从URL中提取文件路径
            String filePath = fileUrl.replace(uploadDomain, "");
            Path path = Paths.get(uploadPath + filePath);
            
            Map<String, Object> fileInfo = new HashMap<>();
            
            if (Files.exists(path)) {
                fileInfo.put("exists", true);
                fileInfo.put("size", Files.size(path));
                fileInfo.put("lastModified", Files.getLastModifiedTime(path).toString());
                fileInfo.put("contentType", Files.probeContentType(path));
            } else {
                fileInfo.put("exists", false);
            }
            
            return fileInfo;
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", fileUrl, e);
            throw new Exception("获取文件信息失败", e);
        }
    }

    /**
     * 通用文件上传方法
     *
     * @param file 文件
     * @param subDir 子目录
     * @return 文件URL
     * @throws Exception 上传异常
     */
    private String uploadFile(MultipartFile file, String subDir) throws Exception {
        try {
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            String fileName = UUID.randomUUID().toString() + extension;
            
            // 生成日期目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            
            // 创建完整的文件路径
            String relativePath = "/" + subDir + "/" + dateDir + "/" + fileName;
            String fullPath = uploadPath + relativePath;
            
            // 创建目录
            Path directory = Paths.get(fullPath).getParent();
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }
            
            // 保存文件
            Path filePath = Paths.get(fullPath);
            Files.copy(file.getInputStream(), filePath);
            
            // 返回访问URL
            String fileUrl = uploadDomain + relativePath;
            log.info("文件上传成功: {}", fileUrl);
            
            return fileUrl;
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new Exception("文件上传失败", e);
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    /**
     * 校验图片格式
     *
     * @param contentType 文件类型
     * @return 是否为图片
     */
    private boolean isImageFile(String contentType) {
        return contentType != null && contentType.startsWith("image/");
    }

    /**
     * 生成缩略图（可选实现）
     *
     * @param originalFile 原始文件
     * @param width 宽度
     * @param height 高度
     * @return 缩略图文件
     */
    private File generateThumbnail(File originalFile, int width, int height) {
        // TODO: 实现图片缩略图生成
        // 可以使用 Java 的 BufferedImage 或者集成第三方图片处理库
        return originalFile;
    }
}
