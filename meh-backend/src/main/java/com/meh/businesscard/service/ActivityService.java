package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.Activity;

import java.util.List;

/**
 * 活动服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ActivityService {

    /**
     * 获取活动列表
     *
     * @param type 活动类型
     * @param status 活动状态
     * @return 活动列表
     */
    Result<List<Activity>> getActivityList(Integer type, Integer status);

    /**
     * 获取活动详情
     *
     * @param id 活动ID
     * @return 活动详情
     */
    Result<Activity> getActivityDetail(Long id);

    /**
     * 参与活动
     *
     * @param id 活动ID
     * @return 参与结果
     */
    Result<?> joinActivity(Long id);

    /**
     * 获取推荐名片列表
     *
     * @return 推荐名片列表
     */
    Result<?> getRecommendCards();
}
