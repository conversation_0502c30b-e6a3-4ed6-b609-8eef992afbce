package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.Invite;

import java.util.List;

/**
 * 邀请服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface InviteService {

    /**
     * 生成邀请码
     *
     * @param activityId 活动ID
     * @return 邀请码
     */
    Result<String> generateInviteCode(Long activityId);

    /**
     * 使用邀请码
     *
     * @param inviteCode 邀请码
     * @return 使用结果
     */
    Result<?> useInviteCode(String inviteCode);

    /**
     * 获取我的邀请记录
     *
     * @param activityId 活动ID
     * @return 邀请记录列表
     */
    Result<List<Invite>> getMyInviteList(Long activityId);

    /**
     * 获取邀请统计
     *
     * @return 邀请统计信息
     */
    Result<?> getInviteStats();
}
