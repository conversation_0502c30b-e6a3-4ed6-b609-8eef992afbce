package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Spring Security用户详情服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 根据用户名查询用户信息
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getNickname, username)
                .eq(User::getDeleted, 0)
                .last("LIMIT 1"));

        if (user == null) {
            log.warn("用户不存在: {}", username);
            throw new UsernameNotFoundException("用户不存在");
        }

        // 构建用户权限列表
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        // 这里可以根据用户角色添加不同的权限，现在先添加一个基本权限
        authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

        // 返回UserDetails对象
        return new org.springframework.security.core.userdetails.User(
                user.getNickname(),
                user.getPassword(),
                user.getStatus() == 1, // enabled
                true, // accountNonExpired
                true, // credentialsNonExpired
                user.getStatus() != 2, // accountNonLocked
                authorities);
    }
}
