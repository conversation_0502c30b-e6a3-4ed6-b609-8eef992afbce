package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;
import com.meh.businesscard.common.exception.BusinessException;
import com.meh.businesscard.dto.UserLoginDTO;
import com.meh.businesscard.dto.UserRegisterDTO;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.entity.UserPointsLog;
import com.meh.businesscard.mapper.UserMapper;
import com.meh.businesscard.mapper.UserPointsMapper;
import com.meh.businesscard.mapper.UserPointsLogMapper;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.util.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.core.util.StrUtil;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserPointsMapper userPointsMapper;

    @Autowired
    private UserPointsLogMapper userPointsLogMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private HttpServletRequest request;

    @Value("${wechat.appid:}")
    private String wechatAppid;

    @Value("${wechat.secret:}")
    private String wechatSecret;

    @Override
    public Result<?> login(UserLoginDTO loginDTO) {
        // 根据用户名查询用户
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .and(wrapper -> wrapper
                        .eq(User::getPhone, loginDTO.getUsername())
                        .or()
                        .eq(User::getEmail, loginDTO.getUsername()))
                .eq(User::getStatus, 1)
                .eq(User::getDeleted, 0)
                .last("LIMIT 1"));

        // 用户不存在或密码错误
        if (user == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "用户不存在");
        }

        // 校验密码
        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "密码错误");
        }

        // 更新登录信息
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(getIpAddress(request));
        userMapper.updateById(user);

        // 生成token
        String token = generateToken(user);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);

        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> register(UserRegisterDTO registerDTO) {
        // 校验两次密码是否一致
        if (!Objects.equals(registerDTO.getPassword(), registerDTO.getConfirmPassword())) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "两次密码不一致");
        }

        // 校验手机号是否已注册
        User existUser = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getPhone, registerDTO.getPhone())
                .eq(User::getDeleted, 0)
                .last("LIMIT 1"));

        if (existUser != null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "手机号已注册");
        }

        // 创建用户
        User user = new User();
        user.setPhone(registerDTO.getPhone());
        user.setNickname(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setStatus(1);
        user.setLevel(0);
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());
        user.setLastLoginTime(LocalDateTime.now());
        user.setLastLoginIp(getIpAddress(request));

        // 保存用户
        userMapper.insert(user);

        // 初始化用户积分
        UserPoints userPoints = new UserPoints();
        userPoints.setUserId(user.getId());
        userPoints.setPoints(0);
        userPoints.setTotalPoints(0);
        userPoints.setUsedPoints(0);
        userPoints.setContinuousLoginDays(1);
        userPoints.setLastSignDate(LocalDateTime.now());
        userPoints.setCreateTime(LocalDateTime.now());
        userPoints.setUpdateTime(LocalDateTime.now());

        // 保存用户积分
        userPointsMapper.insert(userPoints);

        // 生成token
        String token = generateToken(user);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);

        return Result.success(result);
    }

    @Override
    public Result<?> wxLogin(String code) {
        if (!StringUtils.hasText(code)) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "code不能为空");
        }

        // 调用微信接口获取openid和session_key
        String url = StrUtil.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid={}&secret={}&js_code={}&grant_type=authorization_code",
                wechatAppid, wechatSecret, code);
        String resp = HttpUtil.get(url);
        JSONObject json = JSONUtil.parseObj(resp);
        String openid = json.getStr("openid");
        String unionid = json.getStr("unionid");
        String sessionKey = json.getStr("session_key");

        if (!StringUtils.hasText(openid)) {
            log.error("微信登录失败: {}", resp);
            return Result.failed("微信登录失败");
        }

        // 根据openid查询用户
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid)
                .eq(User::getDeleted, 0)
                .last("LIMIT 1"));

        boolean isNewUser = false;

        // 用户不存在，自动注册
        if (user == null) {
            isNewUser = true;
            user = new User();
            user.setOpenid(openid);
            user.setUnionid(unionid);
            user.setNickname("微信用户");
            user.setStatus(1);
            user.setLevel(0);
            user.setCreateTime(LocalDateTime.now());
            user.setUpdateTime(LocalDateTime.now());
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getIpAddress(request));

            // 保存用户
            userMapper.insert(user);

            // 初始化用户积分
            UserPoints userPoints = new UserPoints();
            userPoints.setUserId(user.getId());
            userPoints.setPoints(100); // 新用户注册奖励100积分
            userPoints.setTotalPoints(100);
            userPoints.setUsedPoints(0);
            userPoints.setContinuousLoginDays(1);
            userPoints.setLastSignDate(LocalDateTime.now());
            userPoints.setCreateTime(LocalDateTime.now());
            userPoints.setUpdateTime(LocalDateTime.now());

            // 保存用户积分
            userPointsMapper.insert(userPoints);

            // 记录积分获得日志
            UserPointsLog pointsLog = new UserPointsLog();
            pointsLog.setUserId(user.getId());
            pointsLog.setPoints(100);
            pointsLog.setType(1); // 1-增加
            pointsLog.setSource(3); // 3-完善信息（新用户注册）
            pointsLog.setDescription("新用户注册奖励");
            pointsLog.setBeforePoints(0);
            pointsLog.setAfterPoints(100);
            pointsLog.setCreateTime(LocalDateTime.now());
            userPointsLogMapper.insert(pointsLog);

        } else {
            // 更新登录信息
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(getIpAddress(request));
            userMapper.updateById(user);
        }

        // 生成token
        String token = generateToken(user);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", user);
        result.put("isNewUser", isNewUser);
        result.put("sessionKey", sessionKey); // 返回session_key供前端获取手机号使用

        return Result.success(result);
    }

    @Override
    public Result<?> bindWxPhone(String encryptedData, String iv, String sessionKey) {
        try {
            // TODO: 实现微信手机号解密逻辑
            // 这里需要使用微信提供的解密算法来解密手机号
            // 由于涉及到复杂的加密解密，这里先提供一个简化的实现

            // 获取当前用户
            User currentUser = getCurrentUser();

            // 模拟解密后的手机号（实际项目中需要真正的解密）
            String phoneNumber = "13800138000"; // 这里应该是解密后的真实手机号

            // 更新用户手机号
            currentUser.setPhone(phoneNumber);
            userMapper.updateById(currentUser);

            Map<String, Object> result = new HashMap<>();
            result.put("phone", phoneNumber);
            result.put("message", "手机号绑定成功");

            return Result.success(result);

        } catch (Exception e) {
            log.error("绑定微信手机号失败: {}", e.getMessage(), e);
            return Result.failed("绑定手机号失败");
        }
    }

    @Override
    public User getById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public boolean updateById(User user) {
        return userMapper.updateById(user) > 0;
    }

    @Override
    public User getCurrentUser() {
        // 从SecurityContextHolder中获取用户信息
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        String username = authentication.getName();
        if (!StringUtils.hasText(username)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 根据用户名查询用户
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                .and(wrapper -> wrapper
                        .eq(User::getPhone, username)
                        .or()
                        .eq(User::getEmail, username))
                .eq(User::getStatus, 1)
                .eq(User::getDeleted, 0)
                .last("LIMIT 1"));

        if (user == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        return user;
    }

    /**
     * 用户退出登录
     *
     * @return 退出结果
     */
    @Override
    public Result<?> logout() {
        try {
            // 清除Spring Security上下文中的认证信息
            SecurityContextHolder.clearContext();

            // 在实际应用中，这里可能还需要以下操作：
            // 1. 将当前token加入黑名单（需要额外的Redis存储）
            // 2. 记录用户退出日志

            log.info("用户退出登录成功");
            return Result.success("退出登录成功");
        } catch (Exception e) {
            log.error("退出登录失败: {}", e.getMessage(), e);
            return Result.failed("退出登录失败");
        }
    }

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    @Override
    public Result<?> refreshToken(String refreshToken) {
        try {
            // 验证刷新令牌是否有效
            if (!StringUtils.hasText(refreshToken)) {
                return Result.failed(ResultCode.UNAUTHORIZED.getCode(), "刷新令牌不能为空");
            }

            // 从刷新令牌中提取用户名
            String username = "";
            try {
                username = jwtTokenUtil.getUsernameFromToken(refreshToken);
            } catch (Exception e) {
                return Result.failed(ResultCode.UNAUTHORIZED.getCode(), "无效的刷新令牌");
            }

            if (!StringUtils.hasText(username)) {
                return Result.failed(ResultCode.UNAUTHORIZED.getCode(), "无效的刷新令牌");
            }

            // 加载用户详情
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);

            // 验证令牌是否有效
            if (!jwtTokenUtil.validateToken(refreshToken, userDetails)) {
                return Result.failed(ResultCode.UNAUTHORIZED.getCode(), "刷新令牌已过期");
            }

            // 生成新的访问令牌
            String newToken = jwtTokenUtil.generateToken(userDetails.getUsername());

            // 返回新令牌
            Map<String, Object> result = new HashMap<>();
            result.put("token", newToken);
            result.put("refreshToken", refreshToken);  // 刷新令牌通常有更长的有效期，这里可以选择是否也更新刷新令牌

            log.info("用户[{}]刷新令牌成功", username);
            return Result.success(result);

        } catch (Exception e) {
            log.error("刷新令牌失败: {}", e.getMessage(), e);
            return Result.failed("刷新令牌失败");
        }
    }

    /**
     * 生成JWT令牌
     *
     * @param user 用户信息
     * @return JWT令牌
     */
    private String generateToken(User user) {
        // 根据实际情况，使用nickname、phone或email作为用户标识
        String username = user.getNickname();
        if (username == null || username.isEmpty()) {
            username = user.getPhone();
        }
        return jwtTokenUtil.generateToken(username);
    }

    /**
     * 获取请求IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
