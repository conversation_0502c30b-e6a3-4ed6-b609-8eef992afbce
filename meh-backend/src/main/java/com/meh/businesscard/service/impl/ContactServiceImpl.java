package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;
import com.meh.businesscard.common.exception.BusinessException;
import com.meh.businesscard.dto.ContactCreateDTO;
import com.meh.businesscard.dto.ContactUpdateDTO;
import com.meh.businesscard.entity.Contact;
import com.meh.businesscard.entity.ContactGroup;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.ContactGroupMapper;
import com.meh.businesscard.mapper.ContactMapper;
import com.meh.businesscard.service.ContactService;
import com.meh.businesscard.service.UserPointsService;
import com.meh.businesscard.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通讯录服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class ContactServiceImpl extends ServiceImpl<ContactMapper, Contact> implements ContactService {

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private ContactGroupMapper contactGroupMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createContact(ContactCreateDTO contactCreateDTO) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验分组是否存在
        if (contactCreateDTO.getGroupId() != null) {
            ContactGroup group = contactGroupMapper.selectById(contactCreateDTO.getGroupId());
            if (group == null || !group.getUserId().equals(currentUser.getId())) {
                return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组不存在");
            }
        }

        // 创建联系人
        Contact contact = new Contact();
        BeanUtils.copyProperties(contactCreateDTO, contact);
        contact.setUserId(currentUser.getId());
        contact.setIsStarred(0);
        contact.setCreateTime(LocalDateTime.now());
        contact.setUpdateTime(LocalDateTime.now());

        // 保存联系人
        contactMapper.insert(contact);

        // 添加积分（首次添加联系人）
        long count = contactMapper.selectCount(new LambdaQueryWrapper<Contact>()
            .eq(Contact::getUserId, currentUser.getId())
            .eq(Contact::getDeleted, 0));

        if (count == 1) {
            userPointsService.addPoints(currentUser.getId(), 10, 5, "首次添加联系人");
        } else {
            userPointsService.addPoints(currentUser.getId(), 2, 5, "添加联系人");
        }

        return Result.success(contact);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateContact(ContactUpdateDTO contactUpdateDTO) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验联系人是否存在
        Contact contact = contactMapper.selectById(contactUpdateDTO.getId());
        if (contact == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "联系人不存在");
        }

        // 校验联系人是否属于当前用户
        if (!contact.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此联系人");
        }

        // 校验分组是否存在
        if (contactUpdateDTO.getGroupId() != null) {
            ContactGroup group = contactGroupMapper.selectById(contactUpdateDTO.getGroupId());
            if (group == null || !group.getUserId().equals(currentUser.getId())) {
                return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组不存在");
            }
        }

        // 更新联系人
        BeanUtils.copyProperties(contactUpdateDTO, contact);
        contact.setUpdateTime(LocalDateTime.now());

        // 保存联系人
        contactMapper.updateById(contact);

        return Result.success(contact);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteContact(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验联系人是否存在
        Contact contact = contactMapper.selectById(id);
        if (contact == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "联系人不存在");
        }

        // 校验联系人是否属于当前用户
        if (!contact.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此联系人");
        }

        // ���除联系人
        contactMapper.deleteById(id);

        return Result.success();
    }

    @Override
    public Result<Contact> getContactDetail(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验联系人是否存在
        Contact contact = contactMapper.selectById(id);
        if (contact == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "联系人不存在");
        }

        // 校验联系人是否属于当前用户
        if (!contact.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权查看此联系人");
        }

        return Result.success(contact);
    }

    @Override
    public Result<List<Contact>> getMyContactList(String keyword, Long groupId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 构建查询条件
        LambdaQueryWrapper<Contact> queryWrapper = new LambdaQueryWrapper<Contact>()
            .eq(Contact::getUserId, currentUser.getId())
            .eq(Contact::getDeleted, 0);

        // 添加关键词搜索条件
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(Contact::getName, keyword)
                .or()
                .like(Contact::getPhone, keyword)
                .or()
                .like(Contact::getCompany, keyword)
                .or()
                .like(Contact::getPosition, keyword)
                .or()
                .like(Contact::getEmail, keyword)
                .or()
                .like(Contact::getRemark, keyword));
        }

        // 添加分组条件
        if (groupId != null) {
            queryWrapper.eq(Contact::getGroupId, groupId);
        }

        // 排序：星标优先，然后按创建时间倒序
        queryWrapper.orderByDesc(Contact::getIsStarred)
            .orderByDesc(Contact::getCreateTime);

        // 查询联系人列表
        List<Contact> contactList = contactMapper.selectList(queryWrapper);

        return Result.success(contactList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> starContact(Long id, Integer isStarred) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验联系人是否存在
        Contact contact = contactMapper.selectById(id);
        if (contact == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "联系人不存在");
        }

        // 校验联系人是否属于当前用户
        if (!contact.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此联系人");
        }

        // 设置星标状态
        contact.setIsStarred(isStarred);
        contact.setUpdateTime(LocalDateTime.now());

        // 保存联系人
        contactMapper.updateById(contact);

        return Result.success();
    }

    @Override
    public Result<?> getContactGroupList() {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 查询分组列表
        List<ContactGroup> groupList = contactGroupMapper.selectList(new LambdaQueryWrapper<ContactGroup>()
            .eq(ContactGroup::getUserId, currentUser.getId())
            .eq(ContactGroup::getDeleted, 0)
            .orderByAsc(ContactGroup::getSort));

        return Result.success(groupList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createContactGroup(String name) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验分组名称
        if (!StringUtils.hasText(name)) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组名称不能为空");
        }

        // 校验分组名称是否重复
        ContactGroup existGroup = contactGroupMapper.selectOne(new LambdaQueryWrapper<ContactGroup>()
            .eq(ContactGroup::getUserId, currentUser.getId())
            .eq(ContactGroup::getName, name)
            .eq(ContactGroup::getDeleted, 0)
            .last("LIMIT 1"));

        if (existGroup != null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组名称已存在");
        }

        // 创建分组
        ContactGroup group = new ContactGroup();
        group.setUserId(currentUser.getId());
        group.setName(name);
        group.setSort(0);
        group.setCreateTime(LocalDateTime.now());
        group.setUpdateTime(LocalDateTime.now());

        // 保存分组
        contactGroupMapper.insert(group);

        return Result.success(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateContactGroup(Long id, String name) {
        // 获取��前用户
        User currentUser = userService.getCurrentUser();

        // 校验分组是否存在
        ContactGroup group = contactGroupMapper.selectById(id);
        if (group == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组不存在");
        }

        // 校验分组是否属于当前用户
        if (!group.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此分组");
        }

        // 校验分组名称
        if (!StringUtils.hasText(name)) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组名称不能为空");
        }

        // 校验分组名称是否重复
        ContactGroup existGroup = contactGroupMapper.selectOne(new LambdaQueryWrapper<ContactGroup>()
            .eq(ContactGroup::getUserId, currentUser.getId())
            .eq(ContactGroup::getName, name)
            .ne(ContactGroup::getId, id)
            .eq(ContactGroup::getDeleted, 0)
            .last("LIMIT 1"));

        if (existGroup != null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组名称已存在");
        }

        // 更新分组
        group.setName(name);
        group.setUpdateTime(LocalDateTime.now());

        // 保存分组
        contactGroupMapper.updateById(group);

        return Result.success(group);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteContactGroup(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验分组是否存在
        ContactGroup group = contactGroupMapper.selectById(id);
        if (group == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "分组不存在");
        }

        // 校验分组是否属于当前用户
        if (!group.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此分组");
        }

        // 删除分组
        contactGroupMapper.deleteById(id);

        // 将该分组下的联系人移出分组
        contactMapper.update(null, new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<Contact>()
            .eq(Contact::getUserId, currentUser.getId())
            .eq(Contact::getGroupId, id)
            .set(Contact::getGroupId, null));

        return Result.success();
    }
}
