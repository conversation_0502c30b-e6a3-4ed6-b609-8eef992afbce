package com.meh.businesscard.service;

import com.meh.businesscard.entity.PointsExchange;

/**
 * 积分兑换服务接口
 */
public interface PointsExchangeService {

    /**
     * 兑换积分商品
     * @param userId 用户ID
     * @param goodsId 商品ID
     * @param quantity 数量
     * @return 兑换记录
     */
    PointsExchange exchange(Long userId, Long goodsId, Integer quantity);

    /**
     * 根据ID获取兑换记录
     */
    PointsExchange getById(Long id);
}
