package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.UserLoginDTO;
import com.meh.businesscard.dto.UserRegisterDTO;
import com.meh.businesscard.entity.User;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface UserService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    Result<?> login(UserLoginDTO loginDTO);

    /**
     * 用户注册
     *
     * @param registerDTO 注册参数
     * @return 注册结果
     */
    Result<?> register(UserRegisterDTO registerDTO);

    /**
     * 微信小程序登录
     *
     * @param code 微信授权码
     * @return 登录结果
     */
    Result<?> wxLogin(String code);

    /**
     * 绑定微信手机号
     *
     * @param encryptedData 加密数据
     * @param iv 初始向量
     * @param sessionKey 会话密钥
     * @return 绑定结果
     */
    Result<?> bindWxPhone(String encryptedData, String iv, String sessionKey);

    /**
     * 根据ID获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User getById(Long id);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
    boolean updateById(User user);

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    User getCurrentUser();

    /**
     * 用户退出登录
     *
     * @return 退出结果
     */
    Result<?> logout();

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    Result<?> refreshToken(String refreshToken);
}
