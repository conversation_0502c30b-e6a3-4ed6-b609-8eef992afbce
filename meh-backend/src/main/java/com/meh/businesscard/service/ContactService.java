package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.ContactCreateDTO;
import com.meh.businesscard.dto.ContactUpdateDTO;
import com.meh.businesscard.entity.Contact;

import java.util.List;

/**
 * 通讯录服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ContactService {

    /**
     * 创建联系人
     *
     * @param contactCreateDTO 联系人创建参数
     * @return 创建结果
     */
    Result<?> createContact(ContactCreateDTO contactCreateDTO);

    /**
     * 更新联系人
     *
     * @param contactUpdateDTO 联系人更新参数
     * @return 更新结果
     */
    Result<?> updateContact(ContactUpdateDTO contactUpdateDTO);

    /**
     * 删除联系人
     *
     * @param id 联系人ID
     * @return 删除结果
     */
    Result<?> deleteContact(Long id);

    /**
     * 获取联系人详情
     *
     * @param id 联系人ID
     * @return 联系人详情
     */
    Result<Contact> getContactDetail(Long id);

    /**
     * 获取我的联系人列表
     *
     * @param keyword 搜索关键词
     * @param groupId 分组ID
     * @return 联系人列表
     */
    Result<List<Contact>> getMyContactList(String keyword, Long groupId);

    /**
     * 设置星标联系人
     *
     * @param id 联系人ID
     * @param isStarred 是否星标：0-否，1-是
     * @return 设置结果
     */
    Result<?> starContact(Long id, Integer isStarred);

    /**
     * 获取联系人分组列表
     *
     * @return 分组列表
     */
    Result<?> getContactGroupList();

    /**
     * 创建联系人分组
     *
     * @param name 分组名称
     * @return 创建结果
     */
    Result<?> createContactGroup(String name);

    /**
     * 更新联系人分组
     *
     * @param id 分组ID
     * @param name 分组名称
     * @return 更新结果
     */
    Result<?> updateContactGroup(Long id, String name);

    /**
     * 删除联系人分组
     *
     * @param id 分组ID
     * @return 删除结果
     */
    Result<?> deleteContactGroup(Long id);
}
