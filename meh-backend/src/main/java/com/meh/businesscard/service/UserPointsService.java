package com.meh.businesscard.service;

import com.meh.businesscard.entity.UserPoints;

/**
 * 用户积分服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface UserPointsService {
    
    /**
     * 获取用户积分信息
     * 
     * @param userId 用户ID
     * @return 用户积分信息
     */
    UserPoints getUserPoints(Long userId);
    
    /**
     * 增加用户积分
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param description 积分描述
     * @return 是否成功
     */
    boolean addPoints(Long userId, Integer points, String description);
    
    /**
     * 增加用户积分（带来源）
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param source 积分来源：1-每日登录，2-签到，3-完善信息，4-社交互动，5-邀请好友，6-活动参与，7-积分兑换，8-管理员操作
     * @param description 积分描述
     * @return 是否成功
     */
    boolean addPoints(Long userId, Integer points, Integer source, String description);
    
    /**
     * 减少用户积分
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param description 积分描述
     * @return 是否成功
     */
    boolean reducePoints(Long userId, Integer points, String description);
    
    /**
     * 减少用户积分（带来源）
     * 
     * @param userId 用户ID
     * @param points 积分数量
     * @param source 积分来源：1-每日登录，2-签到，3-完善信息，4-社交互动，5-邀请好友，6-活动参与，7-积分兑换，8-管理员操作
     * @param description 积分描述
     * @return 是否成功
     */
    boolean reducePoints(Long userId, Integer points, Integer source, String description);
    
    /**
     * 管理员操作用户积分
     * 
     * @param userId 用户ID
     * @param points 积分数量（正数增加，负数减少）
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param description 积分描述
     * @return 是否成功
     */
    boolean operatePoints(Long userId, Integer points, Long operatorId, String operatorName, String description);
    
    /**
     * 每日签到
     * 
     * @param userId 用户ID
     * @return 签到结果
     */
    boolean dailySignIn(Long userId);
    
    /**
     * 获取用户积分变动记录
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 积分变动记录
     */
    Object getPointsLog(Long userId, Integer page, Integer size);
}
