package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;
import com.meh.businesscard.common.exception.BusinessException;
import com.meh.businesscard.dto.ChatMessageDTO;
import com.meh.businesscard.entity.Chat;
import com.meh.businesscard.entity.ChatMessage;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.ChatMapper;
import com.meh.businesscard.mapper.ChatMessageMapper;
import com.meh.businesscard.mapper.UserMapper;
import com.meh.businesscard.service.ChatService;
import com.meh.businesscard.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 在线咨询服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class ChatServiceImpl extends ServiceImpl<ChatMapper, Chat> implements ChatService {

    @Autowired
    private ChatMapper chatMapper;

    @Autowired
    private ChatMessageMapper chatMessageMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Override
    public Result<?> getChatList() {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 查询会话列表 - 使用标准方法替代不存在的自定义方法
        List<Chat> chatList = chatMapper.selectList(new LambdaQueryWrapper<Chat>()
                .and(wrapper -> wrapper
                        .eq(Chat::getFromUserId, currentUser.getId())
                        .or()
                        .eq(Chat::getToUserId, currentUser.getId())
                )
                .eq(Chat::getDeleted, 0)
                .orderByDesc(Chat::getLastMessageTime));

        // 构建包含用户信息的结果集
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Chat chat : chatList) {
            Map<String, Object> chatMap = new HashMap<>();
            chatMap.put("id", chat.getId());
            chatMap.put("lastMessage", chat.getLastMessage());
            chatMap.put("lastMessageTime", chat.getLastMessageTime());
            chatMap.put("unreadCount", chat.getUnreadCount());

            // 获取对方用户信息
            Long targetUserId = chat.getFromUserId().equals(currentUser.getId()) ?
                    chat.getToUserId() : chat.getFromUserId();
            User targetUser = userMapper.selectById(targetUserId);

            if (targetUser != null) {
                chatMap.put("targetUserId", targetUser.getId());
                chatMap.put("targetNickname", targetUser.getNickname());
                chatMap.put("targetAvatar", targetUser.getAvatar());
            }

            resultList.add(chatMap);
        }

        return Result.success(resultList);
    }

    @Override
    public Result<?> getChatDetail(Long chatId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验会话是否存在
        Chat chat = chatMapper.selectById(chatId);
        if (chat == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "会话不存在");
        }

        // 校验会话是否属于当前用户
        if (!chat.getFromUserId().equals(currentUser.getId()) && !chat.getToUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权查看此会话");
        }

        // 查询会话详情及相关用户信息
        Map<String, Object> chatDetail = new HashMap<>();
        chatDetail.put("id", chat.getId());
        chatDetail.put("lastMessage", chat.getLastMessage());
        chatDetail.put("lastMessageTime", chat.getLastMessageTime());
        chatDetail.put("unreadCount", chat.getUnreadCount());

        // 获取对方用户信息
        Long targetUserId = chat.getFromUserId().equals(currentUser.getId()) ?
                chat.getToUserId() : chat.getFromUserId();
        User targetUser = userMapper.selectById(targetUserId);

        if (targetUser != null) {
            chatDetail.put("targetUserId", targetUser.getId());
            chatDetail.put("targetNickname", targetUser.getNickname());
            chatDetail.put("targetAvatar", targetUser.getAvatar());
        }

        return Result.success(chatDetail);
    }

    @Override
    public Result<?> getChatMessageList(Long chatId, Integer page, Integer size) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校��会话是否存在
        Chat chat = chatMapper.selectById(chatId);
        if (chat == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "会话不存在");
        }

        // 校验会话是否属于当前用户
        if (!chat.getFromUserId().equals(currentUser.getId()) && !chat.getToUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权查看此会话");
        }

        // 分页查询消息列表
        IPage<ChatMessage> pageResult = new Page<>(page, size);
        IPage<ChatMessage> messagePage = chatMessageMapper.selectPage(pageResult, new LambdaQueryWrapper<ChatMessage>()
            .eq(ChatMessage::getChatId, chatId)
            .eq(ChatMessage::getDeleted, 0)
            .orderByDesc(ChatMessage::getCreateTime));

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", messagePage.getTotal());
        result.put("pages", messagePage.getPages());
        result.put("current", messagePage.getCurrent());
        result.put("size", messagePage.getSize());
        result.put("records", messagePage.getRecords());

        return Result.success(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> sendMessage(ChatMessageDTO chatMessageDTO) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验会话是否存在
        Chat chat = chatMapper.selectById(chatMessageDTO.getChatId());
        if (chat == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "会话不存在");
        }

        // 校验会话是否属于当前用户
        if (!chat.getFromUserId().equals(currentUser.getId()) && !chat.getToUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权发送消息");
        }

        // 创建消息
        ChatMessage message = new ChatMessage();
        message.setChatId(chatMessageDTO.getChatId());
        message.setFromUserId(currentUser.getId());
        message.setToUserId(chat.getFromUserId().equals(currentUser.getId()) ? chat.getToUserId() : chat.getFromUserId());
        message.setContent(chatMessageDTO.getContent());
        message.setType(chatMessageDTO.getType());
        message.setMediaUrl(chatMessageDTO.getMediaUrl());
        // ChatMessage 中没有 duration 字段，注释掉这行
        // message.setDuration(chatMessageDTO.getDuration() != null ? chatMessageDTO.getDuration() : 0);
        message.setIsRead(0);
        message.setCreateTime(LocalDateTime.now());

        // 保存消息
        chatMessageMapper.insert(message);

        // 更新会话最后一条消息
        // 替换不存在的方法调用
        chat.setLastMessage(message.getContent());
        chat.setLastMessageTime(message.getCreateTime());

        // 更新未读消息数
        // 简化未读消息数处理，使用单一未读计数
        if (!chat.getFromUserId().equals(currentUser.getId())) {
            // 当消息发送给 fromUserId 时增加未读数
            chat.setUnreadCount(chat.getUnreadCount() != null ? chat.getUnreadCount() + 1 : 1);
        }

        // 保存会话
        chatMapper.updateById(chat);

        return Result.success(message);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createChat(Long targetUserId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验目标用户是否存在
        User targetUser = userMapper.selectById(targetUserId);
        if (targetUser == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "目标用户不存在");
        }

        // 校验是否已存在会话
        Chat existChat = chatMapper.selectOne(new LambdaQueryWrapper<Chat>()
            .nested(wrapper -> wrapper
                .eq(Chat::getFromUserId, currentUser.getId())
                .eq(Chat::getToUserId, targetUserId))
            .or(wrapper -> wrapper
                .eq(Chat::getFromUserId, targetUserId)
                .eq(Chat::getToUserId, currentUser.getId()))
            .eq(Chat::getDeleted, 0)
            .last("LIMIT 1"));

        if (existChat != null) {
            return Result.success(existChat);
        }

        // 创建会话
        Chat chat = new Chat();
        chat.setFromUserId(currentUser.getId());
        chat.setToUserId(targetUserId);
        chat.setUnreadCount(0);
        chat.setCreateTime(LocalDateTime.now());
        chat.setUpdateTime(LocalDateTime.now());

        // 保存会话
        chatMapper.insert(chat);

        return Result.success(chat);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> markChatRead(Long chatId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验会话是否存在
        Chat chat = chatMapper.selectById(chatId);
        if (chat == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "会话不存在");
        }

        // 校验会话是否属于当前用���
        if (!chat.getFromUserId().equals(currentUser.getId()) && !chat.getToUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此会话");
        }

        // 更新未读消息数
        chat.setUnreadCount(0);

        // 保存会话
        chatMapper.updateById(chat);

        // 更新消息已读状态
        chatMessageMapper.update(null, new LambdaUpdateWrapper<ChatMessage>()
            .eq(ChatMessage::getChatId, chatId)
            .eq(ChatMessage::getToUserId, currentUser.getId())
            .eq(ChatMessage::getIsRead, 0)
            .set(ChatMessage::getIsRead, 1));

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteChat(Long chatId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验会话是否存在
        Chat chat = chatMapper.selectById(chatId);
        if (chat == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "会话不存在");
        }

        // 校验会话是否属于当前用户
        if (!chat.getFromUserId().equals(currentUser.getId()) && !chat.getToUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权删除此会话");
        }

        // 逻辑删除会话
        chat.setDeleted(1);
        chat.setUpdateTime(LocalDateTime.now());
        chatMapper.updateById(chat);

        // 逻辑删除会话中的消息（将所有属于此会话的消息标记为已删除）
        ChatMessage updateMessage = new ChatMessage();
        updateMessage.setDeleted(1);
        //updateMessage.setUpdateTime(LocalDateTime.now());
        chatMessageMapper.update(updateMessage, new LambdaUpdateWrapper<ChatMessage>()
                .eq(ChatMessage::getChatId, chatId)
                .eq(ChatMessage::getDeleted, 0));

        log.info("用户[{}]删除会话[{}]成功", currentUser.getId(), chatId);
        return Result.success("删除会话成功");
    }
}

