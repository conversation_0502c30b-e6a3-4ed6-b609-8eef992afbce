package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.ChatMessageDTO;

/**
 * 在线咨询服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface ChatService {

    /**
     * 获取我的会话列表
     *
     * @return 会话列表
     */
    Result<?> getChatList();

    /**
     * 获取会话详情
     *
     * @param chatId 会话ID
     * @return 会话详情
     */
    Result<?> getChatDetail(Long chatId);

    /**
     * 获取会话消息列表
     *
     * @param chatId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    Result<?> getChatMessageList(Long chatId, Integer page, Integer size);

    /**
     * 发送消息
     *
     * @param chatMessageDTO 消息参数
     * @return 发送结果
     */
    Result<?> sendMessage(ChatMessageDTO chatMessageDTO);

    /**
     * 创建会话
     *
     * @param targetUserId 目标用户ID
     * @return 创建结果
     */
    Result<?> createChat(Long targetUserId);

    /**
     * 标记会话已读
     *
     * @param chatId 会话ID
     * @return 标记结果
     */
    Result<?> markChatRead(Long chatId);

    /**
     * 删除会话
     *
     * @param chatId 会话ID
     * @return 删除结果
     */
    Result<?> deleteChat(Long chatId);
}
