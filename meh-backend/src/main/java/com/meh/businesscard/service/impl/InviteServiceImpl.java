package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;
import com.meh.businesscard.entity.Activity;
import com.meh.businesscard.entity.Invite;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.ActivityMapper;
import com.meh.businesscard.mapper.InviteMapper;
import com.meh.businesscard.service.InviteService;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.service.UserPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 邀请服务实现
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class InviteServiceImpl extends ServiceImpl<InviteMapper, Invite> implements InviteService {

    @Autowired
    private InviteMapper inviteMapper;

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> generateInviteCode(Long activityId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验活动是否存在
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null || activity.getDeleted() == 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动不存在");
        }

        // 校验活动状态
        if (activity.getStatus() != 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动未开始或已结束");
        }

        // 检查是否已经生成过邀请码
        Invite existInvite = inviteMapper.selectOne(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterId, currentUser.getId())
                .eq(Invite::getActivityId, activityId)
                .eq(Invite::getDeleted, 0)
                .last("LIMIT 1"));

        if (existInvite != null) {
            return Result.success(existInvite.getInviteCode());
        }

        // 生成邀请码
        String inviteCode = generateUniqueInviteCode();

        // 创建邀请记录
        Invite invite = new Invite();
        invite.setInviterId(currentUser.getId());
        invite.setActivityId(activityId);
        invite.setInviteCode(inviteCode);
        invite.setStatus(0);
        invite.setRewardStatus(0);
        invite.setRewardPoints(50); // 默认奖励50积分
        invite.setCreateTime(LocalDateTime.now());
        invite.setUpdateTime(LocalDateTime.now());

        inviteMapper.insert(invite);

        return Result.success(inviteCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> useInviteCode(String inviteCode) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验邀请码是否存在
        Invite invite = inviteMapper.selectOne(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviteCode, inviteCode)
                .eq(Invite::getDeleted, 0)
                .last("LIMIT 1"));

        if (invite == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "邀请码不存在");
        }

        // 校验邀请码状态
        if (invite.getStatus() == 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "邀请码已被使用");
        }

        // 不能使用自己的邀请码
        if (invite.getInviterId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "不能使用自己的邀请码");
        }

        // 校验活动是否有效
        Activity activity = activityMapper.selectById(invite.getActivityId());
        if (activity == null || activity.getDeleted() == 1 || activity.getStatus() != 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动已结束或不存在");
        }

        // 更新邀请记录
        invite.setInviteeId(currentUser.getId());
        invite.setStatus(1);
        invite.setUpdateTime(LocalDateTime.now());
        inviteMapper.updateById(invite);

        // 给邀请人发放奖励
        if (invite.getRewardStatus() == 0) {
            userPointsService.addPoints(invite.getInviterId(), invite.getRewardPoints(), 5, "邀请好友奖励");
            invite.setRewardStatus(1);
            inviteMapper.updateById(invite);
        }

        // 给被邀请人发放奖励
        userPointsService.addPoints(currentUser.getId(), 20, 5, "接受邀请奖励");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "使用邀请码成功！");
        result.put("points", 20);
        result.put("activity", activity);

        return Result.success(result);
    }

    @Override
    public Result<List<Invite>> getMyInviteList(Long activityId) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 构建查询条件
        LambdaQueryWrapper<Invite> queryWrapper = new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterId, currentUser.getId())
                .eq(Invite::getDeleted, 0);

        // 添加活动ID条件
        if (activityId != null) {
            queryWrapper.eq(Invite::getActivityId, activityId);
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Invite::getCreateTime);

        List<Invite> inviteList = inviteMapper.selectList(queryWrapper);
        return Result.success(inviteList);
    }

    @Override
    public Result<?> getInviteStats() {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 统计邀请数据
        Long totalInvites = inviteMapper.selectCount(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterId, currentUser.getId())
                .eq(Invite::getDeleted, 0));

        Long successInvites = inviteMapper.selectCount(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterId, currentUser.getId())
                .eq(Invite::getStatus, 1)
                .eq(Invite::getDeleted, 0));

        Long totalRewardPoints = inviteMapper.selectList(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterId, currentUser.getId())
                .eq(Invite::getRewardStatus, 1)
                .eq(Invite::getDeleted, 0))
                .stream()
                .mapToLong(Invite::getRewardPoints)
                .sum();

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalInvites", totalInvites);
        stats.put("successInvites", successInvites);
        stats.put("totalRewardPoints", totalRewardPoints);
        stats.put("successRate", totalInvites > 0 ? (double) successInvites / totalInvites * 100 : 0);

        return Result.success(stats);
    }

    /**
     * 生成唯一邀请码
     *
     * @return 邀请码
     */
    private String generateUniqueInviteCode() {
        String inviteCode;
        do {
            // 生成8位随机邀请码
            inviteCode = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        } while (inviteMapper.selectCount(new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviteCode, inviteCode)) > 0);
        
        return inviteCode;
    }
}
