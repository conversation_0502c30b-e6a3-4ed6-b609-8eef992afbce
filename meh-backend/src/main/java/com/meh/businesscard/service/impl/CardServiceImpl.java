package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;

import com.meh.businesscard.dto.CardCreateDTO;
import com.meh.businesscard.dto.CardUpdateDTO;
import com.meh.businesscard.entity.*;
import com.meh.businesscard.mapper.*;
import com.meh.businesscard.service.CardService;
import com.meh.businesscard.service.UserPointsService;
import com.meh.businesscard.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;

/**
 * 名片服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class CardServiceImpl extends ServiceImpl<CardMapper, Card> implements CardService {

    @Autowired
    private CardMapper cardMapper;

    @Autowired
    private CardTemplateMapper cardTemplateMapper;

    @Autowired
    private CardVisitLogMapper cardVisitLogMapper;

    @Autowired
    private CardLikeMapper cardLikeMapper;

    @Autowired
    private CardCommentMapper cardCommentMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    @Autowired
    private HttpServletRequest request;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> createCard(CardCreateDTO cardCreateDTO) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验模板是否存在
        CardTemplate template = cardTemplateMapper.selectById(cardCreateDTO.getTemplateId());
        if (template == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "模板不存在");
        }

        // 创建名片
        Card card = new Card();
        BeanUtils.copyProperties(cardCreateDTO, card);
        card.setUserId(currentUser.getId());
        card.setVisitCount(0);
        card.setLikeCount(0);
        card.setCommentCount(0);
        card.setIsDefault(0);
        card.setCreateTime(LocalDateTime.now());
        card.setUpdateTime(LocalDateTime.now());

        // 保存名片
        cardMapper.insert(card);

        // 生成名片二维码
        String qrcode = generateQrcode(card.getId());
        card.setQrcode(qrcode);
        cardMapper.updateById(card);

        // 如果是用户的第一张名片，设为默认名片
        Long count = cardMapper.selectCount(new LambdaQueryWrapper<Card>()
                .eq(Card::getUserId, currentUser.getId())
                .eq(Card::getDeleted, 0));

        if (count == 1) {
            card.setIsDefault(1);
            cardMapper.updateById(card);
        }

        // 添加积分（首次创建名片）
        if (count == 1) {
            userPointsService.addPoints(currentUser.getId(), 50, "首次创建名片");
        }

        return Result.success(card);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> updateCard(CardUpdateDTO cardUpdateDTO) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(cardUpdateDTO.getId());
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验名片是否属于当前用户
        if (!card.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此名片");
        }

        // 校验模板是否存在
        CardTemplate template = cardTemplateMapper.selectById(cardUpdateDTO.getTemplateId());
        if (template == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "模板不存在");
        }

        // 更新名片
        BeanUtils.copyProperties(cardUpdateDTO, card);
        card.setUpdateTime(LocalDateTime.now());

        // 保存名片
        cardMapper.updateById(card);

        return Result.success(card);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> deleteCard(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验名片是否属于当前用户
        if (!card.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此名片");
        }

        // 删除名片
        cardMapper.deleteById(id);

        // 如果删除的是默认名片，设置其他名片为默认名片
        if (card.getIsDefault() == 1) {
            Card otherCard = cardMapper.selectOne(new LambdaQueryWrapper<Card>()
                    .eq(Card::getUserId, currentUser.getId())
                    .eq(Card::getDeleted, 0)
                    .orderByDesc(Card::getCreateTime)
                    .last("LIMIT 1"));

            if (otherCard != null) {
                otherCard.setIsDefault(1);
                cardMapper.updateById(otherCard);
            }
        }

        return Result.success();
    }

    @Override
    public Result<Card> getCardDetail(Long id) {
        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        return Result.success(card);
    }

    @Override
    public Result<List<Card>> getMyCardList() {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 查询名片列表
        List<Card> cardList = cardMapper.selectList(new LambdaQueryWrapper<Card>()
                .eq(Card::getUserId, currentUser.getId())
                .eq(Card::getDeleted, 0)
                .orderByDesc(Card::getIsDefault)
                .orderByDesc(Card::getCreateTime));

        return Result.success(cardList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> setDefaultCard(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验名片是否属于当前用户
        if (!card.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此名片");
        }

        // 取消其他默认名片
        cardMapper.update(null, new LambdaUpdateWrapper<Card>()
                .eq(Card::getUserId, currentUser.getId())
                .eq(Card::getIsDefault, 1)
                .set(Card::getIsDefault, 0));

        // 设置当前名片为默认名片
        card.setIsDefault(1);
        cardMapper.updateById(card);

        return Result.success();
    }

    @Override
    public Result<?> getTemplateList() {
        // 查询模板列表
        List<CardTemplate> templateList = cardTemplateMapper.selectList(new LambdaQueryWrapper<CardTemplate>()
                .eq(CardTemplate::getStatus, 1)
                .eq(CardTemplate::getDeleted, 0)
                .orderByAsc(CardTemplate::getSort));

        return Result.success(templateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> visitCard(Long id) {
        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 获取当前用户（可能未登录）
        User currentUser = null;
        try {
            currentUser = userService.getCurrentUser();
        } catch (Exception e) {
            // 未登录用户，忽略异常
        }

        // 记录访问日志
        CardVisitLog visitLog = new CardVisitLog();
        visitLog.setCardId(id);
        visitLog.setCardUserId(card.getUserId());
        visitLog.setVisitorId(currentUser != null ? currentUser.getId() : null);
        visitLog.setVisitorIp(getIpAddress(request));
        visitLog.setVisitorDevice(request.getHeader("User-Agent"));
        visitLog.setSource(1); // 1-小程序
        visitLog.setStayTime(0);
        visitLog.setCreateTime(LocalDateTime.now());

        cardVisitLogMapper.insert(visitLog);

        // 更新名片访问次数
        card.setVisitCount(card.getVisitCount() + 1);
        cardMapper.updateById(card);

        // 如果是他人访问，给名片所有者增加积分
        if (currentUser != null && !currentUser.getId().equals(card.getUserId())) {
            userPointsService.addPoints(card.getUserId(), 1, "名片被访问");
        }

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> likeCard(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验是否已点赞
        CardLike existLike = cardLikeMapper.selectOne(new LambdaQueryWrapper<CardLike>()
                .eq(CardLike::getCardId, id)
                .eq(CardLike::getUserId, currentUser.getId())
                .eq(CardLike::getDeleted, 0)
                .last("LIMIT 1"));

        if (existLike != null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "已点赞");
        }

        // 添加点赞记录
        CardLike cardLike = new CardLike();
        cardLike.setCardId(id);
        cardLike.setCardUserId(card.getUserId());
        cardLike.setUserId(currentUser.getId());
        cardLike.setCreateTime(LocalDateTime.now());

        cardLikeMapper.insert(cardLike);

        // 更新名片点赞次数
        card.setLikeCount(card.getLikeCount() + 1);
        cardMapper.updateById(card);

        // 给点赞用户增加积分
        userPointsService.addPoints(currentUser.getId(), 1, "点赞名片");

        // 给名片所有者增加积分（如果不是自己点赞自己）
        if (!currentUser.getId().equals(card.getUserId())) {
            userPointsService.addPoints(card.getUserId(), 2, "名片被点赞");
        }

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> unlikeCard(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验是否已点赞
        CardLike existLike = cardLikeMapper.selectOne(new LambdaQueryWrapper<CardLike>()
                .eq(CardLike::getCardId, id)
                .eq(CardLike::getUserId, currentUser.getId())
                .eq(CardLike::getDeleted, 0)
                .last("LIMIT 1"));

        if (existLike == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "未点赞");
        }

        // 删除点赞记录
        cardLikeMapper.deleteById(existLike.getId());

        // 更新名片点赞次数
        if (card.getLikeCount() > 0) {
            card.setLikeCount(card.getLikeCount() - 1);
            cardMapper.updateById(card);
        }

        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> commentCard(Long id, String content) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验评论内容
        if (!StringUtils.hasText(content)) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "评论内容不能为空");
        }

        // 添加评论记录
        CardComment comment = new CardComment();
        comment.setCardId(id);
        comment.setCardUserId(card.getUserId());
        comment.setUserId(currentUser.getId());
        comment.setContent(content);
        comment.setStatus(1); // 默认通过审核
        comment.setCreateTime(LocalDateTime.now());
        comment.setUpdateTime(LocalDateTime.now());

        cardCommentMapper.insert(comment);

        // 更新名片评论次数
        card.setCommentCount(card.getCommentCount() + 1);
        cardMapper.updateById(card);

        // 给评论用户增加积分
        userPointsService.addPoints(currentUser.getId(), 2, "评论名片");

        // 给名片所有者增加积分（如果不是自己评论自己）
        if (!currentUser.getId().equals(card.getUserId())) {
            userPointsService.addPoints(card.getUserId(), 3, "名片被评论");
        }

        return Result.success(comment);
    }

    @Override
    public Result<?> getVisitLog(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null || card.getDeleted() == 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }

        // 校验是否有权限查看此名片的访问记录
        if (!card.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权查看此名片访问记录");
        }

        // 获取访问记录
        // 原代码: List<Map<String, Object>> visitLogList = cardVisitLogMapper.selectVisitLogWithUserInfo(id);
        // 使用 MyBatis-Plus 内置方法替代不存在的自定义方法
        List<CardVisitLog> visitLogs = cardVisitLogMapper.selectList(new LambdaQueryWrapper<CardVisitLog>()
                .eq(CardVisitLog::getCardId, id)
                .orderByDesc(CardVisitLog::getCreateTime));

        // 转换为包含用户信息的结果
        List<Map<String, Object>> visitLogList = new ArrayList<>();
        for (CardVisitLog log : visitLogs) {
            Map<String, Object> logInfo = new HashMap<>();
            logInfo.put("id", log.getId());
            logInfo.put("cardId", log.getCardId());
            logInfo.put("visitorId", log.getVisitorId());
            logInfo.put("visitorIp", log.getVisitorIp());
            logInfo.put("visitorDevice", log.getVisitorDevice());
            logInfo.put("createTime", log.getCreateTime());

            // 如果有访问者ID，获取访问者信息
            if (log.getVisitorId() != null) {
                User visitor = userService.getById(log.getVisitorId());
                if (visitor != null) {
                    logInfo.put("visitorName", visitor.getNickname());
                    logInfo.put("visitorAvatar", visitor.getAvatar());
                }
            } else {
                logInfo.put("visitorName", "匿名访客");
                logInfo.put("visitorAvatar", "");
            }

            visitLogList.add(logInfo);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("visitLog", visitLogList);

        return Result.success(result);
    }

    /**
     * 生成名片二维码
     *
     * @param cardId 名片ID
     * @return 二维码URL
     */
    private String generateQrcode(Long cardId) {
        try {
            String fileName = "card_" + cardId + ".png";
            String path = FileUtil.getTmpDirPath() + fileName;
            String content = "https://example.com/cards/" + cardId;
            QrCodeUtil.generate(content, 300, 300, FileUtil.file(path));
            return "/qrcode/" + fileName;
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            return "";
        }
    }

    /**
     * 获取请求IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    @Override
    public Result<String> getShareLink(Long id) {
        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }
        
        // 获取当前用户
        User currentUser = userService.getCurrentUser();
        
        // 校验名片是否属于当前用户
        if (!card.getUserId().equals(currentUser.getId())) {
            return Result.failed(ResultCode.FORBIDDEN.getCode(), "无权操作此名片");
        }
        
        // 生成分享链接
        String shareLink = "https://meh-businesscard.com/share/card/" + id;
        
        return Result.success(shareLink);
    }
    
    @Override
    public Result<List<CardComment>> getCardComments(Long id) {
        // 校验名片是否存在
        Card card = cardMapper.selectById(id);
        if (card == null) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "名片不存在");
        }
        
        // 查询评论列表
        List<CardComment> commentList = cardCommentMapper.selectList(new LambdaQueryWrapper<CardComment>()
                .eq(CardComment::getCardId, id)
                .eq(CardComment::getStatus, 1) // 只查询已审核通过的评论
                .eq(CardComment::getDeleted, 0)
                .orderByDesc(CardComment::getCreateTime));
        
        return Result.success(commentList);
    }
}
