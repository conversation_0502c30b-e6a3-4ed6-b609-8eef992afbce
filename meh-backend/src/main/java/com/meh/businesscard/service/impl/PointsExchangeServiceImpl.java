package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.exception.BusinessException;
import com.meh.businesscard.entity.PointsExchange;
import com.meh.businesscard.entity.PointsGoods;
import com.meh.businesscard.mapper.PointsExchangeMapper;
import com.meh.businesscard.mapper.PointsGoodsMapper;
import com.meh.businesscard.service.PointsExchangeService;
import com.meh.businesscard.service.UserPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 积分兑换服务实现
 */
@Service
public class PointsExchangeServiceImpl extends ServiceImpl<PointsExchangeMapper, PointsExchange> implements PointsExchangeService {

    @Autowired
    private PointsExchangeMapper pointsExchangeMapper;
    @Autowired
    private PointsGoodsMapper pointsGoodsMapper;
    @Autowired
    private UserPointsService userPointsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PointsExchange exchange(Long userId, Long goodsId, Integer quantity) {
        if (quantity == null || quantity <= 0) {
            quantity = 1;
        }
        PointsGoods goods = pointsGoodsMapper.selectById(goodsId);
        if (goods == null || goods.getStatus() == 0 || goods.getDeleted() == 1) {
            throw new BusinessException("商品不存在");
        }
        int totalPoints = goods.getPoints() * quantity;
        userPointsService.reducePoints(userId, totalPoints, 7, "积分兑换");

        PointsExchange exchange = new PointsExchange();
        exchange.setUserId(userId);
        exchange.setGoodsId(goodsId);
        exchange.setGoodsName(goods.getName());
        exchange.setGoodsImage(goods.getImage());
        exchange.setQuantity(quantity);
        exchange.setPoints(totalPoints);
        exchange.setStatus(0);
        exchange.setCreateTime(LocalDateTime.now());
        exchange.setUpdateTime(LocalDateTime.now());
        pointsExchangeMapper.insert(exchange);

        // 更新商品兑换数量
        goods.setExchangeCount(goods.getExchangeCount() + quantity);
        pointsGoodsMapper.updateById(goods);
        return exchange;
    }

    @Override
    public PointsExchange getById(Long id) {
        return pointsExchangeMapper.selectById(id);
    }
}
