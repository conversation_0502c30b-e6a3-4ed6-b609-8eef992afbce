package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.common.api.ResultCode;
import com.meh.businesscard.entity.Activity;
import com.meh.businesscard.entity.Card;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.ActivityMapper;
import com.meh.businesscard.mapper.CardMapper;
import com.meh.businesscard.service.ActivityService;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.service.UserPointsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动服务实现
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements ActivityService {

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private CardMapper cardMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    @Override
    public Result<List<Activity>> getActivityList(Integer type, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<Activity> queryWrapper = new LambdaQueryWrapper<Activity>()
                .eq(Activity::getDeleted, 0);

        // 添加类型条件
        if (type != null) {
            queryWrapper.eq(Activity::getType, type);
        }

        // 添加状态条件
        if (status != null) {
            queryWrapper.eq(Activity::getStatus, status);
        } else {
            // 默认只查询进行中的活动
            queryWrapper.eq(Activity::getStatus, 1);
        }

        // 按开始时间倒序排列
        queryWrapper.orderByDesc(Activity::getStartTime);

        List<Activity> activityList = activityMapper.selectList(queryWrapper);
        return Result.success(activityList);
    }

    @Override
    public Result<Activity> getActivityDetail(Long id) {
        // 校验活动是否存在
        Activity activity = activityMapper.selectById(id);
        if (activity == null || activity.getDeleted() == 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动不存在");
        }

        return Result.success(activity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> joinActivity(Long id) {
        // 获取当前用户
        User currentUser = userService.getCurrentUser();

        // 校验活动是否存在
        Activity activity = activityMapper.selectById(id);
        if (activity == null || activity.getDeleted() == 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动不存在");
        }

        // 校验活动状态
        if (activity.getStatus() != 1) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动未开始或已结束");
        }

        // 校验活动时间
        LocalDateTime now = LocalDateTime.now();
        if (activity.getStartTime() != null && now.isBefore(activity.getStartTime())) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动尚未开始");
        }
        if (activity.getEndTime() != null && now.isAfter(activity.getEndTime())) {
            return Result.failed(ResultCode.VALIDATE_FAILED.getCode(), "活动已结束");
        }

        // 根据活动类型执行不同的参与逻辑
        switch (activity.getType()) {
            case 1: // 裂变营销
                return handleMarketingActivity(currentUser, activity);
            case 2: // 签到活动
                return handleCheckinActivity(currentUser, activity);
            default:
                return handleOtherActivity(currentUser, activity);
        }
    }

    @Override
    public Result<?> getRecommendCards() {
        // 获取推荐名片列表
        // 这里可以根据访问量、点赞数等指标进行推荐
        List<Card> recommendCards = cardMapper.selectList(new LambdaQueryWrapper<Card>()
                .eq(Card::getDeleted, 0)
                .orderByDesc(Card::getVisitCount)
                .orderByDesc(Card::getLikeCount)
                .last("LIMIT 10"));

        return Result.success(recommendCards);
    }

    /**
     * 处理裂变营销活动
     *
     * @param user 用户
     * @param activity 活动
     * @return 处理结果
     */
    private Result<?> handleMarketingActivity(User user, Activity activity) {
        // 裂变营销活动逻辑
        // 用户参与后可以获得邀请码，邀请好友注册可获得奖励
        
        // 给用户增加参与活动的积分
        userPointsService.addPoints(user.getId(), 10, 6, "参与裂变营销活动");

        Map<String, Object> result = new HashMap<>();
        result.put("message", "参与成功！邀请好友注册可获得更多奖励");
        result.put("points", 10);
        
        return Result.success(result);
    }

    /**
     * 处理签到活动
     *
     * @param user 用户
     * @param activity 活动
     * @return 处理结果
     */
    private Result<?> handleCheckinActivity(User user, Activity activity) {
        // 签到活动逻辑
        // 用户每日签到可获得积分奖励
        
        return userPointsService.dailyCheckin(user.getId());
    }

    /**
     * 处理其他活动
     *
     * @param user 用户
     * @param activity 活动
     * @return 处理结果
     */
    private Result<?> handleOtherActivity(User user, Activity activity) {
        // 其他活动逻辑
        // 给用户增加参与活动的积分
        userPointsService.addPoints(user.getId(), 5, 6, "参与活动：" + activity.getName());

        Map<String, Object> result = new HashMap<>();
        result.put("message", "参与成功！");
        result.put("points", 5);
        
        return Result.success(result);
    }
}
