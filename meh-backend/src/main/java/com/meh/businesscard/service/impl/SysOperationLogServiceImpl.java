package com.meh.businesscard.service.impl;

import com.meh.businesscard.entity.SysOperationLog;
import com.meh.businesscard.mapper.SysOperationLogMapper;
import com.meh.businesscard.service.SysOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 系统操作日志服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class SysOperationLogServiceImpl implements SysOperationLogService {

    @Autowired
    private SysOperationLogMapper sysOperationLogMapper;

    @Override
    public boolean saveLog(SysOperationLog operationLog) {
        try {
            // 设置创建时间
            operationLog.setCreateTime(LocalDateTime.now());

            // 保存日志
            return sysOperationLogMapper.insert(operationLog) > 0;
        } catch (Exception e) {
            log.error("保存操作日志失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Async
    @Override
    public void saveLogAsync(SysOperationLog operationLog) {
        saveLog(operationLog);
    }
}
