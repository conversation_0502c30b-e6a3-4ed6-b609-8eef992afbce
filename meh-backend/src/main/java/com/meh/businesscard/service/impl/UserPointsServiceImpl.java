package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.common.exception.BusinessException;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.entity.UserPointsLog;
import com.meh.businesscard.mapper.UserPointsLogMapper;
import com.meh.businesscard.mapper.UserPointsMapper;
import com.meh.businesscard.service.UserPointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户积分服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Slf4j
@Service
public class UserPointsServiceImpl extends ServiceImpl<UserPointsMapper, UserPoints> implements UserPointsService {
    
    @Autowired
    private UserPointsMapper userPointsMapper;
    
    @Autowired
    private UserPointsLogMapper userPointsLogMapper;
    
    @Override
    public UserPoints getUserPoints(Long userId) {
        // 查询用户积分信息
        UserPoints userPoints = userPointsMapper.selectOne(new LambdaQueryWrapper<UserPoints>()
                .eq(UserPoints::getUserId, userId)
                .eq(UserPoints::getDeleted, 0)
                .last("LIMIT 1"));
        
        // 如果不存在，初始化用户积分
        if (userPoints == null) {
            userPoints = new UserPoints();
            userPoints.setUserId(userId);
            userPoints.setPoints(0);
            userPoints.setTotalPoints(0);
            userPoints.setUsedPoints(0);
            userPoints.setContinuousLoginDays(0);
            userPoints.setCreateTime(LocalDateTime.now());
            userPoints.setUpdateTime(LocalDateTime.now());
            
            userPointsMapper.insert(userPoints);
        }
        
        return userPoints;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoints(Long userId, Integer points, String description) {
        // 默认来源为社交互动
        return addPoints(userId, points, 4, description);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoints(Long userId, Integer points, Integer source, String description) {
        // 校验参数
        if (userId == null || points == null || points <= 0) {
            return false;
        }
        
        // 获取用户积分信息
        UserPoints userPoints = getUserPoints(userId);
        
        // 记录变动前积分
        Integer beforePoints = userPoints.getPoints();
        
        // 更新积分
        userPoints.setPoints(userPoints.getPoints() + points);
        userPoints.setTotalPoints(userPoints.getTotalPoints() + points);
        userPoints.setUpdateTime(LocalDateTime.now());
        
        // 保存用户积分
        userPointsMapper.updateById(userPoints);
        
        // 记录积分变动日志
        UserPointsLog pointsLog = new UserPointsLog();
        pointsLog.setUserId(userId);
        pointsLog.setType(1); // 1-增加
        pointsLog.setPoints(points);
        pointsLog.setBeforePoints(beforePoints);
        pointsLog.setAfterPoints(userPoints.getPoints());
        pointsLog.setSource(source);
        pointsLog.setDescription(description);
        pointsLog.setCreateTime(LocalDateTime.now());
        
        userPointsLogMapper.insert(pointsLog);
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reducePoints(Long userId, Integer points, String description) {
        // 默认来源为积分兑换
        return reducePoints(userId, points, 7, description);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reducePoints(Long userId, Integer points, Integer source, String description) {
        // 校验参数
        if (userId == null || points == null || points <= 0) {
            return false;
        }
        
        // 获取用户积分信息
        UserPoints userPoints = getUserPoints(userId);
        
        // 校验积分是否足够
        if (userPoints.getPoints() < points) {
            throw new BusinessException("积分不足");
        }
        
        // 记录变动前积分
        Integer beforePoints = userPoints.getPoints();
        
        // 更新积分
        userPoints.setPoints(userPoints.getPoints() - points);
        userPoints.setUsedPoints(userPoints.getUsedPoints() + points);
        userPoints.setUpdateTime(LocalDateTime.now());
        
        // 保存用户积分
        userPointsMapper.updateById(userPoints);
        
        // 记录积分变动日志
        UserPointsLog pointsLog = new UserPointsLog();
        pointsLog.setUserId(userId);
        pointsLog.setType(2); // 2-减少
        pointsLog.setPoints(points);
        pointsLog.setBeforePoints(beforePoints);
        pointsLog.setAfterPoints(userPoints.getPoints());
        pointsLog.setSource(source);
        pointsLog.setDescription(description);
        pointsLog.setCreateTime(LocalDateTime.now());
        
        userPointsLogMapper.insert(pointsLog);
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean operatePoints(Long userId, Integer points, Long operatorId, String operatorName, String description) {
        // 校验参数
        if (userId == null || points == null || points == 0) {
            return false;
        }
        
        // 获取用户积分信息
        UserPoints userPoints = getUserPoints(userId);
        
        // 记录变动前积分
        Integer beforePoints = userPoints.getPoints();
        
        // 更新积分
        if (points > 0) {
            // 增加积分
            userPoints.setPoints(userPoints.getPoints() + points);
            userPoints.setTotalPoints(userPoints.getTotalPoints() + points);
        } else {
            // 减少积分
            int absPoints = Math.abs(points);
            // 校验积分是否足够
            if (userPoints.getPoints() < absPoints) {
                throw new BusinessException("积分不足");
            }
            userPoints.setPoints(userPoints.getPoints() - absPoints);
            userPoints.setUsedPoints(userPoints.getUsedPoints() + absPoints);
        }
        userPoints.setUpdateTime(LocalDateTime.now());
        
        // 保存用户积分
        userPointsMapper.updateById(userPoints);
        
        // 记录积分变动日志
        UserPointsLog pointsLog = new UserPointsLog();
        pointsLog.setUserId(userId);
        pointsLog.setType(points > 0 ? 1 : 2); // 1-增加，2-减少
        pointsLog.setPoints(Math.abs(points));
        pointsLog.setBeforePoints(beforePoints);
        pointsLog.setAfterPoints(userPoints.getPoints());
        pointsLog.setSource(8); // 8-管理员操作
        pointsLog.setDescription(description);
        pointsLog.setOperatorId(operatorId);
        pointsLog.setOperatorName(operatorName);
        pointsLog.setCreateTime(LocalDateTime.now());
        
        userPointsLogMapper.insert(pointsLog);
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean dailySignIn(Long userId) {
        // 获取用户积分信息
        UserPoints userPoints = getUserPoints(userId);
        
        // 获取当前日期
        LocalDate today = LocalDate.now();
        
        // 获取上次签到日期
        LocalDate lastSignDate = userPoints.getLastSignDate() != null ? 
                userPoints.getLastSignDate().toLocalDate() : null;
        
        // 判断今天是否已签到
        if (lastSignDate != null && lastSignDate.equals(today)) {
            throw new BusinessException("今天已签到");
        }
        
        // 判断是否连续签到
        boolean isContinuous = false;
        if (lastSignDate != null) {
            long daysBetween = ChronoUnit.DAYS.between(lastSignDate, today);
            isContinuous = daysBetween == 1;
        }
        
        // 更新连续签到天数
        if (isContinuous) {
            userPoints.setContinuousLoginDays(userPoints.getContinuousLoginDays() + 1);
        } else {
            userPoints.setContinuousLoginDays(1);
        }
        
        // 计算签到积分
        int signPoints = calculateSignInPoints(userPoints.getContinuousLoginDays());
        
        // 更新最后签到日期
        userPoints.setLastSignDate(LocalDateTime.now());
        userPointsMapper.updateById(userPoints);
        
        // 增加积分
        addPoints(userId, signPoints, 2, "每日签到");
        
        return true;
    }
    
    @Override
    public Object getPointsLog(Long userId, Integer page, Integer size) {
        // 分页查询积分变动记录
        IPage<UserPointsLog> pageResult = new Page<>(page, size);
        IPage<UserPointsLog> pointsLogPage = userPointsLogMapper.selectPage(pageResult, new LambdaQueryWrapper<UserPointsLog>()
                .eq(UserPointsLog::getUserId, userId)
                .eq(UserPointsLog::getDeleted, 0)
                .orderByDesc(UserPointsLog::getCreateTime));
        
        // 查询用户积分信息
        UserPoints userPoints = getUserPoints(userId);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", pointsLogPage.getTotal());
        result.put("pages", pointsLogPage.getPages());
        result.put("current", pointsLogPage.getCurrent());
        result.put("size", pointsLogPage.getSize());
        result.put("records", pointsLogPage.getRecords());
        result.put("userPoints", userPoints);
        
        return result;
    }
    
    /**
     * 计算签到积分
     * 
     * @param continuousDays 连续签到天数
     * @return 签到积分
     */
    private int calculateSignInPoints(int continuousDays) {
        // 基础积分
        int basePoints = 5;
        
        // 连续签到奖励
        if (continuousDays >= 30) {
            // 连续签到30天及以上
            return basePoints + 15;
        } else if (continuousDays >= 15) {
            // 连续签到15天及以上
            return basePoints + 10;
        } else if (continuousDays >= 7) {
            // 连续签到7天及以上
            return basePoints + 5;
        } else if (continuousDays >= 3) {
            // 连续签到3天及以上
            return basePoints + 2;
        } else {
            // 连续签到不足3天
            return basePoints;
        }
    }
}
