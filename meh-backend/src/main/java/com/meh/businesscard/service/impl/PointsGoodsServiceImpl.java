package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meh.businesscard.entity.PointsGoods;
import com.meh.businesscard.mapper.PointsGoodsMapper;
import com.meh.businesscard.service.PointsGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 积分商品服务实现
 */
@Service
public class PointsGoodsServiceImpl extends ServiceImpl<PointsGoodsMapper, PointsGoods> implements PointsGoodsService {

    @Autowired
    private PointsGoodsMapper pointsGoodsMapper;

    @Override
    public List<PointsGoods> listGoods(Long categoryId) {
        LambdaQueryWrapper<PointsGoods> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PointsGoods::getStatus, 1).eq(PointsGoods::getDeleted, 0);
        if (categoryId != null) {
            wrapper.eq(PointsGoods::getCategoryId, categoryId);
        }
        wrapper.orderByAsc(PointsGoods::getSort).orderByDesc(PointsGoods::getCreateTime);
        return pointsGoodsMapper.selectList(wrapper);
    }
}
