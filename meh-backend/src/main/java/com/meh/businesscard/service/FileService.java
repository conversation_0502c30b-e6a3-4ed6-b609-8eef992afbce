package com.meh.businesscard.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface FileService {

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 图片URL
     */
    String uploadImage(MultipartFile file) throws Exception;

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 头像URL
     */
    String uploadAvatar(MultipartFile file) throws Exception;

    /**
     * 上传名片背景
     *
     * @param file 背景图片文件
     * @return 背景图片URL
     */
    String uploadCardBackground(MultipartFile file) throws Exception;

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl) throws Exception;

    /**
     * 获取文件信息
     *
     * @param fileUrl 文件URL
     * @return 文件信息
     */
    Map<String, Object> getFileInfo(String fileUrl) throws Exception;
}
